# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-05-16 08:09:54 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   Maintain Project Context Across Sessions and Memory Resets for Consistent AI-Assisted Development

## Key Features

*   Chat History Persistence within a single VS Code workspace session
*   Workspace Awareness (open files, project structure)
*   Persistence Across Roo's Memory Resets via Memory Bank
*   Building Long-Term Project Knowledge via Memory Bank
*   Establishing an Explicit Mode-Based Workflow via Memory Bank
*   Implementing Project-Specific Rules with `.clinerules` via Memory Bank

## Overall Architecture

*   Memory Bank system acts as external, reliable long-term memory for Roo Code, complementing built-in short-term context features.