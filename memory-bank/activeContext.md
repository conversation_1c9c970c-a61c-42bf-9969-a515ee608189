# Active Context

This file tracks the project's current status, including recent changes, current goals, and open questions.
2025-05-16 08:10:03 - Log of updates made.

*

## Current Focus

*   Stable, consistent export of all analysis results and technical indicator span files to both web2/public/analysis and web_test/analysis for all stock analysis runs. All indicator method calls and output handling are now unified and robust.

## Recent Changes

*   [2024-05-16] Updated codebase to export all technical indicator span files (e.g., HPG_ma.json, HPG_macd.json, etc.) to both web2/public/analysis and web_test/analysis for every symbol and indicator.
*   [2024-05-16] Updated codebase to export all analysis JSON results to both web2/public/analysis and web_test/analysis for improved test/development workflow.
*   [2024-05-16] Implemented static web page in web_test to visualize analysis results using Chart.js, reading from exported chart-data.js.
*   Created memory-bank directory and productContext.md.
*   [2024-06-09 15:00:00] - Refactored server/stock_analyzer.py to move all import statements to the top of the file, ensuring no imports remain inside methods. This enforces the rule: never import types or modules in the middle of a method, always import at the beginning of the file. Duplicate imports were also removed.
*   [2024-06-09 15:10:00] - Fixed TypeError in calculate_indicators (analysis_utils.py): now only the latest float value for each MA period is passed to generate_ma_signals_table, preventing '>' not supported between int and list errors in MA signal generation.
*   [2024-05-19 07:01:00] - Major indicator pipeline refactor: unified all indicator method calls (get_signals, get_price_crossover_signals, etc.), fixed all AttributeErrors, and ensured correct output mapping for each indicator. Fixed ichimoku and ADX export bugs. End-to-end run now completes successfully with all indicator JSONs generated.

## Open Questions/Issues

*   What is the next task?