# Decision Log

This file records architectural and implementation decisions using a list format.
2025-05-16 08:10:14 - Log of updates made.

*

## Decision

* [2024-05-16] Export all technical indicator span files (e.g., HPG_ma.json, HPG_macd.json, etc.) to both web2/public/analysis and web_test/analysis for every symbol and indicator.
* [2024-05-16] Export all analysis results (JSON files) to both web2/public/analysis and web_test/analysis.
* [2024-05-16 10:00:00] - Decided to use Chart.js for static web charting of analysis results in web_test, reading from exported JS data (chart-data.js) for maintainability and ease of integration.

## Rationale

* Enable parallel development and testing environments without manual copying.
* Ensure all technical indicator data is available in both production-like and test environments for advanced analytics and charting.
* Ensure analysis results are always available in both production-like and test environments.

## Implementation Details

* Updated save_indicator_span in stock_analyzer.py to write each indicator JSON file to both locations using Utils.get_web2_dir() and Utils.get_web_test_dir().
* Both locations are created if missing, and actions are logged for traceability.
* Updated main analysis output logic in stock_analyzer.py to write each result to both locations using Utils.get_web2_dir() and Utils.get_web_test_dir().
* Both locations are created if missing, and actions are logged for traceability.

*