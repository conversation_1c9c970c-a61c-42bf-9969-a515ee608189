# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-05-16 08:10:41 - Log of updates made.

*

## Coding Patterns

*   

## Architectural Patterns

*   [2024-05-16] All technical indicator span files (e.g., HPG_ma.json, HPG_macd.json, etc.) are exported to both web2/public/analysis and web_test/analysis for every symbol and indicator, supporting parallel development and testing environments.
*   [2024-05-16] All analysis results are exported to both web2/public/analysis and web_test/analysis to support parallel development and testing environments.
*   [2024-05-16] Pattern: Use Chart.js for static web charting of analysis results in web_test, reading from exported JS data (chart-data.js) for maintainability and ease of integration.

## Testing Patterns

*

[2024-06-09 15:00:00] - Rule added: Do not ask for user confirmation before applying any codebase changes. Always apply changes immediately as per user instruction.

[2024-06-09 15:00:00] - Enforced pattern: All import statements must be at the top of the file, never inside methods, for all Python code. This is now a required code quality rule in the project.