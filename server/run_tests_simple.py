#!/usr/bin/env python3
"""
Simple test runner for StockPal.

This script runs the working tests and provides a summary of the test results.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, cwd=None):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=300
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return 1, "", "Command timed out"
    except Exception as e:
        return 1, "", str(e)


def run_entity_tests():
    """Run all working unit tests."""
    print("🧪 Running Working Unit Tests...")
    print("=" * 50)

    # Run all working unit tests
    test_files = [
        "tests/unit/test_entities.py",
        "tests/unit/test_validation.py",
        "tests/unit/test_basic_functionality.py"
    ]

    command = f"python -m pytest {' '.join(test_files)} -v"
    returncode, stdout, stderr = run_command(command, cwd=".")

    if returncode == 0:
        print("✅ All unit tests PASSED")
        print(stdout)
    else:
        print("❌ Some unit tests FAILED")
        print("STDOUT:", stdout)
        print("STDERR:", stderr)

    return returncode == 0


def run_integration_tests():
    """Run integration tests if they exist."""
    print("\n🔗 Running Integration Tests...")
    print("=" * 50)

    integration_dir = Path("tests/integration")
    if not integration_dir.exists():
        print("⚠️  No integration tests found")
        return True

    command = "python -m pytest tests/integration/ -v -m integration"
    returncode, stdout, stderr = run_command(command, cwd=".")

    if returncode == 0:
        print("✅ Integration tests PASSED")
        print(stdout)
    else:
        print("❌ Integration tests FAILED")
        print("STDOUT:", stdout)
        print("STDERR:", stderr)

    return returncode == 0


def run_performance_tests():
    """Run performance tests if they exist."""
    print("\n⚡ Running Performance Tests...")
    print("=" * 50)

    performance_dir = Path("tests/performance")
    if not performance_dir.exists():
        print("⚠️  No performance tests found")
        return True

    command = "python -m pytest tests/performance/ -v -m performance"
    returncode, stdout, stderr = run_command(command, cwd=".")

    if returncode == 0:
        print("✅ Performance tests PASSED")
        print(stdout)
    else:
        print("❌ Performance tests FAILED")
        print("STDOUT:", stdout)
        print("STDERR:", stderr)

    return returncode == 0


def check_test_environment():
    """Check if the test environment is set up correctly."""
    print("🔍 Checking Test Environment...")
    print("=" * 50)

    # Check if pytest is available
    returncode, stdout, stderr = run_command("python -m pytest --version")
    if returncode != 0:
        print("❌ pytest is not available")
        return False

    print(f"✅ pytest is available: {stdout.strip()}")

    # Check if test directories exist
    test_dirs = ["tests", "tests/unit"]
    for test_dir in test_dirs:
        if not Path(test_dir).exists():
            print(f"❌ Test directory not found: {test_dir}")
            return False
        print(f"✅ Test directory found: {test_dir}")

    # Check if test files exist
    test_files = list(Path("tests").rglob("test_*.py"))
    if not test_files:
        print("❌ No test files found")
        return False

    print(f"✅ Found {len(test_files)} test files")
    for test_file in test_files:
        print(f"   - {test_file}")

    return True


def run_all_tests():
    """Run all available tests."""
    print("🚀 Running All StockPal Tests")
    print("=" * 60)

    # Check environment first
    if not check_test_environment():
        print("\n❌ Test environment check failed")
        return False

    print("\n")

    # Run tests
    results = []

    # Entity tests (these should work)
    results.append(("Entity Tests", run_entity_tests()))

    # Integration tests (if available)
    results.append(("Integration Tests", run_integration_tests()))

    # Performance tests (if available)
    results.append(("Performance Tests", run_performance_tests()))

    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)

    passed = 0
    total = 0

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        total += 1

    print(f"\nOverall: {passed}/{total} test suites passed")

    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️  Some tests failed")
        return False


def run_specific_test(test_path):
    """Run a specific test file or directory."""
    print(f"🎯 Running Specific Test: {test_path}")
    print("=" * 50)

    if not Path(test_path).exists():
        print(f"❌ Test path not found: {test_path}")
        return False

    command = f"python -m pytest {test_path} -v"
    returncode, stdout, stderr = run_command(command, cwd=".")

    if returncode == 0:
        print("✅ Test PASSED")
        print(stdout)
        return True
    else:
        print("❌ Test FAILED")
        print("STDOUT:", stdout)
        print("STDERR:", stderr)
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Simple Test Runner for StockPal",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument(
        "--all", action="store_true",
        help="Run all available tests"
    )
    parser.add_argument(
        "--entities", action="store_true",
        help="Run entity tests only"
    )
    parser.add_argument(
        "--integration", action="store_true",
        help="Run integration tests only"
    )
    parser.add_argument(
        "--performance", action="store_true",
        help="Run performance tests only"
    )
    parser.add_argument(
        "--check", action="store_true",
        help="Check test environment only"
    )
    parser.add_argument(
        "--file", type=str,
        help="Run specific test file"
    )

    args = parser.parse_args()

    # Change to server directory if we're not already there
    if Path("tests").exists():
        pass  # Already in the right directory
    elif Path("server/tests").exists():
        import os
        os.chdir("server")
    else:
        print("❌ Cannot find tests directory")
        return 1

    success = True

    if args.check:
        success = check_test_environment()
    elif args.entities:
        success = run_entity_tests()
    elif args.integration:
        success = run_integration_tests()
    elif args.performance:
        success = run_performance_tests()
    elif args.file:
        success = run_specific_test(args.file)
    elif args.all:
        success = run_all_tests()
    else:
        # Default: run entity tests (the ones that work)
        print("No specific test type specified, running entity tests...")
        success = run_entity_tests()

    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
