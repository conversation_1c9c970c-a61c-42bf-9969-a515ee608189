{
  "s": "HPG", // symbol
  "ad": "2025-05-14", // analysis date
  "ltd": "2025-05-08", // last trading date
  "p": { // price
    "c": "25,700", // close price
    "cv": "300", // change value
    "cp": "1.18" // change percent
  },
  "t": { // trend
    "d": "tăng", // trend direction
    "s": "yếu", // trend strength
    "c": "64.10%" // trend confidence
  },
  "mc": "ranging", // market condition
  "r": { // recommendation
    "r": "Tích lũy dần", // recommendation
    "s": "=== PHÂN TÍCH KỸ THUẬT HPG (2025-05-15) ===\n• Khung thời gian: Phân tích hàng ngày (Daily)\n• Giá hiện tại: 26,150 (+0.00%)\n• Giá đang nằm trên cả SMA5 (25,830) và SMA20 (25,585)\n• <PERSON> hướng tăng ngắn hạn đang chiếm ưu thế\n• SMA5 > SMA20: <PERSON>ín hiệu tăng giá mạnh"
  },
  "bz": [ // buy zones
    {
      "p": "25,383", // price
      "c": "cao", // confidence
      "r": "Điểm hỗ trợ S1" // reason
    },
    {
      "p": "24,912",
      "c": "trung bình",
      "r": "Vùng hỗ trợ"
    },
    {
      "p": "24,500",
      "c": "cao",
      "r": "Hỗ trợ Kijun-sen của Ichimoku"
    },
    {
      "p": "22,479",
      "c": "trung bình",
      "r": "Vùng hỗ trợ"
    }
  ],
  "slz": [ // stop loss zones
    {
      "p": "22,479", // price
      "c": "cao", // confidence
      "r": "Algorithmic stop loss" // reason
    }
  ],
  "tpz": [ // take profit zones
    {
      "p": "25,933", // price
      "c": "cao", // confidence
      "r": "Điểm kháng cự R1" // reason
    },
    {
      "p": "26,452", // price
      "c": "cao", // confidence
      "r": "Algorithmic target price" // reason
    },
    {
      "p": "27,346", // price
      "c": "trung bình", // confidence
      "r": "Vùng kháng cự" // reason
    }
  ],
  "rr": [ // risk reward ratios
    {
      "bp": "24,912", // buy price
      "slp": "24,500", // stop loss price
      "tp": "27,346", // take profit price
      "r": "5.90", // ratio
      "q": "Tuyệt vời" // quality
    },
    {
      "bp": "25,700", // buy price
      "slp": "25,383", // stop loss price
      "tp": "27,346", // take profit price
      "r": "5.20", // ratio
      "q": "Tuyệt vời" // quality
    },
    {
      "bp": "25,383", // buy price
      "slp": "24,912", // stop loss price
      "tp": "27,346", // take profit price
      "r": "4.17", // ratio
      "q": "Tuyệt vời" // quality
    }
  ],
  "ma": [ // ma signals
    {
      "p": 5, // period
      "t": "SMA", // type
      "v": "25,580", // value
      "s": "Mua" // signal
    },
    {
      "p": 5,
      "t": "EMA",
      "v": "25,565",
      "s": "Mua"
    },
    {
      "p": 10,
      "t": "SMA",
      "v": "25,475",
      "s": "Mua"
    },
    {
      "p": 10,
      "t": "EMA",
      "v": "25,490",
      "s": "Mua"
    },
    {
      "p": 20,
      "t": "SMA",
      "v": "24,912",
      "s": "Mua"
    },
    {
      "p": 20,
      "t": "EMA",
      "v": "25,526",
      "s": "Mua"
    },
    {
      "p": 50,
      "t": "SMA",
      "v": "26,452",
      "s": "Bán"
    },
    {
      "p": 50,
      "t": "EMA",
      "v": "25,981",
      "s": "Bán"
    },
    {
      "p": 100,
      "t": "SMA",
      "v": "26,540",
      "s": "Bán"
    },
    {
      "p": 100,
      "t": "EMA",
      "v": "26,304",
      "s": "Bán"
    },
    {
      "p": 200,
      "t": "SMA",
      "v": "26,477",
      "s": "Bán"
    },
    {
      "p": 200,
      "t": "EMA",
      "v": "26,529",
      "s": "Bán"
    }
  ],
  "ti": [ // technical indicators
    {
      "n": "RS(52W)", // name
      "v": null, // value
      "s": "Trung tính", // signal
      "a": "Theo dõi thêm", // action
      "d": "Relative Strength (52 tuần - 1 năm)", // description
      "i": "Chỉ báo sức mạnh tương đối so với thị trường (VNINDEX). Giá trị > 1 cho thấy cổ phiếu mạnh hơn thị trường, giá trị < 1 cho thấy yếu hơn. RS(52W) so sánh hiệu suất trong 52 tuần (1 năm), giúp xác định sức mạnh dài hạn của cổ phiếu trong ngành." // info
    },
    {
      "n": "RSI", // name
      "v": 50.59, // value
      "s": "Trung tính", // signal
      "a": "RSI > 50, xu hướng tăng nhẹ, có thể giữ vị thế mua", // action
      "d": "Chỉ số sức mạnh tương đối (14 ngày)", // description
      "i": "RSI đo lường tốc độ và sự thay đổi của biến động giá. Giá trị dưới 30 thường được xem là quá bán (tín hiệu mua), trên 70 là quá mua (tín hiệu bán). Mức 50 là ngưỡng phân cách xu hướng lên/xuống." // info
    },
    {
      "n": "MACD", // name
      "v": -151.43, // value
      "s": "Mua", // signal
      "a": "Tín hiệu mua đang mạnh lên, xem xét tăng vị thế mua", // action
      "d": "Moving Average Convergence Divergence (12,26,9)", // description
      "i": "MACD là sự khác biệt giữa EMA 12 và EMA 26, với đường tín hiệu là EMA 9 của MACD. Khi MACD cắt lên trên đường tín hiệu là dấu hiệu mua, cắt xuống dưới là dấu hiệu bán. Mức 0 là ngưỡng đảo chiều quan trọng." // info
    },
    {
      "n": "MACD Histogram", // name
      "v": 135.83, // value
      "s": "Mua", // signal
      "a": "Động lượng tăng đang mạnh lên, cơ hội mua", // action
      "d": "MACD Histogram", // description
      "i": "MACD Histogram là sự chênh lệch giữa đường MACD và đường tín hiệu. Giá trị dương và tăng chỉ báo động lượng tăng mạnh, giá trị âm và giảm chỉ báo động lượng giảm mạnh. Khi histogram thay đổi chiều là dấu hiệu đầu tiên của đảo chiều." // info
    },
    {
      "n": "STOCHRSI_fastk", // name
      "v": 56.94, // value
      "s": "Trung tính", // signal
      "a": "StochRSI > 50, động lượng tăng nhẹ, có thể giữ vị thế", // action
      "d": "Stochastic RSI Fast %K (3,3,14,14)", // description
      "i": "Stochastic RSI kết hợp RSI và Stochastic để xác định quá mua/quá bán với độ nhạy cao hơn. Giá trị dưới 20 là quá bán mạnh (cơ hội mua), trên 80 là quá mua mạnh (nên bán). Chỉ báo này có độ nhạy cao hơn RSI thông thường." // info
    },
    {
      "n": "ADX", // name
      "v": 18.55, // value
      "s": "Trung tính", // signal
      "a": "Thị trường không có xu hướng rõ ràng, thận trọng khi giao dịch", // action
      "d": "Average Directional Index (14)", // description
      "i": "ADX đo lường độ mạnh của xu hướng, bất kể hướng tăng hay giảm. Giá trị >25 cho thấy xu hướng mạnh, >40 là xu hướng rất mạnh, <20 là thị trường sideway. +DI và -DI xác định hướng của xu hướng: +DI > -DI là xu hướng tăng, ngược lại là giảm." // info
    },
    {
      "n": "CCI", // name
      "v": 57.22, // value
      "s": "Trung tính", // signal
      "a": "CCI dương, xu hướng tăng nhẹ", // action
      "d": "Commodity Channel Index (20)", // description
      "i": "CCI đo lường sự khác biệt giữa giá hiện tại và giá trung bình lịch sử. Giá trị dưới -100 là quá bán (tín hiệu mua), trên +100 là quá mua (tín hiệu bán). Mức ±200 là vùng cực đoan, thường dẫn đến đảo chiều mạnh." // info
    },
    {
      "n": "WPR", // name
      "v": -16.36, // value
      "s": "Bán", // signal
      "a": "Williams %R trong vùng quá mua, xem xét mở vị thế bán", // action
      "d": "Williams %R (14)", // description
      "i": "Williams %R là chỉ báo động lượng đo lường mức độ quá mua hoặc quá bán. Giá trị từ -80 đến -100 là quá bán (tín hiệu mua), từ 0 đến -20 là quá mua (tín hiệu bán). Thường đảo chiều sớm hơn RSI và Stochastic." // info
    },
    {
      "n": "ULTOSC", // name
      "v": 63.85, // value
      "s": "Trung tính", // signal
      "a": "Ultimate Oscillator trên 50, xu hướng tăng nhẹ", // action
      "d": "Ultimate Oscillator (7,14,28)", // description
      "i": "Ultimate Oscillator kết hợp ba khoảng thời gian khác nhau (7, 14, 28) để giảm nhiễu và tạo tín hiệu chính xác hơn. Giá trị dưới 30 là quá bán, trên 70 là quá mua. Có độ tin cậy cao khi xác nhận với xu hướng giá chung." // info
    },
    {
      "n": "ROC", // name
      "v": 1.38, // value
      "s": "Mua", // signal
      "a": "ROC dương, đà tăng nhẹ, xu hướng có thể tiếp tục phát triển", // action
      "d": "Rate of Change (14)", // description
      "i": "Rate of Change đo lường tốc độ thay đổi giá theo phần trăm trong 14 phiên. Giá trị dương cao cho thấy tốc độ tăng mạnh (có thể là quá mua), giá trị âm sâu cho thấy tốc độ giảm mạnh (có thể là quá bán). Là chỉ báo động lượng tốt để xác định điểm đảo chiều." // info
    },
    {
      "n": "SAR", // name
      "v": 23349.57, // value
      "s": "Mua", // signal
      "a": "Giá đang nằm trên điểm SAR, xu hướng tăng đang tiếp diễn", // action
      "d": "Parabolic SAR", // description
      "i": "Parabolic SAR là chỉ báo theo xu hướng, dùng để xác định điểm dừng và đảo chiều. Điểm SAR nằm dưới giá là tín hiệu mua (xu hướng tăng), điểm SAR nằm trên giá là tín hiệu bán (xu hướng giảm). Khi giá cắt qua điểm SAR, đó là tín hiệu đảo chiều mạnh." // info
    },
    {
      "n": "OBV", // name
      "v": -146521852, // value
      "s": "Bán", // signal
      "a": "Xác nhận xu hướng giảm, lực bán mạnh", // action
      "d": "On Balance Volume", // description
      "i": "OBV là chỉ báo cộng dồn khối lượng theo chiều giá. Khi OBV tăng cùng chiều với giá xác nhận xu hướng tăng, ngược chiều với giá tạo phân kỳ cảnh báo đảo chiều." // info
    },
    {
      "n": "AO", // name
      "v": -266.62, // value
      "s": "Trung tính", // signal
      "a": "AO âm nhưng tăng, xu hướng giảm đang yếu đi", // action
      "d": "Awesome Oscillator", // description
      "i": "Awesome Oscillator so sánh động lượng của 5 phiên gần nhất với 34 phiên. Khi AO vượt qua mức 0 từ dưới lên là tín hiệu mua, từ trên xuống là tín hiệu bán. AO tăng trong vùng dương xác nhận xu hướng tăng, giảm trong vùng âm xác nhận xu hướng giảm." // info
    },
    {
      "n": "ADI", // name
      "v": -739710155.09, // value
      "s": "Mua", // signal
      "a": "ADI tăng trong khi giá giảm, phân kỳ tích cực, chuẩn bị đảo chiều tăng", // action
      "d": "Accumulation Distribution Index", // description
      "i": "ADI kết hợp giá và khối lượng để đánh giá dòng tiền. Khi ADI tăng cùng chiều với giá là xác nhận xu hướng, ngược chiều là dấu hiệu phân kỳ. Phân kỳ tích cực (giá giảm nhưng ADI tăng) báo hiệu đảo chiều tăng, phân kỳ tiêu cực ngược lại." // info
    },
    {
      "n": "BB", // name
      "v": 0.1954, // value
      "s": "Trung tính", // signal
      "a": "Giá trên dải giữa Bollinger Bands, xu hướng tăng nhẹ", // action
      "d": "Bollinger Bands Width (20,2)", // description
      "i": "Bollinger Bands sử dụng độ lệch chuẩn để tạo dải biến động quanh SMA. Giá chạm dải trên thường là tín hiệu quá mua, chạm dải dưới là quá bán. Dải hẹp thường báo hiệu sự tích lũy và sắp có breakout. BB Width đo lường độ rộng của dải, giá trị thấp dự báo biến động mạnh sắp xảy ra." // info
    },
    {
      "n": "Momentum", // name
      "v": 250, // value
      "s": "Mua", // signal
      "a": "Động lượng tăng rất mạnh, theo chiều xu hướng tăng", // action
      "d": "Momentum (14)", // description
      "i": "Momentum đo lường tốc độ thay đổi giá trong 14 phiên. Giá trị dương cao thể hiện xu hướng tăng mạnh, giá trị âm sâu thể hiện xu hướng giảm mạnh. Khi Momentum đạt cực trị và bắt đầu đảo chiều là dấu hiệu sớm cho sự thay đổi xu hướng." // info
    },
    {
      "n": "Bear Power", // name
      "v": -170.59, // value
      "s": "Bán", // signal
      "a": "Bear Power âm nhẹ, xu hướng giảm đang chiếm ưu thế", // action
      "d": "Bear Power", // description
      "i": "Bear Power đo lường sức mạnh của phe bán bằng cách so sánh giá thấp nhất với EMA 13 ngày. Giá trị dương cho thấy người mua đang kiểm soát thị trường, giá trị âm cho thấy người bán đang kiểm soát. Khi Bear Power âm nhưng đang tăng dần là tín hiệu mua tiềm năng." // info
    },
    {
      "n": "Stochastic", // name
      "v": 76.97, // value
      "s": "Bán", // signal
      "a": "%K cắt xuống dưới %D, động lượng giảm, cơ hội bán", // action
      "d": "Stochastic Oscillator %K (14,3)", // description
      "i": "Stochastic Oscillator so sánh giá đóng cửa với phạm vi giá trong một khoảng thời gian nhất định. %K là đường chính, %D là đường tín hiệu. Giá trị dưới 20 là quá bán, trên 80 là quá mua. Tín hiệu mua khi %K cắt lên %D, bán khi %K cắt xuống %D." // info
    }
  ]
}