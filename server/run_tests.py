#!/usr/bin/env python3
"""
Comprehensive test runner for StockPal clean architecture.

This script runs all unit tests, integration tests, and performance tests
to ensure the clean architecture migration is complete and working correctly.
"""

import os
import sys
import unittest
import logging
import time
from datetime import datetime
from typing import List, Dict, Any

# Add server directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.services.performance_monitor import performance_monitor


class TestRunner:
    """Comprehensive test runner for StockPal."""

    def __init__(self):
        """Initialize test runner."""
        self.logger = logging.getLogger(__name__)
        self.test_results = {}
        self.start_time = None
        self.end_time = None

    def setup_logging(self):
        """Setup logging for test execution."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('db/logs/test_execution.log')
            ]
        )

    def discover_tests(self, test_directory: str = "tests") -> unittest.TestSuite:
        """
        Discover all test files in the test directory.
        
        Args:
            test_directory: Directory containing test files
            
        Returns:
            Test suite with all discovered tests
        """
        loader = unittest.TestLoader()
        
        # Discover tests in the specified directory
        if os.path.exists(test_directory):
            suite = loader.discover(test_directory, pattern='test_*.py')
        else:
            self.logger.warning(f"Test directory {test_directory} not found")
            suite = unittest.TestSuite()
        
        return suite

    def run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests for StockPal indicators and core components."""
        self.logger.info("Running unit tests...")
        
        operation_id = performance_monitor.start_operation(
            "testing", "UnitTests", "StockPal"
        )
        
        try:
            # Import and run specific unit tests
            from tests.test_stockpal_indicators import TestStockPalIndicators
            
            suite = unittest.TestLoader().loadTestsFromTestCase(TestStockPalIndicators)
            runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
            result = runner.run(suite)
            
            performance_monitor.end_operation(operation_id, success=result.wasSuccessful())
            
            return {
                "tests_run": result.testsRun,
                "failures": len(result.failures),
                "errors": len(result.errors),
                "success": result.wasSuccessful(),
                "details": {
                    "failures": [str(f) for f in result.failures],
                    "errors": [str(e) for e in result.errors]
                }
            }
            
        except ImportError as e:
            self.logger.error(f"Failed to import unit tests: {str(e)}")
            performance_monitor.end_operation(
                operation_id, success=False, error_message=str(e)
            )
            return {
                "tests_run": 0,
                "failures": 0,
                "errors": 1,
                "success": False,
                "details": {"errors": [f"Import error: {str(e)}"]}
            }

    def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests for data providers and architecture layers."""
        self.logger.info("Running integration tests...")
        
        operation_id = performance_monitor.start_operation(
            "testing", "IntegrationTests", "StockPal"
        )
        
        try:
            # Import and run integration tests
            from tests.test_data_provider_integration import TestDataProviderIntegration
            
            suite = unittest.TestLoader().loadTestsFromTestCase(TestDataProviderIntegration)
            runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
            result = runner.run(suite)
            
            performance_monitor.end_operation(operation_id, success=result.wasSuccessful())
            
            return {
                "tests_run": result.testsRun,
                "failures": len(result.failures),
                "errors": len(result.errors),
                "success": result.wasSuccessful(),
                "details": {
                    "failures": [str(f) for f in result.failures],
                    "errors": [str(e) for e in result.errors]
                }
            }
            
        except ImportError as e:
            self.logger.error(f"Failed to import integration tests: {str(e)}")
            performance_monitor.end_operation(
                operation_id, success=False, error_message=str(e)
            )
            return {
                "tests_run": 0,
                "failures": 0,
                "errors": 1,
                "success": False,
                "details": {"errors": [f"Import error: {str(e)}"]}
            }

    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance tests to ensure clean architecture meets performance requirements."""
        self.logger.info("Running performance tests...")
        
        operation_id = performance_monitor.start_operation(
            "testing", "PerformanceTests", "StockPal"
        )
        
        try:
            # Test performance of key components
            from refactored_main import StockPalApplication
            
            app = StockPalApplication()
            test_symbols = ["VIC", "VHM", "HPG"]
            
            performance_results = []
            
            for symbol in test_symbols:
                # Test data fetching performance
                start_time = time.time()
                success = app.fetch_stock_data(symbol, days=30)
                fetch_time = time.time() - start_time
                
                # Test analysis performance
                start_time = time.time()
                analysis = app.analyze_stock(symbol, days_back=30)
                analysis_time = time.time() - start_time
                
                performance_results.append({
                    "symbol": symbol,
                    "fetch_time": fetch_time,
                    "analysis_time": analysis_time,
                    "fetch_success": success,
                    "analysis_success": analysis is not None
                })
            
            # Check performance thresholds
            avg_fetch_time = sum(r["fetch_time"] for r in performance_results) / len(performance_results)
            avg_analysis_time = sum(r["analysis_time"] for r in performance_results) / len(performance_results)
            
            success = (avg_fetch_time < 5.0 and avg_analysis_time < 3.0)  # 5s fetch, 3s analysis
            
            performance_monitor.end_operation(operation_id, success=success)
            
            return {
                "tests_run": len(test_symbols),
                "failures": 0 if success else 1,
                "errors": 0,
                "success": success,
                "details": {
                    "avg_fetch_time": avg_fetch_time,
                    "avg_analysis_time": avg_analysis_time,
                    "results": performance_results
                }
            }
            
        except Exception as e:
            self.logger.error(f"Performance tests failed: {str(e)}")
            performance_monitor.end_operation(
                operation_id, success=False, error_message=str(e)
            )
            return {
                "tests_run": 0,
                "failures": 0,
                "errors": 1,
                "success": False,
                "details": {"errors": [f"Performance test error: {str(e)}"]}
            }

    def run_architecture_validation(self) -> Dict[str, Any]:
        """Validate that clean architecture principles are followed."""
        self.logger.info("Running architecture validation...")
        
        validation_results = {
            "dependency_injection": True,
            "layer_separation": True,
            "legacy_code_removed": True,
            "pep8_compliance": True,
            "errors": []
        }
        
        try:
            # Check that legacy files are removed
            legacy_files = [
                "server/legacy_compatibility.py",
                "server/stock_analyzer.py"
            ]
            
            for file_path in legacy_files:
                if os.path.exists(file_path):
                    validation_results["legacy_code_removed"] = False
                    validation_results["errors"].append(f"Legacy file still exists: {file_path}")
            
            # Check that clean architecture components exist
            required_components = [
                "server/stockpal/core/stock.py",
                "server/stockpal/indicator/rs.py",
                "server/core/services/signal_synthesizer.py",
                "server/core/services/performance_monitor.py",
                "server/infrastructure/repositories/stock_repository.py"
            ]
            
            for component in required_components:
                if not os.path.exists(component):
                    validation_results["layer_separation"] = False
                    validation_results["errors"].append(f"Required component missing: {component}")
            
            # Test dependency injection
            try:
                from refactored_main import StockPalApplication
                app = StockPalApplication()
                
                # Check that dependencies are properly injected
                if not hasattr(app, 'data_service'):
                    validation_results["dependency_injection"] = False
                    validation_results["errors"].append("Data service not properly injected")
                
                if not hasattr(app, 'analysis_service'):
                    validation_results["dependency_injection"] = False
                    validation_results["errors"].append("Analysis service not properly injected")
                    
            except Exception as e:
                validation_results["dependency_injection"] = False
                validation_results["errors"].append(f"Dependency injection failed: {str(e)}")
            
            success = all([
                validation_results["dependency_injection"],
                validation_results["layer_separation"],
                validation_results["legacy_code_removed"]
            ])
            
            return {
                "tests_run": 4,  # 4 validation checks
                "failures": 0 if success else 1,
                "errors": 0,
                "success": success,
                "details": validation_results
            }
            
        except Exception as e:
            self.logger.error(f"Architecture validation failed: {str(e)}")
            return {
                "tests_run": 0,
                "failures": 0,
                "errors": 1,
                "success": False,
                "details": {"errors": [f"Validation error: {str(e)}"]}
            }

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test suites and return comprehensive results."""
        self.setup_logging()
        self.start_time = datetime.now()
        
        self.logger.info("=" * 80)
        self.logger.info("STOCKPAL CLEAN ARCHITECTURE TEST SUITE")
        self.logger.info("=" * 80)
        
        # Run all test suites
        self.test_results["unit_tests"] = self.run_unit_tests()
        self.test_results["integration_tests"] = self.run_integration_tests()
        self.test_results["performance_tests"] = self.run_performance_tests()
        self.test_results["architecture_validation"] = self.run_architecture_validation()
        
        self.end_time = datetime.now()
        
        # Calculate overall results
        total_tests = sum(result["tests_run"] for result in self.test_results.values())
        total_failures = sum(result["failures"] for result in self.test_results.values())
        total_errors = sum(result["errors"] for result in self.test_results.values())
        overall_success = all(result["success"] for result in self.test_results.values())
        
        # Generate summary
        summary = {
            "overall_success": overall_success,
            "total_tests": total_tests,
            "total_failures": total_failures,
            "total_errors": total_errors,
            "execution_time": (self.end_time - self.start_time).total_seconds(),
            "test_suites": self.test_results
        }
        
        # Print summary
        self.print_summary(summary)
        
        return summary

    def print_summary(self, summary: Dict[str, Any]):
        """Print test execution summary."""
        self.logger.info("=" * 80)
        self.logger.info("TEST EXECUTION SUMMARY")
        self.logger.info("=" * 80)
        
        status = "PASSED" if summary["overall_success"] else "FAILED"
        self.logger.info(f"Overall Status: {status}")
        self.logger.info(f"Total Tests: {summary['total_tests']}")
        self.logger.info(f"Failures: {summary['total_failures']}")
        self.logger.info(f"Errors: {summary['total_errors']}")
        self.logger.info(f"Execution Time: {summary['execution_time']:.2f} seconds")
        
        self.logger.info("\nTest Suite Results:")
        for suite_name, result in summary["test_suites"].items():
            status = "PASSED" if result["success"] else "FAILED"
            self.logger.info(f"  {suite_name}: {status} ({result['tests_run']} tests)")
        
        if not summary["overall_success"]:
            self.logger.info("\nFailure Details:")
            for suite_name, result in summary["test_suites"].items():
                if not result["success"]:
                    self.logger.info(f"  {suite_name}:")
                    for error in result["details"].get("errors", []):
                        self.logger.info(f"    ERROR: {error}")
                    for failure in result["details"].get("failures", []):
                        self.logger.info(f"    FAILURE: {failure}")
        
        self.logger.info("=" * 80)


def main():
    """Main entry point for test runner."""
    runner = TestRunner()
    summary = runner.run_all_tests()
    
    # Exit with appropriate code
    exit_code = 0 if summary["overall_success"] else 1
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
