"""
Indicator processor for calculating technical indicators.

This module provides a centralized way to calculate various technical indicators
with proper error handling and validation.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from stockpal.indicator.ma import MovingAverage
from stockpal.indicator.macd import MACD
from stockpal.indicator.rsi import RelativeStrengthIndex
from stockpal.indicator.bb import BollingerBands
from stockpal.indicator.adx import AverageDirectionalIndex
from stockpal.indicator.cci import CommodityChannelIndex
from stockpal.indicator.stoch import StochasticOscillator
from stockpal.indicator.wpr import WilliamsPercentR
from stockpal.indicator.roc import RateOfChange
from stockpal.indicator.sar import ParabolicSAR
from stockpal.indicator.obv import OnBalanceVolume
from stockpal.indicator.ao import AwesomeOscillator
from stockpal.indicator.adi import AccumulationDistributionIndex
from stockpal.indicator.momentum import Momentum
from stockpal.indicator.bearpower import BearPower
from stockpal.indicator.ultosc import UltimateOscillator
from stockpal.indicator.rs import RelativeStrength
from stockpal.indicator.ichimoku import <PERSON><PERSON><PERSON><PERSON>
from stockpal.indicator.pivot_points import PivotPoints
from stockpal.core.stock import DailyPrice

from shared.models.stock_models import PricePoint, TechnicalIndicator, IndicatorConfig
from shared.exceptions.stock_exceptions import IndicatorCalculationException, InsufficientDataException
from shared.utils.validation import validate_symbol


logger = logging.getLogger(__name__)


class IndicatorProcessor:
    """Processor for calculating technical indicators."""

    def __init__(self):
        """Initialize the indicator processor."""
        self._logger = logging.getLogger(__name__)

    def calculate_moving_averages(
        self,
        symbol: str,
        prices: List[PricePoint],
        periods: List[int] = None
    ) -> Dict[str, List[float]]:
        """
        Calculate moving averages for given periods.

        Args:
            symbol: Stock symbol
            prices: List of price points
            periods: List of periods to calculate (default: [5, 10, 20, 50, 100, 200])

        Returns:
            Dictionary with SMA and EMA values for each period

        Raises:
            IndicatorCalculationException: If calculation fails
        """
        try:
            if not prices:
                raise InsufficientDataException("No price data provided for moving averages")

            periods = periods or [5, 10, 20, 50, 100, 200]
            result = {}

            # Convert to legacy format for existing indicators
            legacy_prices = self._convert_to_legacy_prices(prices)

            for period in periods:
                try:
                    # Simple Moving Average
                    sma_calc = MovingAverage(symbol, legacy_prices, period=period, ma_type="simple")
                    sma_values = sma_calc.calculate()
                    result[f"sma{period}"] = sma_values or []

                    # Exponential Moving Average
                    ema_calc = MovingAverage(symbol, legacy_prices, period=period, ma_type="exponential")
                    ema_values = ema_calc.calculate()
                    result[f"ema{period}"] = ema_values or []

                except Exception as e:
                    self._logger.warning(f"Failed to calculate MA{period} for {symbol}: {str(e)}")
                    result[f"sma{period}"] = []
                    result[f"ema{period}"] = []

            return result

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise IndicatorCalculationException(f"Failed to calculate moving averages for {symbol}: {str(e)}")

    def calculate_macd(self, symbol: str, prices: List[PricePoint]) -> Dict[str, List[float]]:
        """
        Calculate MACD indicator.

        Args:
            symbol: Stock symbol
            prices: List of price points

        Returns:
            Dictionary with MACD line, signal line, and histogram

        Raises:
            IndicatorCalculationException: If calculation fails
        """
        try:
            if not prices:
                raise InsufficientDataException("No price data provided for MACD")

            legacy_prices = self._convert_to_legacy_prices(prices)

            macd_calc = MACD(symbol, legacy_prices)
            macd_result = macd_calc.calculate()

            return {
                "macd_line": macd_result.get("macd_line", []),
                "signal_line": macd_result.get("signal_line", []),
                "histogram": macd_result.get("histogram", [])
            }

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise IndicatorCalculationException(f"Failed to calculate MACD for {symbol}: {str(e)}")

    def calculate_rsi(self, symbol: str, prices: List[PricePoint], period: int = 14) -> List[float]:
        """
        Calculate RSI indicator.

        Args:
            symbol: Stock symbol
            prices: List of price points
            period: RSI period (default: 14)

        Returns:
            List of RSI values

        Raises:
            IndicatorCalculationException: If calculation fails
        """
        try:
            if not prices:
                raise InsufficientDataException("No price data provided for RSI")

            legacy_prices = self._convert_to_legacy_prices(prices)

            rsi_calc = RelativeStrengthIndex(symbol, legacy_prices, period=period)
            rsi_values = rsi_calc.calculate()

            return rsi_values or []

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise IndicatorCalculationException(f"Failed to calculate RSI for {symbol}: {str(e)}")

    def calculate_bollinger_bands(
        self,
        symbol: str,
        prices: List[PricePoint],
        period: int = 20,
        std_dev: float = 2.0
    ) -> Dict[str, List[float]]:
        """
        Calculate Bollinger Bands.

        Args:
            symbol: Stock symbol
            prices: List of price points
            period: Period for calculation (default: 20)
            std_dev: Standard deviation multiplier (default: 2.0)

        Returns:
            Dictionary with upper, middle, and lower bands

        Raises:
            IndicatorCalculationException: If calculation fails
        """
        try:
            if not prices:
                raise InsufficientDataException("No price data provided for Bollinger Bands")

            legacy_prices = self._convert_to_legacy_prices(prices)

            bb_calc = BollingerBands(symbol, legacy_prices, period=period, num_std_dev=std_dev)
            bb_result = bb_calc.calculate()
            bb_width = bb_calc.get_band_width()

            return {
                "upper_band": bb_result.get("upper_band", []),
                "middle_band": bb_result.get("middle_band", []),
                "lower_band": bb_result.get("lower_band", []),
                "band_width": bb_width or []
            }

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise IndicatorCalculationException(f"Failed to calculate Bollinger Bands for {symbol}: {str(e)}")

    def calculate_stochastic(
        self,
        symbol: str,
        prices: List[PricePoint],
        k_period: int = 14,
        d_period: int = 3
    ) -> Dict[str, List[float]]:
        """
        Calculate Stochastic Oscillator.

        Args:
            symbol: Stock symbol
            prices: List of price points
            k_period: %K period (default: 14)
            d_period: %D period (default: 3)

        Returns:
            Dictionary with %K and %D values

        Raises:
            IndicatorCalculationException: If calculation fails
        """
        try:
            if not prices:
                raise InsufficientDataException("No price data provided for Stochastic")

            legacy_prices = self._convert_to_legacy_prices(prices)

            stoch_calc = StochasticOscillator(symbol, legacy_prices, k_period=k_period, d_period=d_period)
            stoch_result = stoch_calc.calculate()

            return {
                "k_values": stoch_result.get("k_values", []),
                "d_values": stoch_result.get("d_values", [])
            }

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise IndicatorCalculationException(f"Failed to calculate Stochastic for {symbol}: {str(e)}")

    def calculate_single_value_indicator(
        self,
        symbol: str,
        prices: List[PricePoint],
        indicator_name: str,
        **kwargs
    ) -> Optional[float]:
        """
        Calculate a single-value indicator.

        Args:
            symbol: Stock symbol
            prices: List of price points
            indicator_name: Name of the indicator
            **kwargs: Additional parameters for the indicator

        Returns:
            Single indicator value or None if calculation fails

        Raises:
            IndicatorCalculationException: If calculation fails
        """
        try:
            if not prices:
                raise InsufficientDataException(f"No price data provided for {indicator_name}")

            legacy_prices = self._convert_to_legacy_prices(prices)

            # Map indicator names to their calculators
            indicator_map = {
                "cci": lambda: CommodityChannelIndex(symbol, legacy_prices).calculate(),
                "adx": lambda: AverageDirectionalIndex(symbol, legacy_prices).calculate(),
                "wpr": lambda: WilliamsPercentR(symbol, legacy_prices).calculate(),
                "roc": lambda: RateOfChange(symbol, legacy_prices).calculate(),
                "sar": lambda: ParabolicSAR(symbol, legacy_prices).calculate(),
                "obv": lambda: OnBalanceVolume(symbol, legacy_prices).calculate(),
                "ao": lambda: AwesomeOscillator(symbol, legacy_prices).calculate(),
                "adi": lambda: AccumulationDistributionIndex(symbol, legacy_prices).calculate(),
                "momentum": lambda: Momentum(symbol, legacy_prices).calculate(),
                "bearpower": lambda: BearPower(symbol, legacy_prices).calculate(),
                "ultosc": lambda: UltimateOscillator(symbol, legacy_prices).calculate(),
            }

            if indicator_name.lower() not in indicator_map:
                raise IndicatorCalculationException(f"Unknown indicator: {indicator_name}")

            result = indicator_map[indicator_name.lower()]()

            # Handle different return types
            if isinstance(result, list) and result:
                return result[-1] if result[-1] is not None else None
            elif isinstance(result, dict):
                # For indicators that return dictionaries (like ADI)
                if indicator_name.lower() == "adi" and "adi" in result:
                    adi_values = result["adi"]
                    return adi_values[-1] if adi_values and adi_values[-1] is not None else None
                elif indicator_name.lower() == "adx":
                    # ADX returns a complex structure, return the ADX value
                    adx_values = result.get("adx", [])
                    return adx_values[-1] if adx_values and adx_values[-1] is not None else None

            return None

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise IndicatorCalculationException(f"Failed to calculate {indicator_name} for {symbol}: {str(e)}")

    def _convert_to_legacy_prices(self, prices: List[PricePoint]) -> List:
        """
        Convert domain price points to legacy format for existing indicators.

        Args:
            prices: List of domain price points

        Returns:
            List of legacy price objects
        """
        legacy_prices = []
        for price in prices:
            legacy_price = DailyPrice(
                symbol=price.symbol,
                timestamp=price.timestamp,
                open_price=price.open_price,
                highest_price=price.high_price,
                lowest_price=price.low_price,
                close_price=price.close_price,
                match_volume=price.volume,
                change_price=price.change_price or 0.0,
                change_price_percent=price.change_percent or 0.0
            )
            legacy_prices.append(legacy_price)

        return legacy_prices

    def calculate_all_indicators(self, symbol: str, prices: List[PricePoint]) -> Dict[str, List[float]]:
        """
        Calculate all available technical indicators.

        Args:
            symbol: Stock symbol
            prices: List of price points

        Returns:
            Dictionary with all calculated indicators

        Raises:
            IndicatorCalculationException: If calculation fails
        """
        try:
            if not prices:
                raise InsufficientDataException("No price data provided for indicators")

            self._logger.info(f"Calculating all indicators for {symbol}")

            result = {}

            # Moving averages
            try:
                ma_result = self.calculate_moving_averages(symbol, prices)
                result.update(ma_result)
            except Exception as e:
                self._logger.warning(f"Failed to calculate moving averages for {symbol}: {str(e)}")

            # MACD
            try:
                macd_result = self.calculate_macd(symbol, prices)
                result.update(macd_result)
            except Exception as e:
                self._logger.warning(f"Failed to calculate MACD for {symbol}: {str(e)}")

            # RSI
            try:
                rsi_values = self.calculate_rsi(symbol, prices)
                result["rsi"] = rsi_values
            except Exception as e:
                self._logger.warning(f"Failed to calculate RSI for {symbol}: {str(e)}")

            # Bollinger Bands
            try:
                bb_result = self.calculate_bollinger_bands(symbol, prices)
                result.update(bb_result)
            except Exception as e:
                self._logger.warning(f"Failed to calculate Bollinger Bands for {symbol}: {str(e)}")

            # Stochastic
            try:
                stoch_result = self.calculate_stochastic(symbol, prices)
                result.update(stoch_result)
            except Exception as e:
                self._logger.warning(f"Failed to calculate Stochastic for {symbol}: {str(e)}")

            # Single value indicators
            single_indicators = ["cci", "adx", "wpr", "roc", "sar", "obv", "ao", "adi", "momentum", "bearpower", "ultosc"]
            for indicator_name in single_indicators:
                try:
                    value = self.calculate_single_value_indicator(symbol, prices, indicator_name)
                    if value is not None:
                        result[indicator_name] = [value]  # Convert to list for consistency
                except Exception as e:
                    self._logger.warning(f"Failed to calculate {indicator_name} for {symbol}: {str(e)}")

            self._logger.info(f"Calculated {len(result)} indicators for {symbol}")
            return result

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise IndicatorCalculationException(f"Failed to calculate indicators for {symbol}: {str(e)}")
