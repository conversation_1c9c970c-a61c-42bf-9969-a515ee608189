"""
Symbol repository for accessing and managing stock symbol data.

This module provides a clean interface for accessing symbol data while abstracting
away the underlying storage mechanisms.
"""

from typing import List, Optional, Protocol

from stockpal.core.sql_service import SqliteService
from stockpal.core.stock import Stock
from stockpal.db import SymbolDao

from shared.exceptions.stock_exceptions import DatabaseException, SymbolNotFoundException
from shared.utils.validation import validate_symbol


class SymbolRepositoryInterface(Protocol):
    """Interface for symbol repositories."""

    def get_all_symbols(self) -> List[str]:
        """Get all available stock symbols."""
        ...

    def get_symbol_info(self, symbol: str) -> dict:
        """Get detailed information about a symbol."""
        ...

    def symbol_exists(self, symbol: str) -> bool:
        """Check if a symbol exists."""
        ...

    def add_symbol(self, symbol: str, name: str = None) -> None:
        """Add a new symbol."""
        ...


class SqliteSymbolRepository:
    """SQLite implementation of the symbol repository."""

    def __init__(self, sql_service: Optional[SqliteService] = None):
        """
        Initialize the repository.

        Args:
            sql_service: Optional SQL service instance. If None, creates a new one.
        """
        self._sql_service = sql_service or SqliteService()
        self._symbol_dao = SymbolDao(sql_service=self._sql_service)

    def get_all_symbols(self) -> List[str]:
        """
        Get all available stock symbols.

        Returns:
            List of stock symbols

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            stocks = self._symbol_dao.get_all()
            return [stock.code for stock in stocks]
        except Exception as e:
            raise DatabaseException(f"Failed to get all symbols: {str(e)}")

    def get_symbol_info(self, symbol: str) -> dict:
        """
        Get detailed information about a symbol.

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary containing symbol information

        Raises:
            DatabaseException: If database operation fails
            SymbolNotFoundException: If symbol is not found
        """
        try:
            symbol = validate_symbol(symbol)

            # Note: The current SymbolDao doesn't have a get_by_code method
            # This is a limitation of the existing implementation
            stocks = self._symbol_dao.get_all()

            for stock in stocks:
                if stock.code == symbol:
                    return {
                        'code': stock.code,
                        'name': getattr(stock, 'name', None),
                        'exchange': getattr(stock, 'exchange', None),
                        'sector': getattr(stock, 'sector', None),
                        'industry': getattr(stock, 'industry', None)
                    }

            raise SymbolNotFoundException(f"Symbol not found: {symbol}")

        except Exception as e:
            if isinstance(e, SymbolNotFoundException):
                raise
            raise DatabaseException(f"Failed to get symbol info for {symbol}: {str(e)}")

    def symbol_exists(self, symbol: str) -> bool:
        """
        Check if a symbol exists.

        Args:
            symbol: Stock symbol

        Returns:
            True if symbol exists, False otherwise

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            symbol = validate_symbol(symbol)

            stocks = self._symbol_dao.get_all()
            return any(stock.code == symbol for stock in stocks)

        except Exception as e:
            raise DatabaseException(f"Failed to check if symbol exists {symbol}: {str(e)}")

    def add_symbol(self, symbol: str, name: str = None) -> None:
        """
        Add a new symbol.

        Args:
            symbol: Stock symbol
            name: Optional company name

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            symbol = validate_symbol(symbol)

            # Check if symbol already exists
            if self.symbol_exists(symbol):
                return  # Symbol already exists, no need to add

            # Create a new Stock object
            # Note: This depends on the Stock class structure
            stock = Stock(code=symbol)
            if name:
                stock.name = name

            # Note: The current SymbolDao might not have an add method
            # This would need to be implemented in the existing DAO
            # For now, we'll raise an exception indicating this limitation
            raise DatabaseException(
                "Adding symbols is not supported by the current SymbolDao implementation"
            )

        except Exception as e:
            if isinstance(e, DatabaseException):
                raise
            raise DatabaseException(f"Failed to add symbol {symbol}: {str(e)}")

    def get_symbols_by_exchange(self, exchange: str) -> List[str]:
        """
        Get symbols filtered by exchange.

        Args:
            exchange: Exchange name (e.g., 'HOSE', 'HNX', 'UPCOM')

        Returns:
            List of symbols for the specified exchange

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            stocks = self._symbol_dao.get_all()

            # Filter by exchange if the Stock object has an exchange attribute
            filtered_symbols = []
            for stock in stocks:
                stock_exchange = getattr(stock, 'exchange', None)
                if stock_exchange and stock_exchange.upper() == exchange.upper():
                    filtered_symbols.append(stock.code)

            return filtered_symbols

        except Exception as e:
            raise DatabaseException(f"Failed to get symbols for exchange {exchange}: {str(e)}")

    def search_symbols(self, query: str) -> List[str]:
        """
        Search for symbols by name or code.

        Args:
            query: Search query

        Returns:
            List of matching symbols

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            if not query or len(query.strip()) < 2:
                return []

            query = query.strip().upper()
            stocks = self._symbol_dao.get_all()

            matching_symbols = []
            for stock in stocks:
                # Search by code
                if query in stock.code:
                    matching_symbols.append(stock.code)
                    continue

                # Search by name if available
                stock_name = getattr(stock, 'name', '')
                if stock_name and query.lower() in stock_name.lower():
                    matching_symbols.append(stock.code)

            return matching_symbols

        except Exception as e:
            raise DatabaseException(f"Failed to search symbols with query '{query}': {str(e)}")
