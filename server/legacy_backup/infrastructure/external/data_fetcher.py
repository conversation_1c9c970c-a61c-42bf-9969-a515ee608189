"""
External data fetcher for retrieving stock data from various providers.

This module provides a clean interface for fetching data from external sources
while handling errors and data validation.
"""

import logging
from typing import List, Optional, Protocol

from stockpal.data.data_scraper import DataScraper
from stockpal.core.stock import PriceData

from shared.models.stock_models import PricePoint, DataFetchRequest
from shared.exceptions.stock_exceptions import DataFetchException, NetworkException
from shared.utils.validation import validate_symbol, validate_provider


logger = logging.getLogger(__name__)


class DataFetcherInterface(Protocol):
    """Interface for data fetchers."""

    def fetch_daily_prices(self, request: DataFetchRequest) -> List[PricePoint]:
        """Fetch daily price data."""
        ...

    def fetch_minute_prices(self, request: DataFetchRequest) -> List[PricePoint]:
        """Fetch minute price data."""
        ...

    def fetch_symbols(self, provider: str) -> List[str]:
        """Fetch available symbols from provider."""
        ...


class ExternalDataFetcher:
    """External data fetcher implementation using existing scrapers."""

    def __init__(self):
        """Initialize the data fetcher."""
        self._logger = logging.getLogger(__name__)

    def fetch_daily_prices(self, request: DataFetchRequest) -> List[PricePoint]:
        """
        Fetch daily price data from external provider.

        Args:
            request: Data fetch request

        Returns:
            List of price points

        Raises:
            DataFetchException: If data fetching fails
            NetworkException: If network operation fails
        """
        try:
            # Validate inputs
            symbol = validate_symbol(request.symbol)
            provider = validate_provider(request.provider)

            self._logger.info(f"Fetching daily prices for {symbol} from {provider.value}")

            # Create scraper instance
            scraper = DataScraper(symbol=symbol, provider=provider.value)

            # Fetch prices
            prices = scraper.fetch_prices(timeframe_in_minute=False)

            if not prices:
                raise DataFetchException(f"No price data returned for {symbol} from {provider}")

            # Convert to domain models
            price_points = []
            for price in prices:
                if price.close_price == 0:
                    continue  # Skip invalid prices

                price_point = self._convert_to_price_point(price)
                price_points.append(price_point)

            self._logger.info(f"Successfully fetched {len(price_points)} daily prices for {symbol}")
            return price_points

        except Exception as e:
            if isinstance(e, (DataFetchException, NetworkException)):
                raise

            error_msg = f"Failed to fetch daily prices for {request.symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def fetch_minute_prices(self, request: DataFetchRequest) -> List[PricePoint]:
        """
        Fetch minute price data from external provider.

        Args:
            request: Data fetch request

        Returns:
            List of price points

        Raises:
            DataFetchException: If data fetching fails
            NetworkException: If network operation fails
        """
        try:
            # Validate inputs
            symbol = validate_symbol(request.symbol)
            provider = validate_provider(request.provider)

            self._logger.info(f"Fetching minute prices for {symbol} from {provider.value}")

            # Create scraper instance
            scraper = DataScraper(symbol=symbol, provider=provider.value)

            # Fetch prices
            prices = scraper.fetch_prices(timeframe_in_minute=True)

            if not prices:
                raise DataFetchException(f"No minute price data returned for {symbol} from {provider}")

            # Convert to domain models
            price_points = []
            for price in prices:
                if price.close_price == 0:
                    continue  # Skip invalid prices

                price_point = self._convert_to_price_point(price)
                price_points.append(price_point)

            self._logger.info(f"Successfully fetched {len(price_points)} minute prices for {symbol}")
            return price_points

        except Exception as e:
            if isinstance(e, (DataFetchException, NetworkException)):
                raise

            error_msg = f"Failed to fetch minute prices for {request.symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def fetch_symbols(self, provider: str) -> List[str]:
        """
        Fetch available symbols from provider.

        Args:
            provider: Data provider name

        Returns:
            List of available symbols

        Raises:
            DataFetchException: If symbol fetching fails
            NetworkException: If network operation fails
        """
        try:
            provider_enum = validate_provider(provider)

            self._logger.info(f"Fetching symbols from {provider_enum.value}")

            # Create scraper instance with a dummy symbol
            scraper = DataScraper(symbol="VIC", provider=provider_enum.value)

            # Fetch symbols
            stocks = scraper.fetch_stock_symbols()

            if not stocks:
                raise DataFetchException(f"No symbols returned from {provider}")

            symbols = [stock.code for stock in stocks if stock.code]

            self._logger.info(f"Successfully fetched {len(symbols)} symbols from {provider}")
            return symbols

        except Exception as e:
            if isinstance(e, (DataFetchException, NetworkException)):
                raise

            error_msg = f"Failed to fetch symbols from {provider}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def _convert_to_price_point(self, price_data: PriceData) -> PricePoint:
        """
        Convert internal price data to domain model.

        Args:
            price_data: Internal price data object

        Returns:
            Price point domain model
        """
        def safe_get(attr_name):
            return getattr(price_data, attr_name, None)

        return PricePoint(
            timestamp=price_data.timestamp,
            open_price=price_data.open_price,
            high_price=price_data.highest_price,
            low_price=price_data.lowest_price,
            close_price=price_data.close_price,
            volume=price_data.match_volume,
            change_price=safe_get('change_price'),
            change_percent=safe_get('change_price_percent'),

            # Additional fields from enhanced model
            symbol=safe_get('symbol'),
            average_price=safe_get('average_price'),
            close_price_adjusted=safe_get('close_price_adjusted'),
            ceiling_price=safe_get('ceiling_price'),
            floor_price=safe_get('floor_price'),
            reference_price=safe_get('reference_price'),

            # Trading volume and value data
            match_value=safe_get('match_value'),
            deal_value=safe_get('deal_value'),
            deal_volume=safe_get('deal_volume'),

            # Foreign trading data
            foreign_current_room=safe_get('foreign_current_room'),
            foreign_buy_volume=safe_get('foreign_buy_volume'),
            foreign_buy_value=safe_get('foreign_buy_value'),
            foreign_sell_volume=safe_get('foreign_sell_volume'),
            foreign_sell_value=safe_get('foreign_sell_value'),
            foreign_net_volume=safe_get('foreign_net_volume'),
            foreign_net_value=safe_get('foreign_net_value'),
            foreign_match_buy_volume=safe_get('foreign_match_buy_volume'),
            foreign_deal_buy_volume=safe_get('foreign_deal_buy_volume'),

            # Buy/sell trade data
            buy_trade_quantity=safe_get('buy_trade_quantity'),
            buy_trade_volume=safe_get('buy_trade_volume'),
            sell_trade_quantity=safe_get('sell_trade_quantity'),
            sell_trade_volume=safe_get('sell_trade_volume')
        )

    def validate_connection(self, provider: str) -> bool:
        """
        Validate connection to a data provider.

        Args:
            provider: Data provider name

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            provider_enum = validate_provider(provider)

            # Try to fetch a small amount of data for a known symbol
            request = DataFetchRequest(symbol="VIC", provider=provider_enum)
            prices = self.fetch_daily_prices(request)

            return len(prices) > 0

        except Exception as e:
            self._logger.warning(
                f"Connection validation failed for {provider}: {str(e)}"
            )
            return False
