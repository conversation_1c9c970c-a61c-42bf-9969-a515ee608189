"""
Custom exceptions for the StockPal application.

This module defines custom exception classes that provide better error handling
and debugging capabilities throughout the application.
"""


class StockPalException(Exception):
    """Base exception for all StockPal-related errors."""

    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class DataFetchException(StockPalException):
    """Exception raised when data fetching fails."""
    pass


class DataValidationException(StockPalException):
    """Exception raised when data validation fails."""
    pass


class AnalysisException(StockPalException):
    """Exception raised when stock analysis fails."""
    pass


class IndicatorCalculationException(StockPalException):
    """Exception raised when indicator calculation fails."""
    pass


class InsufficientDataException(StockPalException):
    """Exception raised when there's insufficient data for analysis."""
    pass


class ConfigurationException(StockPalException):
    """Exception raised when configuration is invalid."""
    pass


class ExportException(StockPalException):
    """Exception raised when data export fails."""
    pass


class CacheException(StockPalException):
    """Exception raised when cache operations fail."""
    pass


class SymbolNotFoundException(StockPalException):
    """Exception raised when a stock symbol is not found."""
    pass


class InvalidTimeframeException(StockPalException):
    """Exception raised when an invalid timeframe is specified."""
    pass


class NetworkException(StockPalException):
    """Exception raised when network operations fail."""
    pass


class DatabaseException(StockPalException):
    """Exception raised when database operations fail."""
    pass


class PerformanceException(StockPalException):
    """Exception raised for performance monitoring errors."""
    pass
