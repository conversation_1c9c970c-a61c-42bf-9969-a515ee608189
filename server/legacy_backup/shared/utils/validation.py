"""
Validation utilities for the StockPal application.

This module provides validation functions for various data types and business rules.
"""

import re
from datetime import datetime, timedelta
from typing import List, Optional, Any

from ..exceptions.stock_exceptions import DataValidationException
from ..models.stock_models import DataProvider


def validate_symbol(symbol: str) -> str:
    """
    Validate and normalize a stock symbol.

    Args:
        symbol: The stock symbol to validate

    Returns:
        The normalized symbol (uppercase)

    Raises:
        DataValidationException: If the symbol is invalid
    """
    if not symbol or not isinstance(symbol, str):
        raise DataValidationException("Symbol must be a non-empty string")

    # Remove whitespace and convert to uppercase
    symbol = symbol.strip().upper()

    # Check if symbol matches Vietnamese stock symbol pattern
    if not re.match(r'^[A-Z]{3,4}$', symbol):
        raise DataValidationException(
            f"Invalid symbol format: {symbol}. Must be 3-4 uppercase letters."
        )

    return symbol


def validate_price(price: Any) -> float:
    """
    Validate and convert a price value.

    Args:
        price: The price value to validate

    Returns:
        The validated price as float

    Raises:
        DataValidationException: If the price is invalid
    """
    try:
        price_float = float(price)
        if price_float < 0:
            raise DataValidationException("Price cannot be negative")
        return price_float
    except (ValueError, TypeError):
        raise DataValidationException(f"Invalid price value: {price}")


def validate_volume(volume: Any) -> int:
    """
    Validate and convert a volume value.

    Args:
        volume: The volume value to validate

    Returns:
        The validated volume as int

    Raises:
        DataValidationException: If the volume is invalid
    """
    try:
        volume_int = int(volume)
        if volume_int < 0:
            raise DataValidationException("Volume cannot be negative")
        return volume_int
    except (ValueError, TypeError):
        raise DataValidationException(f"Invalid volume value: {volume}")


def validate_timestamp(timestamp: Any) -> int:
    """
    Validate and convert a timestamp value.

    Args:
        timestamp: The timestamp to validate

    Returns:
        The validated timestamp as int

    Raises:
        DataValidationException: If the timestamp is invalid
    """
    try:
        if isinstance(timestamp, datetime):
            return int(timestamp.timestamp())

        timestamp_int = int(timestamp)

        # Check if timestamp is reasonable (between 2000 and 2100)
        min_timestamp = int(datetime(2000, 1, 1).timestamp())
        max_timestamp = int(datetime(2100, 1, 1).timestamp())

        if not (min_timestamp <= timestamp_int <= max_timestamp):
            raise DataValidationException(
                f"Timestamp {timestamp_int} is outside reasonable range"
            )

        return timestamp_int
    except (ValueError, TypeError):
        raise DataValidationException(f"Invalid timestamp value: {timestamp}")


def validate_date_range(start_date: datetime, end_date: datetime) -> None:
    """
    Validate a date range.

    Args:
        start_date: The start date
        end_date: The end date

    Raises:
        DataValidationException: If the date range is invalid
    """
    if start_date >= end_date:
        raise DataValidationException("Start date must be before end date")

    # Check if the range is reasonable (not more than 10 years)
    max_range = timedelta(days=3650)  # ~10 years
    if end_date - start_date > max_range:
        raise DataValidationException("Date range cannot exceed 10 years")


def validate_days_back(days: Any) -> int:
    """
    Validate the number of days to look back.

    Args:
        days: The number of days

    Returns:
        The validated number of days

    Raises:
        DataValidationException: If the days value is invalid
    """
    try:
        days_int = int(days)
        if days_int <= 0:
            raise DataValidationException("Days must be positive")
        if days_int > 3650:  # ~10 years
            raise DataValidationException("Days cannot exceed 3650 (10 years)")
        return days_int
    except (ValueError, TypeError):
        raise DataValidationException(f"Invalid days value: {days}")


def validate_provider(provider) -> DataProvider:
    """
    Validate a data provider.

    Args:
        provider: The provider (string or DataProvider enum)

    Returns:
        The validated DataProvider enum

    Raises:
        DataValidationException: If the provider is invalid
    """
    if isinstance(provider, DataProvider):
        return provider

    if not provider or not isinstance(provider, str):
        raise DataValidationException("Provider must be a non-empty string or DataProvider enum")

    provider_str = provider.lower().strip()

    try:
        return DataProvider(provider_str)
    except ValueError:
        valid_providers = [p.value for p in DataProvider]
        raise DataValidationException(
            f"Invalid provider: {provider_str}. Must be one of {valid_providers}"
        )


def validate_price_data(price_data: dict) -> dict:
    """
    Validate price data dictionary.

    Args:
        price_data: Dictionary containing price data

    Returns:
        Validated price data dictionary

    Raises:
        DataValidationException: If the price data is invalid
    """
    required_fields = ['timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume']

    for field in required_fields:
        if field not in price_data:
            raise DataValidationException(f"Missing required field: {field}")

    validated_data = {}

    # Validate timestamp
    validated_data['timestamp'] = validate_timestamp(price_data['timestamp'])

    # Validate prices
    for price_field in ['open_price', 'high_price', 'low_price', 'close_price']:
        validated_data[price_field] = validate_price(price_data[price_field])

    # Validate volume
    validated_data['volume'] = validate_volume(price_data['volume'])

    # Validate price relationships
    if not (validated_data['low_price'] <= validated_data['open_price'] <= validated_data['high_price']):
        raise DataValidationException("Open price must be between low and high prices")

    if not (validated_data['low_price'] <= validated_data['close_price'] <= validated_data['high_price']):
        raise DataValidationException("Close price must be between low and high prices")

    # Add optional fields if present
    for optional_field in ['change_price', 'change_percent']:
        if optional_field in price_data:
            validated_data[optional_field] = float(price_data[optional_field])

    return validated_data
