"""
File utility functions for the StockPal application.

This module provides common file operations and utilities used across the application.
"""

import os
import shutil
import logging
from pathlib import Path
from typing import Optional, List


logger = logging.getLogger(__name__)


def ensure_directory_exists(directory_path: str) -> bool:
    """
    Ensure that a directory exists, creating it if necessary.

    Args:
        directory_path: Path to the directory

    Returns:
        True if directory exists or was created successfully, False otherwise
    """
    try:
        if not directory_path:
            return False
            
        Path(directory_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory {directory_path}: {str(e)}")
        return False


def safe_file_write(file_path: str, content: str, encoding: str = "utf-8") -> bool:
    """
    Safely write content to a file with error handling.

    Args:
        file_path: Path to the file
        content: Content to write
        encoding: File encoding

    Returns:
        True if write successful, False otherwise
    """
    try:
        # Ensure directory exists
        directory = os.path.dirname(file_path)
        if directory and not ensure_directory_exists(directory):
            return False

        with open(file_path, "w", encoding=encoding) as f:
            f.write(content)
        
        return True
    except Exception as e:
        logger.error(f"Failed to write file {file_path}: {str(e)}")
        return False


def safe_file_read(file_path: str, encoding: str = "utf-8") -> Optional[str]:
    """
    Safely read content from a file with error handling.

    Args:
        file_path: Path to the file
        encoding: File encoding

    Returns:
        File content if successful, None otherwise
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"File does not exist: {file_path}")
            return None

        with open(file_path, "r", encoding=encoding) as f:
            return f.read()
    except Exception as e:
        logger.error(f"Failed to read file {file_path}: {str(e)}")
        return None


def copy_file(source_path: str, destination_path: str) -> bool:
    """
    Copy a file from source to destination with error handling.

    Args:
        source_path: Source file path
        destination_path: Destination file path

    Returns:
        True if copy successful, False otherwise
    """
    try:
        if not os.path.exists(source_path):
            logger.error(f"Source file does not exist: {source_path}")
            return False

        # Ensure destination directory exists
        dest_dir = os.path.dirname(destination_path)
        if dest_dir and not ensure_directory_exists(dest_dir):
            return False

        shutil.copy2(source_path, destination_path)
        return True
    except Exception as e:
        logger.error(f"Failed to copy file from {source_path} to {destination_path}: {str(e)}")
        return False


def get_file_size(file_path: str) -> Optional[int]:
    """
    Get the size of a file in bytes.

    Args:
        file_path: Path to the file

    Returns:
        File size in bytes if successful, None otherwise
    """
    try:
        if not os.path.exists(file_path):
            return None
        return os.path.getsize(file_path)
    except Exception as e:
        logger.error(f"Failed to get file size for {file_path}: {str(e)}")
        return None


def list_files_in_directory(directory_path: str, extension: Optional[str] = None) -> List[str]:
    """
    List all files in a directory, optionally filtered by extension.

    Args:
        directory_path: Path to the directory
        extension: File extension to filter by (e.g., '.json', '.txt')

    Returns:
        List of file paths
    """
    try:
        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            return []

        files = []
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            if os.path.isfile(item_path):
                if extension is None or item.endswith(extension):
                    files.append(item_path)
        
        return sorted(files)
    except Exception as e:
        logger.error(f"Failed to list files in directory {directory_path}: {str(e)}")
        return []


def delete_file(file_path: str) -> bool:
    """
    Delete a file with error handling.

    Args:
        file_path: Path to the file to delete

    Returns:
        True if deletion successful or file doesn't exist, False otherwise
    """
    try:
        if not os.path.exists(file_path):
            return True  # File doesn't exist, consider it successful

        os.remove(file_path)
        return True
    except Exception as e:
        logger.error(f"Failed to delete file {file_path}: {str(e)}")
        return False


def clean_directory(directory_path: str, keep_extensions: Optional[List[str]] = None) -> bool:
    """
    Clean a directory by removing files, optionally keeping files with specific extensions.

    Args:
        directory_path: Path to the directory to clean
        keep_extensions: List of file extensions to keep (e.g., ['.json', '.txt'])

    Returns:
        True if cleaning successful, False otherwise
    """
    try:
        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            return True

        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            
            if os.path.isfile(item_path):
                # Check if we should keep this file
                if keep_extensions:
                    should_keep = any(item.endswith(ext) for ext in keep_extensions)
                    if should_keep:
                        continue
                
                # Delete the file
                if not delete_file(item_path):
                    return False

        return True
    except Exception as e:
        logger.error(f"Failed to clean directory {directory_path}: {str(e)}")
        return False


def get_project_root() -> str:
    """
    Get the project root directory.

    Returns:
        Path to the project root directory
    """
    # Assuming this file is in server/shared/utils/
    current_file = Path(__file__)
    project_root = current_file.parent.parent.parent.parent
    return str(project_root.resolve())


def get_web_directories() -> dict:
    """
    Get paths to web directories.

    Returns:
        Dictionary with web directory paths
    """
    project_root = get_project_root()
    return {
        "web": os.path.join(project_root, "web"),
        "web2": os.path.join(project_root, "web2"),
        "web_test": os.path.join(project_root, "web_test")
    }


def backup_file(file_path: str, backup_suffix: str = ".bak") -> bool:
    """
    Create a backup of a file.

    Args:
        file_path: Path to the file to backup
        backup_suffix: Suffix to add to the backup file

    Returns:
        True if backup successful, False otherwise
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"Cannot backup non-existent file: {file_path}")
            return False

        backup_path = file_path + backup_suffix
        return copy_file(file_path, backup_path)
    except Exception as e:
        logger.error(f"Failed to backup file {file_path}: {str(e)}")
        return False
