from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

class BaseIndicator(ABC):
    """
    Abstract base class for all technical indicators.
    Defines the standard interface for calculation, signal generation, trend prediction, and recommendations.
    """
    def __init__(self, symbol: str, prices: list, **kwargs):
        """
        Initialize the indicator with symbol and price data.
        Args:
            symbol (str): Stock symbol
            prices (list): List of price data objects
            kwargs: Additional parameters for the indicator
        """
        self.symbol = symbol
        self.prices = prices
        self.params = kwargs

    @abstractmethod
    def calculate(self) -> Any:
        """
        Calculate the indicator values.
        Returns:
            Any: Raw indicator values (list, dict, etc.)
        """
        pass

    @abstractmethod
    def get_signals(self) -> List[Dict]:
        """
        Generate trading signals based on indicator logic.
        Returns:
            List[Dict]: List of signal dictionaries
        """
        pass

    def predict_trend(self) -> Dict[str, Any]:
        """
        Predict the trend based on indicator values.
        Returns:
            Dict[str, Any]: Trend direction and confidence
        """
        return {"trend": "neutral", "confidence": 0.0}

    def get_recommendation(self) -> str:
        """
        Generate a recommendation (Buy/Sell/Hold) based on current data.
        Returns:
            str: Recommendation string
        """
        return "No recommendation available." 