from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

"""
This implementation of the Stochastic Oscillator indicator includes:

1. A calculate() method that returns:
- %K values (the main stochastic line)
- %D values (the signal line, which is an SMA of %K)
2. Default parameters:
- %K period: 14 (lookback period for highest high and lowest low)
- %D period: 3 (SMA period for the signal line)
- Slowing period: 3 (smoothing for %K)
3. A get_signals() method that identifies:
- Overbought and oversold conditions
- Bullish and bearish crossovers
- Crossovers in overbought/oversold territory (stronger signals)
- Exits from overbought/oversold conditions
4. A get_divergence_signals() method that identifies:
- Bearish divergence: Price makes higher high but Stochastic makes lower high
- Bullish divergence: Price makes lower low but Stochastic makes higher low
5. Proper handling of the initial period values with None values

The Stochastic Oscillator is a momentum indicator that compares a security's closing price to its price range over a specific period. It's particularly useful for identifying potential reversal points, overbought/oversold conditions, and divergences.

Tín hiệu lực mua/bán của Stochastic Oscillator:
- Dải giá trị: 0-100
- <PERSON><PERSON><PERSON> mua cao/bán yếu: %K và %D > 80 (vùng quá mua)
  + <PERSON><PERSON><PERSON> động: Cảnh giác với khả năng đảo chiều giảm, cân nhắc bán
- Lực bán cao/mua yếu: %K và %D < 20 (vùng quá bán)
  + Hành động: Cảnh giác với khả năng đảo chiều tăng, cân nhắc mua
- Tín hiệu mua mạnh: %K cắt lên %D trong vùng quá bán
  + Hành động: Tín hiệu mua với xác suất cao
- Tín hiệu bán mạnh: %K cắt xuống %D trong vùng quá mua
  + Hành động: Tín hiệu bán với xác suất cao
"""


class StochasticOscillator(BaseIndicator):
    def __init__(
        self,
        symbol: str,
        prices: List[PriceData],
        k_period: int = 14,
        d_period: int = 3,
        slowing: int = 3,
    ):
        super().__init__(symbol, prices, k_period=k_period, d_period=d_period, slowing=slowing)
        self.k_period = k_period
        self.d_period = d_period
        self.slowing = slowing

    def calculate(self) -> Dict[str, List[float]]:
        """
        Calculate Stochastic Oscillator values (%K and %D).

        Returns:
            dict: Dictionary containing %K and %D values
        """
        if len(self.prices) <= self.k_period:
            return {
                "k_values": [50.0] * len(self.prices),
                "d_values": [50.0] * len(self.prices),
            }

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Extract price data
        close_prices = [price.close_price for price in sorted_prices]
        high_prices = [price.highest_price for price in sorted_prices]
        low_prices = [price.lowest_price for price in sorted_prices]

        # Calculate raw %K values
        raw_k_values = []

        # First (k_period - 1) values are None
        for i in range(self.k_period - 1):
            raw_k_values.append(None)

        # Calculate %K for each window
        for i in range(self.k_period - 1, len(sorted_prices)):
            window_high = max(high_prices[i - (self.k_period - 1) : i + 1])
            window_low = min(low_prices[i - (self.k_period - 1) : i + 1])

            if window_high == window_low:
                # Avoid division by zero
                raw_k_values.append(50.0)
            else:
                # %K = (Current Close - Lowest Low) / (Highest High - Lowest Low) * 100
                k_value = (
                    (close_prices[i] - window_low) / (window_high - window_low)
                ) * 100
                raw_k_values.append(k_value)

        # Apply slowing to %K (if slowing > 1)
        k_values = []

        # First (k_period + slowing - 2) values are None
        for i in range(self.k_period + self.slowing - 2):
            k_values.append(None)

        # Calculate slowed %K
        for i in range(self.k_period + self.slowing - 2, len(sorted_prices)):
            # Average of last 'slowing' raw %K values
            slowing_window = raw_k_values[i - (self.slowing - 1) : i + 1]
            slowing_window = [k for k in slowing_window if k is not None]

            if not slowing_window:
                k_values.append(None)
            else:
                k_values.append(sum(slowing_window) / len(slowing_window))

        # Calculate %D (SMA of %K)
        d_values = []

        # First (k_period + slowing + d_period - 3) values are None
        for i in range(self.k_period + self.slowing + self.d_period - 3):
            d_values.append(None)

        # Calculate %D
        for i in range(
            self.k_period + self.slowing + self.d_period - 3, len(sorted_prices)
        ):
            # SMA of last 'd_period' %K values
            d_window = k_values[i - (self.d_period - 1) : i + 1]
            d_window = [k for k in d_window if k is not None]

            if not d_window:
                d_values.append(None)
            else:
                d_values.append(sum(d_window) / len(d_window))

        # Ensure k_values and d_values have the same length as prices
        k_values_full = [None] * (len(sorted_prices) - len(k_values)) + k_values
        d_values_full = [None] * (len(sorted_prices) - len(d_values)) + d_values

        return {"k_values": k_values_full, "d_values": d_values_full}

    def get_signals(self, overbought: float = 80, oversold: float = 20) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị Stochastic Oscillator.
        
        Args:
            overbought (float): Ngưỡng quá mua (mặc định 80)
            oversold (float): Ngưỡng quá bán (mặc định 20)
        
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        result = self.calculate()
        k_values = result["k_values"]
        d_values = result["d_values"]
        
        signals = []
        
        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        # Xác định tín hiệu
        for i in range(1, len(sorted_prices)):
            # Bỏ qua nếu không có giá trị hợp lệ
            if (i >= len(k_values) or i >= len(d_values) or 
                k_values[i] is None or d_values[i] is None or 
                k_values[i-1] is None or d_values[i-1] is None):
                continue
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Vùng quá bán: cả %K và %D dưới ngưỡng quá bán
            if k_values[i] <= oversold and d_values[i] <= oversold:
                # Bullish crossover trong vùng quá bán (%K cắt lên %D khi ở vùng quá bán)
                if k_values[i-1] <= d_values[i-1] and k_values[i] > d_values[i]:
                    signal_type = "bullish_crossover_oversold"
                    buying_power = "Lực mua tăng mạnh từ vùng quá bán"
                    action = "Mua với xác suất thành công cao"
                else:
                    signal_type = "oversold"
                    buying_power = "Lực bán cao, lực mua yếu, có thể xuất hiện đảo chiều"
                    action = "Chuẩn bị mua khi có tín hiệu xác nhận"
            
            # Vùng quá mua: cả %K và %D trên ngưỡng quá mua
            elif k_values[i] >= overbought and d_values[i] >= overbought:
                # Bearish crossover trong vùng quá mua (%K cắt xuống %D khi ở vùng quá mua)
                if k_values[i-1] >= d_values[i-1] and k_values[i] < d_values[i]:
                    signal_type = "bearish_crossover_overbought"
                    buying_power = "Lực bán tăng mạnh từ vùng quá mua"
                    action = "Bán với xác suất thành công cao"
                else:
                    signal_type = "overbought"
                    buying_power = "Lực mua cao, lực bán yếu, có thể xuất hiện đảo chiều"
                    action = "Chuẩn bị bán khi có tín hiệu xác nhận"
            
            # Bullish crossover thông thường (%K cắt lên %D)
            elif k_values[i-1] <= d_values[i-1] and k_values[i] > d_values[i]:
                signal_type = "bullish_crossover"
                buying_power = "Lực mua đang tăng"
                action = "Cân nhắc mua nếu có xác nhận từ chỉ báo khác"
            
            # Bearish crossover thông thường (%K cắt xuống %D)
            elif k_values[i-1] >= d_values[i-1] and k_values[i] < d_values[i]:
                signal_type = "bearish_crossover"
                buying_power = "Lực bán đang tăng"
                action = "Cân nhắc bán nếu có xác nhận từ chỉ báo khác"
            
            # Thoát khỏi vùng quá bán: %K vượt lên trên ngưỡng quá bán
            elif k_values[i-1] <= oversold and k_values[i] > oversold:
                signal_type = "exit_oversold"
                buying_power = "Lực mua đang phục hồi từ vùng quá bán"
                action = "Tín hiệu mua, đặc biệt nếu xu hướng chung là tăng"
            
            # Thoát khỏi vùng quá mua: %K giảm xuống dưới ngưỡng quá mua
            elif k_values[i-1] >= overbought and k_values[i] < overbought:
                signal_type = "exit_overbought"
                buying_power = "Lực bán đang phục hồi từ vùng quá mua"
                action = "Tín hiệu bán, đặc biệt nếu xu hướng chung là giảm"
            
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "k_value": round(k_values[i], 2),
                    "d_value": round(d_values[i], 2),
                    "signal": signal_type,
                    "buying_power": buying_power,
                    "action": action
                })
        
        return signals

    def get_divergence_signals(self) -> list[dict]:
        """
        Identify potential divergence signals between price and Stochastic Oscillator.

        Returns:
            list[dict]: List of divergence signals
        """
        result = self.calculate()
        k_values = result["k_values"]

        signals = []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Need at least 10 valid values to look for divergence
        valid_indices = [
            i
            for i in range(len(sorted_prices))
            if i >= self.k_period + self.slowing - 1 and k_values[i] is not None
        ]

        if len(valid_indices) < 10:
            return signals

        # Look for local highs and lows in price and Stochastic
        for i in range(2, len(valid_indices) - 2):
            idx = valid_indices[i]
            prev_idx = valid_indices[i - 2]

            # Check if we have a price high but Stochastic lower high (bearish divergence)
            if (
                sorted_prices[idx].close_price > sorted_prices[prev_idx].close_price
                and k_values[idx] < k_values[prev_idx]
                and k_values[idx] > 70
            ):  # Stochastic should be in overbought territory
                signals.append(
                    {
                        "timestamp": sorted_prices[idx].timestamp,
                        "price": sorted_prices[idx].close_price,
                        "signal": "bearish_divergence",
                        "k_value": k_values[idx],
                    }
                )

            # Check if we have a price low but Stochastic higher low (bullish divergence)
            elif (
                sorted_prices[idx].close_price < sorted_prices[prev_idx].close_price
                and k_values[idx] > k_values[prev_idx]
                and k_values[idx] < 30
            ):  # Stochastic should be in oversold territory
                signals.append(
                    {
                        "timestamp": sorted_prices[idx].timestamp,
                        "price": sorted_prices[idx].close_price,
                        "signal": "bullish_divergence",
                        "k_value": k_values[idx],
                    }
                )

        return signals

    def predict_trend(self) -> Dict[str, Any]:
        result = self.calculate()
        k_values = result["k_values"]
        latest_k = next((v for v in reversed(k_values) if v is not None), 0.0)
        if latest_k > 80:
            return {"trend": "overbought", "confidence": 1.0}
        elif latest_k < 20:
            return {"trend": "oversold", "confidence": 1.0}
        else:
            return {"trend": "neutral", "confidence": 0.5}

    def get_recommendation(self) -> str:
        result = self.calculate()
        k_values = result["k_values"]
        latest_k = next((v for v in reversed(k_values) if v is not None), 0.0)
        if latest_k > 80:
            return "Sell (Overbought)"
        elif latest_k < 20:
            return "Buy (Oversold)"
        else:
            return "Hold (Neutral)"
