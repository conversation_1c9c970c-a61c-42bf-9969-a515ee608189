from stockpal.core import PriceData
from .base import BaseIndicator
from typing import List, Dict, Any

"""
This implementation of the Average Directional Index (ADX) indicator includes:

1. A calculate() method that returns ADX, +DI, and -DI values
2. Proper handling of the initial period values
3. Calculation of True Range (TR), Directional Movement (+DM, -DM)
4. Smoothing of values using <PERSON>'s smoothing technique
5. A get_signals() method that identifies trend strength and direction
6. Support for customizing the period (default is 14)

The ADX indicator helps traders determine the strength of a trend, regardless of its direction. Values above 25 typically indicate a strong trend, while values below 20 suggest a weak or non-trending market.

Tín hiệu lực mua/bán của ADX:
- Dải giá trị: 0-100
- Xu hướng mạnh (ADX > 25):
  + Nếu +DI > -DI: Xu hướng tăng mạnh
    * Hành động: Ưu tiên mua và giữ
  + Nếu -DI > +DI: Xu hướng giảm mạnh
    * Hành động: Ưu tiên bán và không mua
- <PERSON> hướng yếu (ADX < 20):
  + Hành động: Th<PERSON>n trọng với các giao dịch theo xu hướng, c<PERSON> thể xem xét giao dịch trong biên độ
"""


class AverageDirectionalIndex(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        super().__init__(symbol, prices, period=period)
        self.period = period

    def calculate(self) -> Dict[str, List[Any]]:
        """
        Calculate ADX, +DI, and -DI values for the price data.

        Returns:
            dict: Dictionary containing ADX, +DI, and -DI values
        """
        if len(self.prices) <= self.period + 1:
            return {
                "adx": [50.0] * len(self.prices),
                "plus_di": [25.0] * len(self.prices),
                "minus_di": [25.0] * len(self.prices),
            }

        # Sort prices by timestamp in ascending order for calculation
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Calculate True Range (TR), +DM, and -DM
        tr_values = []
        plus_dm_values = []
        minus_dm_values = []

        for i in range(1, len(sorted_prices)):
            high = sorted_prices[i].highest_price
            low = sorted_prices[i].lowest_price
            prev_high = sorted_prices[i - 1].highest_price
            prev_low = sorted_prices[i - 1].lowest_price
            prev_close = sorted_prices[i - 1].close_price

            # True Range calculation
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            tr = max(tr1, tr2, tr3)
            tr_values.append(tr)

            # Directional Movement calculation
            up_move = high - prev_high
            down_move = prev_low - low

            plus_dm = max(0, up_move) if up_move > down_move else 0
            minus_dm = max(0, down_move) if down_move > up_move else 0

            plus_dm_values.append(plus_dm)
            minus_dm_values.append(minus_dm)

        # Calculate smoothed TR, +DM, and -DM
        smoothed_tr = [sum(tr_values[: self.period])]
        smoothed_plus_dm = [sum(plus_dm_values[: self.period])]
        smoothed_minus_dm = [sum(minus_dm_values[: self.period])]

        for i in range(self.period, len(tr_values)):
            smoothed_tr.append(
                smoothed_tr[-1] - (smoothed_tr[-1] / self.period) + tr_values[i]
            )
            smoothed_plus_dm.append(
                smoothed_plus_dm[-1]
                - (smoothed_plus_dm[-1] / self.period)
                + plus_dm_values[i]
            )
            smoothed_minus_dm.append(
                smoothed_minus_dm[-1]
                - (smoothed_minus_dm[-1] / self.period)
                + minus_dm_values[i]
            )

        # Calculate +DI and -DI
        plus_di_values = [
            100 * (dm / tr) if tr > 0 else 0
            for dm, tr in zip(smoothed_plus_dm, smoothed_tr)
        ]
        minus_di_values = [
            100 * (dm / tr) if tr > 0 else 0
            for dm, tr in zip(smoothed_minus_dm, smoothed_tr)
        ]

        # Calculate DX
        dx_values = [
            (
                100 * (abs(plus_di - minus_di) / (plus_di + minus_di))
                if (plus_di + minus_di) > 0
                else 0
            )
            for plus_di, minus_di in zip(plus_di_values, minus_di_values)
        ]

        # Calculate ADX (smoothed DX)
        adx_values = [None] * (self.period + 1)  # First values are None

        # First ADX value is average of first period DX values
        first_adx = sum(dx_values[: self.period]) / self.period
        adx_values.append(first_adx)

        # Calculate remaining ADX values
        for i in range(1, len(dx_values) - self.period + 1):
            adx = (
                adx_values[self.period + i] * (self.period - 1)
                + dx_values[self.period + i - 1]
            ) / self.period
            adx_values.append(adx)

        # Pad the beginning of +DI and -DI with None values to match ADX length
        plus_di_full = [None] * (
            len(sorted_prices) - len(plus_di_values)
        ) + plus_di_values
        minus_di_full = [None] * (
            len(sorted_prices) - len(minus_di_values)
        ) + minus_di_values
        adx_full = [None] * (len(sorted_prices) - len(adx_values)) + adx_values

        return {"adx": adx_full, "plus_di": plus_di_full, "minus_di": minus_di_full}

    def get_signals(self, threshold: float = 25) -> List[Dict[str, Any]]:
        """
        Xác định tín hiệu xu hướng dựa trên giá trị ADX, +DI và -DI.
        
        Args:
            threshold (float): Ngưỡng ADX cho xu hướng mạnh
        
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, ADX, +DI, -DI và hành động gợi ý
        """
        result = self.calculate()
        adx_values = result["adx"]
        plus_di_values = result["plus_di"]
        minus_di_values = result["minus_di"]
        
        signals = []
        
        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        # Lọc các giá trị hợp lệ
        valid_indices = [i for i, adx in enumerate(adx_values) if adx is not None]
        
        for i in valid_indices:
            if i >= len(sorted_prices):
                continue
            
            adx = adx_values[i]
            plus_di = plus_di_values[i]
            minus_di = minus_di_values[i]
            
            signal_type = ""
            trend_type = ""
            buying_power = ""
            action = ""
            
            if adx >= threshold:
                if plus_di > minus_di:
                    signal_type = "strong_uptrend"
                    trend_type = "strong_uptrend uptrend"
                    buying_power = "Xu hướng tăng mạnh, lực mua vượt trội"
                    action = "Ưu tiên mua và giữ"
                else:
                    signal_type = "strong_downtrend"
                    trend_type = "strong_downtrend downtrend"
                    buying_power = "Xu hướng giảm mạnh, lực bán vượt trội"
                    action = "Ưu tiên bán và không mua"
            else:
                if adx < 20:  # Xu hướng yếu
                    if plus_di > minus_di:
                        signal_type = "weak_uptrend"
                        trend_type = "weak_uptrend uptrend"
                        buying_power = "Xu hướng tăng yếu, lực mua nhẹ"
                        action = "Thận trọng khi mua, xem xét giao dịch trong biên độ"
                    else:
                        signal_type = "weak_downtrend"
                        trend_type = "weak_downtrend downtrend"
                        buying_power = "Xu hướng giảm yếu, lực bán nhẹ"
                        action = "Thận trọng khi bán, xem xét giao dịch trong biên độ"
                else:
                    if plus_di > minus_di:
                        signal_type = "moderate_uptrend"
                        trend_type = "moderate_uptrend uptrend"
                        buying_power = "Xu hướng tăng trung bình, lực mua tương đối"
                        action = "Có thể mua với quản lý rủi ro"
                    else:
                        signal_type = "moderate_downtrend"
                        trend_type = "moderate_downtrend downtrend"
                        buying_power = "Xu hướng giảm trung bình, lực bán tương đối"
                        action = "Có thể bán với quản lý rủi ro"
            
            signals.append({
                "timestamp": sorted_prices[i].timestamp,
                "price": sorted_prices[i].close_price,
                "adx": round(adx, 2),
                "plus_di": round(plus_di, 2),
                "minus_di": round(minus_di, 2),
                "signal": signal_type,
                "trend": trend_type,
                "buying_power": buying_power,
                "action": action
            })
        
        return signals

    def predict_trend(self) -> Dict[str, Any]:
        result = self.calculate()
        adx_values = result["adx"]
        plus_di_values = result["plus_di"]
        minus_di_values = result["minus_di"]
        latest_adx = next((v for v in reversed(adx_values) if v is not None), 0.0)
        latest_plus = next((v for v in reversed(plus_di_values) if v is not None), 0.0)
        latest_minus = next((v for v in reversed(minus_di_values) if v is not None), 0.0)
        if latest_adx > 25:
            if latest_plus > latest_minus:
                return {"trend": "strong_uptrend", "confidence": min(1.0, (latest_adx-25)/25)}
            else:
                return {"trend": "strong_downtrend", "confidence": min(1.0, (latest_adx-25)/25)}
        else:
            return {"trend": "sideways", "confidence": 1 - abs(latest_adx-20)/20}

    def get_recommendation(self) -> str:
        result = self.calculate()
        adx_values = result["adx"]
        plus_di_values = result["plus_di"]
        minus_di_values = result["minus_di"]
        latest_adx = next((v for v in reversed(adx_values) if v is not None), 0.0)
        latest_plus = next((v for v in reversed(plus_di_values) if v is not None), 0.0)
        latest_minus = next((v for v in reversed(minus_di_values) if v is not None), 0.0)
        if latest_adx > 25:
            if latest_plus > latest_minus:
                return "Buy (Strong Uptrend)"
            else:
                return "Sell (Strong Downtrend)"
        else:
            return "Hold (Sideways/Weak Trend)"
