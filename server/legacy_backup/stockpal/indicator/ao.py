from typing import List, Dict, Any, Optional
from stockpal.core import PriceData
from .base import BaseIndicator

"""
Awesome Oscillator (AO) là chỉ báo động lượng dựa trên sự chênh lệch giữa trung bình động của giá trung bình
của hai giai đoạn khác nhau. Chỉ báo này giúp xác định xu hướng và các điểm đảo chiều.

Tín hiệu lực mua/bán của Awesome Oscillator:
- Không có dải giá trị chuẩn: Tập trung vào đường 0, các mẫu hình và sự thay đổi màu sắc
- Vượt ngưỡng 0:
  + AO từ âm lên dương: Tín hiệu mua
  + AO từ dương xuống âm: Tín hiệu bán
- Mẫu hình Saucer:
  + Ba thanh cùng màu sau khi chuyển từ màu đối lập
- Mẫu hình Twin Peaks:
  + Hai đỉnh hoặc đáy với giá trị thứ hai thấp/cao hơn giá trị đầu tiên
"""


class AwesomeOscillator(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], fast_period: int = 5, slow_period: int = 34):
        """
        Khởi tạo chỉ báo Awesome Oscillator.

        Args:
            symbol: Mã chứng khoán
            prices: Danh sách dữ liệu giá
            fast_period: Khoảng thời gian ngắn (mặc định là 5)
            slow_period: Khoảng thời gian dài (mặc định là 34)
        """
        super().__init__(symbol, prices, fast_period=fast_period, slow_period=slow_period)
        self.fast_period = fast_period
        self.slow_period = slow_period

    def calculate(self) -> List[Optional[float]]:
        """
        Tính toán chỉ báo Awesome Oscillator.

        Returns:
            List[Optional[float]]: Danh sách giá trị Awesome Oscillator
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        if len(sorted_prices) < self.slow_period:
            return [None] * len(sorted_prices)
        
        # Tính giá trung bình của High và Low
        median_prices = [(price.highest_price + price.lowest_price) / 2 for price in sorted_prices]
        
        # Tính SMA nhanh và SMA chậm
        fast_sma = []
        slow_sma = []
        
        for i in range(len(median_prices)):
            if i >= self.fast_period - 1:
                fast_sma.append(sum(median_prices[i - self.fast_period + 1:i + 1]) / self.fast_period)
            else:
                fast_sma.append(None)
                
            if i >= self.slow_period - 1:
                slow_sma.append(sum(median_prices[i - self.slow_period + 1:i + 1]) / self.slow_period)
            else:
                slow_sma.append(None)
        
        # Tính toán Awesome Oscillator (AO)
        ao_values = []
        
        for i in range(len(sorted_prices)):
            if fast_sma[i] is not None and slow_sma[i] is not None:
                ao_values.append(fast_sma[i] - slow_sma[i])
            else:
                ao_values.append(None)
        
        return ao_values

    def get_signals(self) -> List[Dict[str, Any]]:
        """
        Xác định tín hiệu dựa trên giá trị Awesome Oscillator.
        
        Returns:
            List[Dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        ao_values = self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(2, len(sorted_prices)):
            if i >= len(ao_values) or ao_values[i] is None or ao_values[i-1] is None or ao_values[i-2] is None:
                continue
            
            current_ao = ao_values[i]
            prev_ao = ao_values[i-1]
            prev2_ao = ao_values[i-2]
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Xác định hướng của AO
            current_color = "green" if current_ao > prev_ao else "red"
            prev_color = "green" if prev_ao > prev2_ao else "red"
            
            # Tín hiệu xuyên qua đường 0
            if prev_ao < 0 and current_ao >= 0:
                signal_type = "zero_line_crossover"
                buying_power = "Lực mua tăng, xuyên qua đường 0 từ dưới lên"
                action = "Tín hiệu mua mạnh, xem xét mở vị thế mua"
                
            elif prev_ao > 0 and current_ao <= 0:
                signal_type = "zero_line_crossunder"
                buying_power = "Lực bán tăng, xuyên qua đường 0 từ trên xuống"
                action = "Tín hiệu bán mạnh, xem xét mở vị thế bán"
            
            # Mẫu hình Saucer
            # Saucer tăng: ba thanh màu xanh sau khi chuyển từ màu đỏ
            elif i >= 4 and current_color == "green" and prev_color == "green" and ao_values[i-3] > ao_values[i-2]:
                if current_ao > 0:  # Saucer trên đường 0
                    signal_type = "bullish_saucer_above_zero"
                    buying_power = "Lực mua mạnh, xác nhận xu hướng tăng"
                    action = "Tín hiệu mua mạnh, xem xét mở vị thế mua"
                else:  # Saucer dưới đường 0
                    signal_type = "bullish_saucer_below_zero"
                    buying_power = "Lực mua đang tăng, có thể đảo chiều xu hướng"
                    action = "Tín hiệu mua trung bình, theo dõi xác nhận"
            
            # Saucer giảm: ba thanh màu đỏ sau khi chuyển từ màu xanh
            elif i >= 4 and current_color == "red" and prev_color == "red" and ao_values[i-3] < ao_values[i-2]:
                if current_ao < 0:  # Saucer dưới đường 0
                    signal_type = "bearish_saucer_below_zero"
                    buying_power = "Lực bán mạnh, xác nhận xu hướng giảm"
                    action = "Tín hiệu bán mạnh, xem xét mở vị thế bán"
                else:  # Saucer trên đường 0
                    signal_type = "bearish_saucer_above_zero"
                    buying_power = "Lực bán đang tăng, có thể đảo chiều xu hướng"
                    action = "Tín hiệu bán trung bình, theo dõi xác nhận"
            
            # Mẫu hình Twin Peaks (đôi đỉnh/đáy)
            # Tìm hai đỉnh trong 10 thanh gần nhất
            elif i >= 10:
                peaks = []
                for j in range(i-10, i+1):
                    if j < 2 or j >= len(ao_values) - 2 or ao_values[j] is None:
                        continue
                    
                    # Điểm được coi là đỉnh nếu cao hơn hai điểm trước và sau nó
                    if ao_values[j] > ao_values[j-1] and ao_values[j] > ao_values[j-2] and \
                       ao_values[j] > ao_values[j+1] and ao_values[j] > ao_values[j+2]:
                        peaks.append((j, ao_values[j]))
                
                # Nếu có hai đỉnh và cả hai đều cùng phía đường 0
                if len(peaks) >= 2:
                    last_two_peaks = sorted(peaks, key=lambda x: x[0])[-2:]
                    if all(peak[1] > 0 for peak in last_two_peaks) and last_two_peaks[1][1] < last_two_peaks[0][1]:
                        signal_type = "bearish_twin_peaks"
                        buying_power = "Lực bán tiềm ẩn, giảm sức mạnh xu hướng tăng"
                        action = "Cẩn trọng, có thể xuất hiện đảo chiều giảm"
                
                # Tìm hai đáy trong 10 thanh gần nhất
                troughs = []
                for j in range(i-10, i+1):
                    if j < 2 or j >= len(ao_values) - 2 or ao_values[j] is None:
                        continue
                    
                    # Điểm được coi là đáy nếu thấp hơn hai điểm trước và sau nó
                    if ao_values[j] < ao_values[j-1] and ao_values[j] < ao_values[j-2] and \
                       ao_values[j] < ao_values[j+1] and ao_values[j] < ao_values[j+2]:
                        troughs.append((j, ao_values[j]))
                
                # Nếu có hai đáy và cả hai đều cùng phía đường 0
                if len(troughs) >= 2:
                    last_two_troughs = sorted(troughs, key=lambda x: x[0])[-2:]
                    if all(trough[1] < 0 for trough in last_two_troughs) and last_two_troughs[1][1] > last_two_troughs[0][1]:
                        signal_type = "bullish_twin_troughs"
                        buying_power = "Lực mua tiềm ẩn, giảm sức mạnh xu hướng giảm"
                        action = "Có thể xuất hiện đảo chiều tăng, theo dõi xác nhận"
            
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "ao": round(current_ao, 4),
                    "signal": signal_type,
                    "buying_power": buying_power,
                    "action": action
                })
        
        return signals 

    def predict_trend(self) -> Dict[str, Any]:
        ao_values = self.calculate()
        latest_ao = next((v for v in reversed(ao_values) if v is not None), 0.0)
        if latest_ao > 0:
            return {"trend": "uptrend", "confidence": min(1.0, abs(latest_ao)/10)}
        elif latest_ao < 0:
            return {"trend": "downtrend", "confidence": min(1.0, abs(latest_ao)/10)}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        ao_values = self.calculate()
        latest_ao = next((v for v in reversed(ao_values) if v is not None), 0.0)
        if latest_ao > 0:
            return "Buy (AO Positive)"
        elif latest_ao < 0:
            return "Sell (AO Negative)"
        else:
            return "Hold (AO Neutral)" 