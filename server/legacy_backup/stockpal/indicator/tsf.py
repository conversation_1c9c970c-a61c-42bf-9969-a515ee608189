import numpy as np
from typing import List, Optional, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

class TimeSeriesForecast(BaseIndicator):
    """
    Time Series Forecast (TSF) indicator implementation.

    TSF fits a linear regression (least  squares) line over the last N closing prices
    and projects the value at the next bar. It is used to forecast the next price
    and identify trend direction.
    """
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        """
        Initialize the TSF indicator.

        Args:
            symbol (str): Stock symbol.
            prices (List[PriceData]): List of price data.
            period (int, optional): Lookback period for regression. Defaults to 14.
        """
        super().__init__(symbol, prices, period=period)
        self.period = period

    def calculate(self) -> List[Optional[float]]:
        """
        Calculate the TSF values for the price data.

        Returns:
            List[Optional[float]]: TSF forecast values for each price point (None if insufficient data).
        """
        if len(self.prices) < self.period:
            return [None] * len(self.prices)

        close_prices = [p.close_price for p in sorted(self.prices, key=lambda x: x.timestamp)]
        tsf_values: List[Optional[float]] = [None] * (self.period - 1)

        for i in range(self.period - 1, len(close_prices)):
            y = np.array(close_prices[i - self.period + 1 : i + 1])
            x = np.arange(self.period)
            # Linear regression: y = a * x + b
            a, b = np.polyfit(x, y, 1)
            forecast = a * (self.period - 1) + b  # Forecast at the last x
            tsf_values.append(forecast)
        return tsf_values

    def get_signals(self) -> List[Dict]:
        """
        Generate trading signals based on TSF slope and forecast direction.

        Returns:
            List[Dict]: List of signals with timestamp, forecast, and signal type.
        """
        tsf_values = self.calculate()
        signals: List[Dict] = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        for i in range(self.period, len(sorted_prices)):
            prev_tsf = tsf_values[i - 1]
            curr_tsf = tsf_values[i]
            if prev_tsf is None or curr_tsf is None:
                continue
            signal_type = "neutral"
            if curr_tsf > prev_tsf:
                signal_type = "bullish"
            elif curr_tsf < prev_tsf:
                signal_type = "bearish"
            signals.append({
                "timestamp": sorted_prices[i].timestamp,
                "forecast": curr_tsf,
                "signal": signal_type
            })
        return signals

    def predict_trend(self) -> Dict[str, Any]:
        tsf_values = self.calculate()
        latest_tsf = next((v for v in reversed(tsf_values) if v is not None), 0.0)
        if latest_tsf > 0:
            return {"trend": "uptrend", "confidence": 1.0}
        elif latest_tsf < 0:
            return {"trend": "downtrend", "confidence": 1.0}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        tsf_values = self.calculate()
        latest_tsf = next((v for v in reversed(tsf_values) if v is not None), 0.0)
        if latest_tsf > 0:
            return "Buy (TSF Positive)"
        elif latest_tsf < 0:
            return "Sell (TSF Negative)"
        else:
            return "Hold (TSF Neutral)" 