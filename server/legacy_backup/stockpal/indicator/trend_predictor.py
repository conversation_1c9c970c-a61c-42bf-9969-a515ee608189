from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

from stockpal.core import PriceData
from stockpal.indicator.adx import AverageDirectionalIndex
from stockpal.indicator.bb import BollingerBands
from stockpal.indicator.cci import CommodityChannelIndex
from stockpal.indicator.ma import MovingAverage
from stockpal.indicator.macd import MACD
from stockpal.indicator.obv import OnBalanceVolume
from stockpal.indicator.rsi import RelativeStrengthIndex
from stockpal.indicator.stoch import StochasticOscillator
from stockpal.indicator.tsf import TimeSeriesForecast


class TrendPredictor:
    """
    Combines multiple technical indicators to predict trends and price targets.

    This class integrates various indicators to:
    1. Determine the current trend direction and strength
    2. Identify potential price targets for buying or selling
    3. Generate trading signals with confidence levels
    4. Provide support and resistance levels
    5. Calculate Fibonacci retracement levels
    6. Analyze volume profile for key price levels
    7. Suggest position sizing based on volatility
    8. Backtest the strategy with historical data
    9. Provide adaptive indicator weighting based on market conditions
    10. Detect divergences between price and indicators
    """

    def __init__(
        self, symbol: str, prices: list[PriceData], risk_percentage: float = 2.0, language: str = "en"
    ):
        self.symbol = symbol
        self.prices = prices
        self.risk_percentage = risk_percentage  # Portfolio risk percentage
        self.language = language.lower()  # 'en' for English, 'vi' for Vietnamese

        # Initialize all indicators
        self.adx = AverageDirectionalIndex(symbol, prices)
        self.bb = BollingerBands(symbol, prices)
        self.cci = CommodityChannelIndex(symbol, prices)
        self.ma_short = MovingAverage(symbol, prices, period=9)
        self.ma_medium = MovingAverage(symbol, prices, period=20)
        self.ma_long = MovingAverage(symbol, prices, period=50)
        self.macd = MACD(symbol, prices)
        self.obv = OnBalanceVolume(symbol, prices)
        self.rsi = RelativeStrengthIndex(symbol, prices)
        self.stoch = StochasticOscillator(symbol, prices)
        self.tsf = TimeSeriesForecast(symbol, prices)

        # Language dictionaries for trend directions
        self.direction_labels = {
            "en": {
                "bullish": "bullish",
                "bearish": "bearish",
                "neutral": "neutral"
            },
            "vi": {
                "bullish": "tăng",
                "bearish": "giảm",
                "neutral": "đi ngang"
            }
        }

        # Language dictionaries for strength labels
        self.strength_labels = {
            "en": {
                "strong": "strong",
                "moderate": "moderate",
                "weak": "weak"
            },
            "vi": {
                "strong": "mạnh",
                "moderate": "trung bình",
                "weak": "yếu"
            }
        }

        # Adaptive weights for different market conditions
        self._indicator_weights = {
            "trending": {
                "adx": 2.0,
                "ma_crossover": 2.0,
                "macd": 1.5,
                "obv": 1.5,
                "rsi": 0.8,
                "stoch": 0.8,
                "cci": 0.8,
                "tsf": 1.0
            },
            "ranging": {
                "adx": 0.8,
                "ma_crossover": 0.8,
                "macd": 1.0,
                "obv": 1.0,
                "rsi": 2.0,
                "stoch": 2.0,
                "cci": 1.5,
                "tsf": 1.0
            }
        }

    def predict_trend(self) -> dict:
        """
        Analyze current trend direction and strength.

        Returns:
            dict: Trend analysis with direction, strength, and confidence level
        """
        # Get signals from various indicators
        adx_signals = self.adx.get_signals()
        ma_crossovers = self.ma_short.get_price_crossover_signals()
        macd_signals = self.macd.get_crossover_signals()
        obv_signals = self.obv.get_signals()
        rsi_signals = self.rsi.get_signals()
        stoch_signals = self.stoch.get_signals()
        cci_signals = self.cci.get_signals()
        tsf_signals = self.tsf.get_signals()

        # Get the most recent price
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        latest_price = sorted_prices[-1].close_price if sorted_prices else None

        # Determine market condition (trending or ranging)
        latest_adx = adx_signals[-1] if adx_signals else None
        market_condition = "trending" if latest_adx and latest_adx["adx"] >= 25 else "ranging"
        weights = self._indicator_weights[market_condition]

        # Count bullish and bearish signals with adaptive weights
        bullish_count = 0
        bearish_count = 0

        # ADX trend strength
        if latest_adx:
            if "uptrend" in latest_adx["trend"]:
                bullish_count += weights["adx"]
            elif "downtrend" in latest_adx["trend"]:
                bearish_count += weights["adx"]

        # MA crossovers
        latest_ma_signal = ma_crossovers[-1] if ma_crossovers else None
        if latest_ma_signal:
            if latest_ma_signal["signal"] == "golden_cross":
                bullish_count += weights["ma_crossover"]
            elif latest_ma_signal["signal"] == "death_cross":
                bearish_count += weights["ma_crossover"]

        # MACD signals
        latest_macd_signal = macd_signals[-1] if macd_signals else None
        if latest_macd_signal:
            if latest_macd_signal["signal"] == "bullish_crossover":
                bullish_count += weights["macd"]
            elif latest_macd_signal["signal"] == "bearish_crossover":
                bearish_count += weights["macd"]

        # OBV signals
        latest_obv_signal = obv_signals[-1] if obv_signals else None
        if latest_obv_signal:
            if "bullish" in latest_obv_signal["signal"]:
                bullish_count += weights["obv"]
            elif "bearish" in latest_obv_signal["signal"]:
                bearish_count += weights["obv"]

        # RSI signals
        if rsi_signals:
            latest_rsi_signal = rsi_signals[-1]
            if latest_rsi_signal.get("signal") in ["oversold", "enter_oversold"]:
                bullish_count += weights["rsi"]
            if latest_rsi_signal.get("signal") in ["overbought", "enter_overbought"]:
                bearish_count += weights["rsi"]

        # Stochastic signals
        latest_stoch = stoch_signals[-1] if stoch_signals else None
        if latest_stoch:
            if latest_stoch["signal"] == "oversold":
                bullish_count += weights["stoch"]
            elif latest_stoch["signal"] == "overbought":
                bearish_count += weights["stoch"]

        # CCI signals
        latest_cci = cci_signals[-1] if cci_signals else None
        if latest_cci:
            if latest_cci["signal"] == "oversold":
                bullish_count += weights["cci"]
            elif latest_cci["signal"] == "overbought":
                bearish_count += weights["cci"]

        # TSF signals
        latest_tsf_signal = tsf_signals[-1] if tsf_signals else None
        if latest_tsf_signal:
            if latest_tsf_signal["signal"] == "bullish":
                bullish_count += weights["tsf"]
            elif latest_tsf_signal["signal"] == "bearish":
                bearish_count += weights["tsf"]

        # Check for divergences (additional confirmation)
        price_rsi_divergence = self._check_divergence("rsi")
        price_macd_divergence = self._check_divergence("macd")

        if price_rsi_divergence == "bullish":
            bullish_count += 1.5
        elif price_rsi_divergence == "bearish":
            bearish_count += 1.5

        if price_macd_divergence == "bullish":
            bullish_count += 1.5
        elif price_macd_divergence == "bearish":
            bearish_count += 1.5

        # Calculate trend direction and confidence
        total_signals = bullish_count + bearish_count
        if total_signals == 0:
            raw_direction = "neutral"
            confidence = 0
        elif bullish_count > bearish_count:
            raw_direction = "bullish"
            confidence = (bullish_count / total_signals) * 100
        else:
            raw_direction = "bearish"
            confidence = (bearish_count / total_signals) * 100

        # Get localized direction
        direction = self.direction_labels.get(self.language, self.direction_labels["en"]).get(raw_direction)

        # Determine trend strength based on both ADX and signal dominance
        # This ensures better alignment between strength and confidence metrics
        raw_strength = "weak"

        # Calculate the signal dominance ratio to influence strength
        signal_dominance = abs(bullish_count - bearish_count) / max(total_signals, 1)

        # Combine ADX and signal dominance for strength determination
        if latest_adx:
            adx_value = latest_adx["adx"]
            if adx_value >= 40 or (adx_value >= 25 and signal_dominance >= 0.7):
                raw_strength = "strong"
            elif adx_value >= 20 or (adx_value >= 15 and signal_dominance >= 0.6):
                raw_strength = "moderate"
            # Otherwise, it remains "weak"
        else:
            # If no ADX, use only signal dominance
            if signal_dominance >= 0.7:
                raw_strength = "strong"
            elif signal_dominance >= 0.5:
                raw_strength = "moderate"
            # Otherwise, it remains "weak"

        # Get localized strength
        strength = self.strength_labels.get(self.language, self.strength_labels["en"]).get(raw_strength)

        # Market condition should align with strength and ADX
        if raw_strength == "strong":
            market_condition = "trending"
        elif raw_strength == "weak" and (not latest_adx or latest_adx["adx"] < 15):
            market_condition = "ranging"
        # For moderate strength, we keep the existing market_condition determination

        return {
            "timestamp": datetime.now(),
            "symbol": self.symbol,
            "direction": direction,
            "raw_direction": raw_direction,  # Keep raw direction for internal use
            "strength": strength,
            "raw_strength": raw_strength,  # Keep raw strength for internal use
            "confidence": confidence,
            "current_price": latest_price,
            "bullish_signals": bullish_count,
            "bearish_signals": bearish_count,
            "market_condition": market_condition,
            "divergences": {
                "rsi": self._check_divergence("rsi"),
                "macd": self._check_divergence("macd")
            }
        }

    def _check_divergence(self, indicator_type: str) -> Optional[str]:
        """
        Check for divergence between price and indicator.

        Args:
            indicator_type: Type of indicator to check (rsi, macd)

        Returns:
            str: Type of divergence ("bullish", "bearish", or None)
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        if len(sorted_prices) < 20:
            return None

        # Get recent price highs and lows
        recent_prices = sorted_prices[-20:]

        # Determine which price attributes to use
        if all(hasattr(p, 'highest_price') and hasattr(p, 'lowest_price') for p in recent_prices[:5]):
            price_highs = [p.highest_price for p in recent_prices]
            price_lows = [p.lowest_price for p in recent_prices]
        elif all(hasattr(p, 'high_price') and hasattr(p, 'low_price') for p in recent_prices[:5]):
            price_highs = [p.high_price for p in recent_prices]
            price_lows = [p.low_price for p in recent_prices]
        else:
            # We don't have the necessary price attributes
            return None

        # Make sure we have enough valid values
        if len(price_highs) < 5 or len(price_lows) < 5:
            return None

        # Filter out any None values
        price_highs = [p for p in price_highs if p is not None]
        price_lows = [p for p in price_lows if p is not None]

        if len(price_highs) < 5 or len(price_lows) < 5:
            return None

        # Find two recent price highs and lows
        price_high_indices = sorted(range(len(price_highs)), key=lambda i: price_highs[i], reverse=True)[:2]
        price_low_indices = sorted(range(len(price_lows)), key=lambda i: price_lows[i])[:2]

        if len(price_high_indices) < 2 or len(price_low_indices) < 2:
            return None

        if indicator_type == "rsi":
            rsi_values = self.rsi.calculate()

            # Make sure we have enough RSI values
            if not rsi_values or len(rsi_values) < 20:
                return None

            rsi_values = rsi_values[-20:]

            # Filter out None values
            if None in rsi_values:
                return None

            # Ensure indices are within bounds
            for idx in price_high_indices + price_low_indices:
                if idx >= len(rsi_values):
                    return None

            # Check for bearish divergence (price higher high, RSI lower high)
            if price_high_indices[0] > price_high_indices[1]:  # More recent high is higher
                if rsi_values[price_high_indices[0]] < rsi_values[price_high_indices[1]]:
                    return "bearish"

            # Check for bullish divergence (price lower low, RSI higher low)
            if price_low_indices[0] > price_low_indices[1]:  # More recent low is lower
                if rsi_values[price_low_indices[0]] > rsi_values[price_low_indices[1]]:
                    return "bullish"

        elif indicator_type == "macd":
            macd_values = self.macd.calculate()

            # Validate MACD data
            if not macd_values or "macd_line" not in macd_values or not macd_values["macd_line"]:
                return None

            macd_line = macd_values["macd_line"][-20:] if len(macd_values["macd_line"]) >= 20 else None

            if not macd_line:
                return None

            # Filter out None values
            if None in macd_line:
                return None

            # Ensure indices are within bounds
            for idx in price_high_indices + price_low_indices:
                if idx >= len(macd_line):
                    return None

            # Check for bearish divergence (price higher high, MACD lower high)
            if price_high_indices[0] > price_high_indices[1]:  # More recent high is higher
                if macd_line[price_high_indices[0]] < macd_line[price_high_indices[1]]:
                    return "bearish"

            # Check for bullish divergence (price lower low, MACD higher low)
            if price_low_indices[0] > price_low_indices[1]:  # More recent low is lower
                if macd_line[price_low_indices[0]] > macd_line[price_low_indices[1]]:
                    return "bullish"

        return None

    def calculate_price_targets(self) -> dict:
        """
        Calculate price targets based on current trend and technical levels.

        Returns:
            dict: Dictionary with support, resistance, and price targets
        """
        # First get the current trend
        trend = self.predict_trend()

        # Current price
        current_price = trend.get("current_price", 0)
        if not current_price:
            return {"error": "No current price available"}

        # Determine market condition - use the updated market condition from trend prediction
        market_condition = trend.get("market_condition", "ranging")

        # Make prediction more accurate by incorporating trend metrics more directly
        trend_direction = trend.get("raw_direction", "neutral")
        trend_strength = trend.get("raw_strength", "weak")
        confidence = trend.get("confidence", 0)

        # Get indicator values
        bb_result = self.bb.calculate()
        rsi_values = self.rsi.calculate()

        # Calculate pivot points
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Get high, low, close for previous period
        if len(sorted_prices) >= 6:
            high_values = [p.high_price for p in sorted_prices[-6:-1] if hasattr(p, 'high_price')]
            low_values = [p.low_price for p in sorted_prices[-6:-1] if hasattr(p, 'low_price')]

            if high_values and low_values:  # Verify these arrays are not empty
                prev_high = max(high_values)
                prev_low = min(low_values)
                prev_close = sorted_prices[-2].close_price if len(sorted_prices) > 1 else current_price
            else:
                prev_high = prev_low = prev_close = current_price
        else:
            prev_high = prev_low = prev_close = current_price

        # Calculate pivot levels
        pivot = (prev_high + prev_low + prev_close) / 3
        s1 = 2 * pivot - prev_high
        s2 = pivot - (prev_high - prev_low)
        r1 = 2 * pivot - prev_low
        r2 = pivot + (prev_high - prev_low)

        # Get Bollinger Bands values
        upper_band = bb_result["upper_band"][-1] if "upper_band" in bb_result and bb_result["upper_band"] else current_price * 1.05
        middle_band = bb_result["middle_band"][-1] if "middle_band" in bb_result and bb_result["middle_band"] else current_price
        lower_band = bb_result["lower_band"][-1] if "lower_band" in bb_result and bb_result["lower_band"] else current_price * 0.95

        # Get RSI value
        latest_rsi = rsi_values[-1] if rsi_values else 50

        # Calculate support and resistance levels from various methods for better accuracy
        support_levels = []
        resistance_levels = []

        # Add pivot points
        support_levels.extend([s1, s2])
        resistance_levels.extend([r1, r2])

        # Add Bollinger Bands
        support_levels.append(lower_band)
        resistance_levels.append(upper_band)

        # Add moving averages
        ma_periods = [9, 20, 50, 100, 200]
        for period in ma_periods:
            ma = MovingAverage(self.symbol, self.prices, period=period)
            ma_values = ma.calculate()
            if ma_values and ma_values[-1] is not None:
                ma_value = ma_values[-1]
                if ma_value < current_price:
                    support_levels.append(ma_value)
                else:
                    resistance_levels.append(ma_value)

        # Add historical highs and lows
        if len(sorted_prices) > 30:
            recent_prices = sorted_prices[-30:]
            local_highs = []
            local_lows = []

            # Find local highs and lows
            for i in range(1, len(recent_prices) - 1):
                # Check if we have high_price attribute
                if hasattr(recent_prices[i], 'high_price'):
                    if recent_prices[i].high_price > recent_prices[i-1].high_price and recent_prices[i].high_price > recent_prices[i+1].high_price:
                        local_highs.append(recent_prices[i].high_price)

                # Check if we have low_price attribute
                if hasattr(recent_prices[i], 'low_price'):
                    if recent_prices[i].low_price < recent_prices[i-1].low_price and recent_prices[i].low_price < recent_prices[i+1].low_price:
                        local_lows.append(recent_prices[i].low_price)

            # Add significant levels
            if local_highs:
                resistance_levels.extend(local_highs)
            if local_lows:
                support_levels.extend(local_lows)

        # Ensure support and resistance levels are not empty
        if not support_levels:
            support_levels = [current_price * 0.95, current_price * 0.9]
        if not resistance_levels:
            resistance_levels = [current_price * 1.05, current_price * 1.1]

        # Clean and sort levels
        support_levels = sorted(set([round(level, 2) for level in support_levels if level < current_price * 0.98]))
        resistance_levels = sorted(set([round(level, 2) for level in resistance_levels if level > current_price * 1.02]))

        # If still empty after filtering, add default levels
        if not support_levels:
            support_levels = [round(current_price * 0.95, 2), round(current_price * 0.9, 2)]
        if not resistance_levels:
            resistance_levels = [round(current_price * 1.05, 2), round(current_price * 1.1, 2)]

        # Calculate price targets based on trend direction and strength
        targets = {
            "buy_targets": [],
            "sell_targets": [],
            "stop_loss_levels": [],
            "take_profit_levels": []
        }

        # Stronger weight for trend direction, strength and confidence
        # The buy, sell and stop loss targets should be directly aligned with trend

        if trend_direction == "bullish":
            # Buy targets are below current price in bullish trend
            buy_factor = 0.95 if trend_strength == "strong" else 0.92 if trend_strength == "moderate" else 0.9
            buy_target = current_price * buy_factor

            # Find closest support level below buy_target
            closest_support = None
            min_distance = float('inf')
            for level in support_levels:
                if abs(level - buy_target) < min_distance:
                    min_distance = abs(level - buy_target)
                    closest_support = level

            # Add targets with confidence-adjusted factors
            confidence_adj = 1 + (confidence / 200)  # Scales from 1.0 to 1.5 based on confidence

            if closest_support:
                targets["buy_targets"].append({
                    "price": closest_support,
                    "confidence": "high",
                    "reason": "Support level near buy target"
                })

            # Add another buy target slightly below
            second_buy = buy_target * 0.97
            targets["buy_targets"].append({
                "price": second_buy,
                "confidence": "medium",
                "reason": "Secondary buy zone"
            })

            # Set stop loss below key support level
            if support_levels:
                stop_loss = min(support_levels) * 0.98  # 2% below lowest support
                targets["stop_loss_levels"].append({
                    "price": stop_loss,
                    "confidence": "medium",
                    "reason": "Below major support level"
                })

            # Set take profit near resistance levels
            if resistance_levels:
                for i, level in enumerate(resistance_levels[:2]):  # Top 2 resistance levels
                    confidence = "high" if i == 0 else "medium"
                    targets["take_profit_levels"].append({
                        "price": level,
                        "confidence": confidence,
                        "reason": f"Resistance level {i+1}"
                    })

        elif trend_direction == "bearish":
            # Sell targets are rallies in bearish trend
            sell_factor = 1.05 if trend_strength == "strong" else 1.08 if trend_strength == "moderate" else 1.1
            sell_target = current_price * sell_factor

            # Find closest resistance level above sell_target
            closest_resistance = None
            min_distance = float('inf')
            for level in resistance_levels:
                if abs(level - sell_target) < min_distance:
                    min_distance = abs(level - sell_target)
                    closest_resistance = level

            # Add targets with confidence-adjusted factors
            confidence_adj = 1 + (confidence / 200)  # Scales from 1.0 to 1.5 based on confidence

            if closest_resistance:
                targets["sell_targets"].append({
                    "price": closest_resistance,
                    "confidence": "high",
                    "reason": "Resistance level near sell target"
                })

            # Add another sell target slightly above
            second_sell = sell_target * 1.03
            targets["sell_targets"].append({
                "price": second_sell,
                "confidence": "medium",
                "reason": "Secondary sell zone"
            })

            # Set stop loss above key resistance level
            if resistance_levels:
                stop_loss = max(resistance_levels) * 1.02  # 2% above highest resistance
                targets["stop_loss_levels"].append({
                    "price": stop_loss,
                    "confidence": "medium",
                    "reason": "Above major resistance level"
                })

            # Set take profit near support levels
            if support_levels:
                for i, level in enumerate(reversed(support_levels[:2])):  # Bottom 2 support levels
                    confidence = "high" if i == 0 else "medium"
                    targets["take_profit_levels"].append({
                        "price": level,
                        "confidence": confidence,
                        "reason": f"Support level {i+1}"
                    })

        else:  # neutral trend
            # In ranging markets, buy near support and sell near resistance
            if support_levels:
                targets["buy_targets"].append({
                    "price": support_levels[0],
                    "confidence": "medium",
                    "reason": "Support in range-bound market"
                })

                # Conservative stop loss
                stop_price = support_levels[0] * 0.95
                targets["stop_loss_levels"].append({
                    "price": stop_price,
                    "confidence": "medium",
                    "reason": "Below support in range-bound market"
                })

            if resistance_levels:
                targets["sell_targets"].append({
                    "price": resistance_levels[0],
                    "confidence": "medium",
                    "reason": "Resistance in range-bound market"
                })

                # Take profit at resistance
                targets["take_profit_levels"].append({
                    "price": resistance_levels[0],
                    "confidence": "medium",
                    "reason": "Resistance level in range"
                })

        # Get Fibonacci levels for additional reference
        fib_levels = self._calculate_fibonacci_levels()

        # Calculate volatility for potential position sizing
        volatility = 0
        if len(sorted_prices) >= 20:
            returns = [p.close_price / sorted_prices[i].close_price - 1 for i, p in enumerate(sorted_prices[1:20])]
            volatility = np.std(returns) * 100  # Volatility as percentage

        return {
            "timestamp": datetime.now(),
            "symbol": self.symbol,
            "current_price": current_price,
            "market_condition": market_condition,
            "support_levels": support_levels,
            "resistance_levels": resistance_levels,
            "targets": targets,
            "fibonacci_levels": fib_levels,
            "volatility": volatility,
            "rsi": latest_rsi
        }

    def generate_trading_signals(self) -> dict:
        """
        Generate trading signals with confidence levels.

        Returns:
            dict: Trading signals with action, confidence, and price targets
        """
        trend = self.predict_trend()
        price_targets = self.calculate_price_targets()

        # Action labels based on language
        action_labels = {
            "en": {"buy": "buy", "sell": "sell", "hold": "hold"},
            "vi": {"buy": "mua", "sell": "bán", "hold": "nắm giữ"}
        }

        # Default values
        raw_action = "hold"
        confidence = 0
        entry_price = None
        target_price = None
        stop_loss = None

        current_price = trend.get("current_price")

        # Determine action based on trend and confidence
        if trend["raw_direction"] == "bullish" and trend["confidence"] > 60:
            raw_action = "buy"
            confidence = trend["confidence"]

            # Set entry price (current price or slightly above for breakout)
            entry_price = current_price

            # Set target price (nearest resistance)
            if price_targets["resistance_levels"]:
                # Check if resistance_levels contains dictionaries with 'price' key or just float values
                if isinstance(price_targets["resistance_levels"][0], dict) and "price" in price_targets["resistance_levels"][0]:
                    target_price = price_targets["resistance_levels"][0]["price"]
                else:
                    # If it's a float, use it directly
                    target_price = price_targets["resistance_levels"][0]

            # Set stop loss (nearest support)
            if price_targets["support_levels"]:
                # Check if support_levels contains dictionaries with 'price' key or just float values
                if isinstance(price_targets["support_levels"][0], dict) and "price" in price_targets["support_levels"][0]:
                    stop_loss = price_targets["support_levels"][0]["price"]
                else:
                    # If it's a float, use it directly
                    stop_loss = price_targets["support_levels"][0]

        elif trend["raw_direction"] == "bearish" and trend["confidence"] > 60:
            raw_action = "sell"
            confidence = trend["confidence"]

            # Set entry price (current price or slightly below for breakout)
            entry_price = current_price

            # Set target price (nearest support)
            if price_targets["support_levels"]:
                # Check if support_levels contains dictionaries with 'price' key or just float values
                if isinstance(price_targets["support_levels"][0], dict) and "price" in price_targets["support_levels"][0]:
                    target_price = price_targets["support_levels"][0]["price"]
                else:
                    # If it's a float, use it directly
                    target_price = price_targets["support_levels"][0]

            # Set stop loss (nearest resistance)
            if price_targets["resistance_levels"]:
                # Check if resistance_levels contains dictionaries with 'price' key or just float values
                if isinstance(price_targets["resistance_levels"][0], dict) and "price" in price_targets["resistance_levels"][0]:
                    stop_loss = price_targets["resistance_levels"][0]["price"]
                else:
                    # If it's a float, use it directly
                    stop_loss = price_targets["resistance_levels"][0]

        # Get localized action
        action = action_labels.get(self.language, action_labels["en"]).get(raw_action)

        # Signal description based on language
        signal_description = self._get_signal_description(raw_action, trend["raw_direction"], trend["confidence"])

        return {
            "timestamp": datetime.now(),
            "symbol": self.symbol,
            "action": action,
            "raw_action": raw_action,  # Keep raw action for internal use
            "confidence": confidence,
            "current_price": current_price,
            "entry_price": entry_price,
            "target_price": target_price,
            "stop_loss": stop_loss,
            "risk_reward_ratio": self._calculate_risk_reward(entry_price, target_price, stop_loss),
            "description": signal_description
        }

    def _get_signal_description(self, action, direction, confidence):
        """
        Generate a description for the trading signal based on the selected language.

        Args:
            action: The trading action (buy, sell, hold)
            direction: The trend direction
            confidence: The confidence score

        Returns:
            str: A description of the trading signal
        """
        if self.language == "vi":
            if action == "buy":
                if confidence > 80:
                    return f"Tín hiệu mua mạnh với độ tin cậy {confidence:.1f}%. Nên xem xét mua ở giá hiện tại."
                else:
                    return f"Tín hiệu mua với độ tin cậy {confidence:.1f}%. Nên xem xét mua ở vùng hỗ trợ."
            elif action == "sell":
                if confidence > 80:
                    return f"Tín hiệu bán mạnh với độ tin cậy {confidence:.1f}%. Nên xem xét bán ở giá hiện tại."
                else:
                    return f"Tín hiệu bán với độ tin cậy {confidence:.1f}%. Nên xem xét bán ở vùng kháng cự."
            else:
                return "Thị trường đang đi ngang. Nên chờ tín hiệu rõ ràng hơn."
        else:  # Default to English
            if action == "buy":
                if confidence > 80:
                    return f"Strong buy signal with {confidence:.1f}% confidence. Consider entering at current price."
                else:
                    return f"Moderate buy signal with {confidence:.1f}% confidence. Consider entering at support levels."
            elif action == "sell":
                if confidence > 80:
                    return f"Strong sell signal with {confidence:.1f}% confidence. Consider exiting at current price."
                else:
                    return f"Moderate sell signal with {confidence:.1f}% confidence. Consider exiting at resistance levels."
            else:
                return "Neutral market. Wait for clearer signals."

    def _calculate_risk_reward(
        self, entry: Optional[float], target: Optional[float], stop: Optional[float]
    ) -> Optional[float]:
        """Calculate risk-reward ratio for a trade"""
        if not all([entry, target, stop]) or entry == stop:
            return None

        reward = abs(target - entry)
        risk = abs(entry - stop)

        if risk == 0:
            return None

        return round(reward / risk, 2)

    def _calculate_fibonacci_levels(self) -> dict:
        """
        Calculate Fibonacci retracement levels based on recent price swings.

        Returns:
            dict: Fibonacci levels for support and resistance
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        if len(sorted_prices) < 10:  # Ensure we have enough data
            # Return empty structure with default values
            return {
                "support": [],
                "resistance": []
            }

        # Attempt to find recent swing high and low
        # First check if we have highest_price and lowest_price attributes
        has_high_low = all(hasattr(p, 'highest_price') and hasattr(p, 'lowest_price') for p in sorted_prices[-10:])

        if has_high_low:
            high_prices = [p.highest_price for p in sorted_prices[-30:] if hasattr(p, 'highest_price')]
            low_prices = [p.lowest_price for p in sorted_prices[-30:] if hasattr(p, 'lowest_price')]
        else:
            # Fall back to high_price and low_price if available
            has_alt_high_low = all(hasattr(p, 'high_price') and hasattr(p, 'low_price') for p in sorted_prices[-10:])

            if has_alt_high_low:
                high_prices = [p.high_price for p in sorted_prices[-30:] if hasattr(p, 'high_price')]
                low_prices = [p.low_price for p in sorted_prices[-30:] if hasattr(p, 'low_price')]
            else:
                # Last resort - use close_price as both high and low
                high_prices = [p.close_price for p in sorted_prices[-30:]]
                low_prices = [p.close_price for p in sorted_prices[-30:]]

        # Ensure we have data in the arrays
        if not high_prices or not low_prices:
            return {
                "support": [],
                "resistance": []
            }

        swing_high = max(high_prices)
        swing_low = min(low_prices)

        # If high and low are too close, calculations won't be meaningful
        if abs(swing_high - swing_low) / swing_low < 0.01:  # Less than 1% difference
            return {
                "support": [],
                "resistance": []
            }

        # Calculate Fibonacci levels
        diff = swing_high - swing_low
        fib_levels = {
            0: swing_low,
            0.236: swing_low + 0.236 * diff,
            0.382: swing_low + 0.382 * diff,
            0.5: swing_low + 0.5 * diff,
            0.618: swing_low + 0.618 * diff,
            0.786: swing_low + 0.786 * diff,
            1: swing_high,
        }

        current_price = sorted_prices[-1].close_price

        # Separate into support and resistance based on current price
        support_levels = []
        resistance_levels = []

        for level, price in fib_levels.items():
            if price < current_price:
                support_levels.append({"level": level, "price": price})
            elif price > current_price:
                resistance_levels.append({"level": level, "price": price})

        return {
            "support": sorted(support_levels, key=lambda x: x["price"], reverse=True),
            "resistance": sorted(resistance_levels, key=lambda x: x["price"]),
        }

    def _analyze_volume_profile(self) -> dict:
        """
        Analyze volume profile to identify key price levels.

        Returns:
            dict: Key support and resistance levels based on volume
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        if len(sorted_prices) < 10:  # Ensure we have enough data
            return {
                "support": [],
                "resistance": []
            }

        # Check if the prices have the necessary attributes
        has_volume = all(hasattr(p, 'match_volume') for p in sorted_prices[-10:])
        if not has_volume:
            return {
                "support": [],
                "resistance": []
            }

        # Create price bins
        all_prices = []
        for price in sorted_prices:
            # Check for highest_price and lowest_price
            if hasattr(price, 'highest_price') and hasattr(price, 'lowest_price'):
                all_prices.extend([price.lowest_price, price.highest_price, price.close_price])
            # Fall back to high_price and low_price
            elif hasattr(price, 'high_price') and hasattr(price, 'low_price'):
                all_prices.extend([price.low_price, price.high_price, price.close_price])
            # Last resort - just use close_price
            else:
                all_prices.append(price.close_price)

        if not all_prices:  # Ensure we have prices to work with
            return {
                "support": [],
                "resistance": []
            }

        min_price = min(all_prices)
        max_price = max(all_prices)

        # If min and max are the same, can't create meaningful bins
        if min_price == max_price:
            return {
                "support": [],
                "resistance": []
            }

        # Create 10 price bins
        bin_size = (max_price - min_price) / 10
        bins = [min_price + i * bin_size for i in range(11)]

        # Count volume in each price bin
        volume_profile = [0] * 10
        for price in sorted_prices:
            # Determine average price for this candle
            if hasattr(price, 'highest_price') and hasattr(price, 'lowest_price'):
                avg_price = (price.highest_price + price.lowest_price + price.close_price) / 3
            elif hasattr(price, 'high_price') and hasattr(price, 'low_price'):
                avg_price = (price.high_price + price.low_price + price.close_price) / 3
            else:
                avg_price = price.close_price

            bin_idx = min(int((avg_price - min_price) / bin_size), 9)
            volume_profile[bin_idx] += (
                price.match_volume if hasattr(price, 'match_volume') else 0
            )

        # Find high volume nodes (potential support/resistance)
        # Guard against all zeros
        if not any(volume_profile):
            return {
                "support": [],
                "resistance": []
            }

        avg_volume = sum(volume_profile) / len(volume_profile)
        high_volume_bins = []

        for i, volume in enumerate(volume_profile):
            if volume > avg_volume * 1.5:  # 50% above average
                high_volume_bins.append(i)

        # Convert bins to price levels
        high_volume_prices = [
            (min_price + (i + 0.5) * bin_size) for i in high_volume_bins
        ]

        current_price = sorted_prices[-1].close_price

        # Separate into support and resistance
        support_levels = []
        resistance_levels = []

        for price in high_volume_prices:
            if price < current_price:
                support_levels.append({"price": price})
            elif price > current_price:
                resistance_levels.append({"price": price})

        return {
            "support": sorted(support_levels, key=lambda x: x["price"], reverse=True),
            "resistance": sorted(resistance_levels, key=lambda x: x["price"]),
        }

    def _calculate_position_size(
        self,
        entry_price: Optional[float],
        stop_loss: Optional[float],
        portfolio_value: float = 100000,
    ) -> Optional[float]:
        """
        Calculate position size based on volatility and risk management.

        Args:
            entry_price: Entry price for the trade
            stop_loss: Stop loss price
            portfolio_value: Total portfolio value

        Returns:
            float: Recommended position size in number of shares
        """
        if not entry_price or not stop_loss or entry_price == stop_loss:
            return None

        # Calculate risk per share
        risk_per_share = abs(entry_price - stop_loss)

        # Calculate maximum risk amount based on portfolio value and risk percentage
        max_risk_amount = portfolio_value * (self.risk_percentage / 100)

        # Calculate position size
        position_size = max_risk_amount / risk_per_share

        # Round down to nearest whole number
        return int(position_size)

    def backtest_strategy(self, days: int = 30) -> dict:
        """
        Backtest the trading strategy over a specified number of days.

        Args:
            days: Number of days to backtest

        Returns:
            dict: Backtest results including win rate, profit/loss, etc.
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Ensure we have enough data
        if len(sorted_prices) < days + 10:  # Need extra
            return {"error": "Not enough data for backtesting"}

        # Initialize backtest results
        backtest_results = {
            "start_date": sorted_prices[0].timestamp,
            "end_date": sorted_prices[-1].timestamp,
            "total_days": len(sorted_prices),
            "trades": [],
            "win_rate": None,
            "profit_loss": None,
            "average_profit_loss": None,
        }

        # Generate trading signals for each day
        for i in range(days, len(sorted_prices)):
            # Get prices for the current day
            current_price = sorted_prices[i].close_price
            previous_prices = sorted_prices[i - days : i]

            # Create a temporary TrendPredictor instance for the current window
            temp_predictor = TrendPredictor(
                self.symbol, previous_prices, self.risk_percentage
            )

            # Generate trading signals
            signals = temp_predictor.generate_trading_signals()

            # Check if we have a valid signal
            if signals["action"] != "hold":
                # Execute the trade
                entry_price = signals["entry_price"]
                target_price = signals["target_price"]
                stop_loss = signals["stop_loss"]
                position_size = signals["position_size"]

                # Determine exit price
                exit_price = None
                for j in range(i + 1, len(sorted_prices)):
                    next_price = sorted_prices[j].close_price
                    if next_price >= target_price or next_price <= stop_loss:
                        exit_price = next_price
                        break

                # If no exit price found, use the last price in the dataset
                if not exit_price:
                    exit_price = sorted_prices[-1].close_price

                # Calculate profit/loss
                profit_loss = (exit_price - entry_price) * position_size
                is_win = profit_loss > 0

                # Append trade result
                backtest_results["trades"].append(
                    {
                        "date": sorted_prices[i].timestamp,
                        "action": signals["action"],
                        "entry_price": entry_price,
                        "exit_price": exit_price,
                        "target_price": target_price,
                        "stop_loss": stop_loss,
                        "position_size": position_size,
                        "profit_loss": profit_loss,
                        "is_win": is_win,
                    }
                )

        # Calculate backtest statistics
        if backtest_results["trades"]:
            backtest_results["win_rate"] = sum(
                trade["is_win"] for trade in backtest_results["trades"]
            ) / len(backtest_results["trades"])
            backtest_results["profit_loss"] = sum(
                trade["profit_loss"] for trade in backtest_results["trades"]
            )
            backtest_results["average_profit_loss"] = backtest_results[
                "profit_loss"
            ] / len(backtest_results["trades"])

        return backtest_results
