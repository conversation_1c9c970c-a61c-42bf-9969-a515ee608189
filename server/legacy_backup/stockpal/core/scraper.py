"""
Stock data scraper base class module.

This module provides the abstract base class for all stock data scrapers,
defining the common interface for fetching stock prices, events, quotes, and orders.
"""

import logging
from datetime import datetime, timedelta
from typing import List

from stockpal.core.util import Utils

from .stock import PriceData, Stock

# Configure module-specific logger
logger = logging.getLogger(__name__)


class Scraper:
    """
    Abstract base class for stock data scrapers.

    This class defines the common interface that all data provider scrapers
    must implement. It provides basic initialization and cache management
    functionality while requiring subclasses to implement specific data
    fetching methods.

    Attributes:
        _symbol (str): The stock symbol in uppercase format
        _cache_subfolder (str): Cache subdirectory name for this symbol
    """

    def __init__(self, symbol: str):
        """
        Initialize the scraper with a stock symbol.

        Args:
            symbol (str): Stock symbol to scrape data for

        Raises:
            ValueError: If symbol is empty or None
        """
        if not symbol or not symbol.strip():
            raise ValueError("Symbol cannot be empty or None")

        self._symbol = symbol.upper()
        self._cache_subfolder = symbol.upper()

        logger.info(f"Initialized scraper for symbol: {self._symbol}")

    def prices(self, timeframe_in_minute: bool = False) -> List[PriceData]:
        """
        Fetch price data for the stock symbol.

        Args:
            timeframe_in_minute (bool): If True, fetch minute-level data.
                                      If False, fetch daily data.

        Returns:
            List[PriceData]: List of price data objects

        Raises:
            NotImplementedError: Must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement prices() method")

    def events(self):
        """
        Fetch corporate events data for the stock symbol.

        Returns:
            List: List of event data objects

        Raises:
            NotImplementedError: Must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement events() method")

    def quote(self):
        """
        Fetch current quote/real-time data for the stock symbol.

        Returns:
            Quote object with current market data

        Raises:
            NotImplementedError: Must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement quote() method")

    def orders(self):
        """
        Fetch order book/trading data for the stock symbol.

        Returns:
            List of order/trade data objects

        Raises:
            NotImplementedError: Must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement orders() method")

    def fetch_stock_symbols(self) -> List[Stock]:
        """
        Fetch list of all available stock symbols from the data provider.

        Returns:
            List[Stock]: List of Stock objects with symbol information

        Raises:
            NotImplementedError: Must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement fetch_stock_symbols() method")

    def _get_time_ranges(self, next_days: int = 0) -> list[tuple[int, int]]:
        # Opening date is 2000-07-01 07:00:00 GMT+7 and convert to epoch seconds
        opening = int(datetime(2000, 7, 1, 7).timestamp())
        # opening = int(datetime(2024, 1, 1, 7).timestamp())

        time_ranges: list[tuple[int, int]] = []

        # Part 1: From opening to 2001-01-01 07:00:00 GMT+7
        start = int(datetime(2001, 1, 1, 7).timestamp())
        # start = int(datetime(2025, 1, 1, 7).timestamp())
        time_ranges.append((opening, start))

        # Part 2: To 2025-04-25 07:00:00 GMT+7 with span of 1 year
        another = int(datetime(2025, 4, 25, 7).timestamp())
        while start < another:
            one_year_later = int(
                datetime.fromtimestamp(start)
                .replace(year=datetime.fromtimestamp(start).year + 1)
                .timestamp()
            )
            end = min(one_year_later, another)
            time_ranges.append((start, end))
            start = end

        # Part 3: To current with span of 1 day
        day_span: int | None = 24 * 60 * 60

        next_date = datetime.now() + timedelta(days=next_days)
        dest = int(
            next_date.replace(hour=7, minute=0, second=0, microsecond=0).timestamp()
        )
        while start < dest:
            end = min(start + day_span, dest)
            if Utils.is_trading_day(datetime.fromtimestamp(end)):
                time_ranges.append((start, end))
            start = end

        # for time_range in time_ranges:
        #     start_date = datetime.fromtimestamp(time_range[0]).strftime(
        #         "%Y-%m-%d %H:%M:%S"
        #     )
        #     end_date = datetime.fromtimestamp(time_range[1]).strftime(
        #         "%Y-%m-%d %H:%M:%S"
        #     )
        #     print(
        #         f"Time range: {time_range[0]} ({start_date}) to {time_range[1]} ({end_date})"
        #     )

        time_ranges.sort(key=lambda x: x[0], reverse=True)

        return time_ranges

    def _stop_scraping(self, start: int) -> bool:
        return start < (datetime.now() - timedelta(days=5)).timestamp()
