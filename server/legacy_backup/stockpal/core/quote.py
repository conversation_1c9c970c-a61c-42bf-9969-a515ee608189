from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime


@dataclass
class Quote:
    """Data Transfer Object for SSI API stock data response."""

    timestamp: int  # Unix timestamp

    volume: int  # Trading volume

    open_price: float  # Opening price
    close_price: float  # Closing price
    high_price: float  # Highest price
    low_price: float  # Lowest price

    ceiling_price: Optional[float] = 0  # Ceiling price (c)
    floor_price: Optional[float] = 0  # Floor price (f)
    reference_price: Optional[float] = 0  # Reference price (r)
    change_price: Optional[float] = 0  # Change price (cp)

    @property
    def change_price_percent(self):
        if self.reference_price == 0 or self.reference_price is None:
            return 0

        return round(self.change_price / self.reference_price * 100, 2)
