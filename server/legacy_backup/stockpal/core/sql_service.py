import os
import sqlite3
from contextlib import contextmanager
from pathlib import Path
from typing import Any, List, Optional, Tuple

from .constants import Constants
from .util import Utils


class SqliteService:
    def __init__(self, dbname: str = Constants.SQLITE_DB_FILE):
        self.db_path = os.path.join(Utils.get_db_dir(), dbname)
        self.__init_connection()

    def __init_connection(self):
        """Initialize database connection and create tables if needed"""
        with self.get_connection() as conn:
            conn.commit()

    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = sqlite3.connect(self.db_path)
        try:
            yield conn
        finally:
            conn.close()

    def execute(self, query: str, params: Tuple[Any, ...] = ()) -> bool:
        """Execute a query that doesn't return results (CREATE, INSERT, UPDATE, DELETE)"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return True
        except Exception as e:
            print(f"Error executing query: {str(e)}")
            return False

    def execute_many(self, query: str, params_list: List[Tuple[Any, ...]]) -> bool:
        """Execute many queries at once (batch insert/update)"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                conn.commit()
                return True
        except Exception as e:
            print(f"Error executing batch query: {str(e)}")
            return False

    def fetch_one(
        self, query: str, params: Tuple[Any, ...] = ()
    ) -> Optional[Tuple[Any, ...]]:
        """Fetch a single row from the database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchone()
        except Exception as e:
            print(f"Error fetching row: {str(e)}")
            return None

    def fetch_all(
        self, query: str, params: Tuple[Any, ...] = ()
    ) -> List[Tuple[Any, ...]]:
        """Fetch all rows from the database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            print(f"Error fetching rows: {str(e)}")
            return []
