"""
Data scraper factory module for StockPal.

This module provides a unified interface for scraping stock data from multiple
data providers (SSI, VietStock, CafeF). It acts as a factory pattern implementation
that creates the appropriate scraper based on the specified provider.
"""

import logging
from typing import List, Literal

from stockpal.core.stock import DailyPrice, EventData, MinutePrice, PriceData, Stock

from .ssi_scraper import SsiScraper
from .vietstock_scraper import VietstockScraper
from .cafef_scraper import CafefScraper

# Configure module-specific logger
logger = logging.getLogger(__name__)


class DataScraper:
    """
    Factory class for creating data scrapers for different providers.

    This class provides a unified interface for scraping stock data from multiple
    Vietnamese stock market data providers. It automatically selects the appropriate
    scraper implementation based on the specified provider.

    Supported Providers:
        - SSI (default): SSI Securities data provider
        - VietStock: VietStock data provider
        - CafeF: CafeF financial data provider

    Attributes:
        _symbol (str): Stock symbol in uppercase format
        _provider (str): Selected data provider name
        _scraper: Instance of the appropriate scraper implementation
    """

    def __init__(self, symbol: str, provider: Literal["ssi", "vietstock", "cafef"] = "ssi"):
        """
        Initialize the data scraper with symbol and provider.

        Args:
            symbol (str): Stock symbol to scrape data for
            provider (Literal["ssi", "vietstock", "cafef"]): Data provider to use

        Raises:
            ValueError: If symbol is empty or None
        """
        if not symbol or not symbol.strip():
            raise ValueError("Symbol cannot be empty or None")

        self._symbol = symbol.upper()
        self._provider = provider.lower()

        # Create appropriate scraper instance based on provider
        if self._provider == "vietstock":
            self._scraper = VietstockScraper(symbol=self._symbol)
            logger.info(f"Created VietStock scraper for {self._symbol}")
        elif self._provider == "cafef":
            self._scraper = CafefScraper(symbol=self._symbol)
            logger.info(f"Created CafeF scraper for {self._symbol}")
        else:
            # Default to SSI if provider is not recognized
            self._scraper = SsiScraper(symbol=self._symbol)
            logger.info(f"Created SSI scraper for {self._symbol}")

    def fetch_prices(self, timeframe_in_minute: bool = False) -> list[PriceData]:
        """
        Fetch price data for the configured stock symbol.

        Args:
            timeframe_in_minute (bool): If True, fetch minute-level data.
                                      If False, fetch daily data.

        Returns:
            list[PriceData]: List of price data objects (DailyPrice or MinutePrice)

        Raises:
            Exception: If data fetching fails
        """
        try:
            logger.debug(f"Fetching {'minute' if timeframe_in_minute else 'daily'} "
                        f"prices for {self._symbol} from {self._provider}")

            # Fetch raw price data from the scraper
            prices = self._scraper.prices(timeframe_in_minute)

            # Convert to appropriate price data type
            if timeframe_in_minute:
                result = [MinutePrice(**price.__dict__) for price in prices]
                logger.info(f"Fetched {len(result)} minute price records for {self._symbol}")
            else:
                result = [DailyPrice(**price.__dict__) for price in prices]
                logger.info(f"Fetched {len(result)} daily price records for {self._symbol}")

            return result

        except Exception as e:
            logger.error(f"Failed to fetch prices for {self._symbol} from {self._provider}: {str(e)}")
            raise

    def fetch_stock_symbols(self) -> list[Stock]:
        """
        Fetch list of all available stock symbols from the data provider.

        Returns:
            list[Stock]: List of Stock objects with symbol information

        Raises:
            Exception: If symbol fetching fails
        """
        try:
            logger.debug(f"Fetching stock symbols from {self._provider}")
            symbols = self._scraper.fetch_stock_symbols()
            logger.info(f"Fetched {len(symbols)} stock symbols from {self._provider}")
            return symbols

        except Exception as e:
            logger.error(f"Failed to fetch stock symbols from {self._provider}: {str(e)}")
            raise

    def events(self) -> list[EventData]:
        """
        Fetch corporate events data for the configured stock symbol.

        Returns:
            list[EventData]: List of corporate event data objects

        Raises:
            Exception: If event data fetching fails
        """
        try:
            logger.debug(f"Fetching events for {self._symbol} from {self._provider}")
            events = self._scraper.events()
            logger.info(f"Fetched {len(events)} events for {self._symbol}")
            return events

        except Exception as e:
            logger.error(f"Failed to fetch events for {self._symbol} from {self._provider}: {str(e)}")
            raise

    def orders(self):
        """
        Fetch order book/trading data for the configured stock symbol.

        Returns:
            List of order/trade data objects

        Raises:
            Exception: If order data fetching fails
        """
        try:
            logger.debug(f"Fetching orders for {self._symbol} from {self._provider}")
            orders = self._scraper.orders()
            logger.info(f"Fetched order data for {self._symbol}")
            return orders

        except Exception as e:
            logger.error(f"Failed to fetch orders for {self._symbol} from {self._provider}: {str(e)}")
            raise
