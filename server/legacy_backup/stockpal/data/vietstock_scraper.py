import re
from dataclasses import dataclass
from datetime import datetime
from typing import override

from stockpal.core import (
    Constants,
    DailyPrice,
    EventData,
    HttpJsonService,
    MinutePrice,
    PriceData,
    Scraper,
)
from stockpal.core.stock import Stock


class VietstockScraper(Scraper):

    def __init__(self, symbol: str):
        super().__init__(symbol)

        self.http_service = HttpJsonService(
            headers={
                "Accept": "*/*",
                "Accept-Encoding": "gzip",
                "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "Host": "finance.vietstock.vn",
                "Origin": "https://finance.vietstock.vn",
            },
            cookies={
                "__RequestVerificationToken": Constants.VIETSTOCK_COOKIE_REQUEST_VERIFICATION_TOKEN
            },
        )

    @override
    def prices(self, timeframe_in_minute: bool = False) -> list[PriceData]:
        return (
            self.__fetch_price_from_chart(timeframe_in_minute)
            if timeframe_in_minute
            else self.__fetch_price_details()
        )

    @override
    def events(self) -> list[EventData]:
        time_ranges = self._get_time_ranges()

        events = []
        for start, end in time_ranges:
            # Prepare the URL with the current time range
            url = f"https://api.vietstock.vn/tvnew/marks?symbol={self._symbol}&from={start}&to={end}&resolution=1D"

            start_str = datetime.fromtimestamp(start).strftime("%Y_%m_%d")
            end_str = datetime.fromtimestamp(end).strftime("%Y_%m_%d")
            cache_filename = (
                f"{self._symbol}_events_{start_str}_{end_str}-vietstock.json"
            )

            response = self.http_service.get(
                url=url,
                cache_foldername=self._cache_subfolder,
                cache_filename=cache_filename,
            )

            if not response:
                print(f"No data found for {cache_filename}")
                break

            events.append(
                EventData(
                    id=item.get("id", ""),
                    time=item.get("time", 0),
                    texts=[item.get("text", "")],
                )
                for item in response
            )

        return events

    def __fetch_price_details(self) -> list[PriceData]:
        page_size = 100
        prices: list[PriceData] = []

        time_ranges = self._get_time_ranges()

        for start, end in time_ranges:
            start_str = datetime.fromtimestamp(start).strftime("%Y-%m-%d")
            end_str = datetime.fromtimestamp(end).strftime("%Y-%m-%d")

            has_next = True
            page_index = 1

            form_data = {
                "Code": self._symbol,
                "OrderBy": "",
                "OrderDirection": "desc",
                "PageIndex": "1",
                "PageSize": str(page_size),
                "FromDate": start_str,
                "ToDate": end_str,
                "ExportType": "default",
                "Cols": "TKLGD,TGTGD,VHTT,TGG,DC,TGPTG,KLGDKL,GTGDKL,Room,RoomCL,RoomCLPT,KL_M_GDKL,GT_M_GDKL,KL_B_GDKL,GT_B_GDKL,KL_M_GDTT,GT_M_GDTT,KL_B_GDTT,GT_B_GDTT,KL_M_TKL,GT_M_TGT,KL_B_TKL,GT_B_TGT",
                "ExchangeID": str(Constants.VIETSTOCK_HOSE_EXCHANGE_ID),
                "__RequestVerificationToken": Constants.VIETSTOCK_PAYLOAD_REQUEST_VERIFICATION_TOKEN,
            }

            while has_next:
                form_data["PageIndex"] = str(page_index)

                cache_filename = f"{self._symbol}_trading_result_{start_str}_{end_str}_page_{page_index}-vietstock.json"
                response = self.http_service.post_form_data(
                    url="https://finance.vietstock.vn/data/gettradingresult",
                    form_data=form_data,
                    cache_foldername=self._cache_subfolder,
                    cache_filename=cache_filename,
                )

                if not response or "Data" not in response or not response["Data"]:
                    print(f"No data found for {cache_filename}")
                    has_next = False
                    break

                # Check if we have fewer items than the page size, indicating no more data
                if response["Data"] and len(response["Data"]) < page_size:
                    has_next = False

                for item in response.get("Data", []):
                    prices.append(
                        PriceData(
                            symbol=self._symbol,
                            timestamp=int(
                                re.sub(r"[^\d.]", "", str(item.get("TradingDate")))[:-3]
                            ),
                            open_price=float(item.get("OpenPrice", 0.0)),
                            close_price=float(item.get("ClosePrice", 0.0)),
                            highest_price=float(item.get("HighestPrice", 0.0)),
                            lowest_price=float(item.get("LowestPrice", 0.0)),
                            change_price=float(item.get("Change", 0)),
                            change_price_percent=float(item.get("perChange", 0)),
                            average_price=float(item.get("AvrPriceFullTime", 0)),
                            close_price_adjusted=float(item.get("AdjustPrice", 0)),
                            ceiling_price=float(item.get("CeilingPrice", 0)),
                            floor_price=float(item.get("FloorPrice", 0)),
                            reference_price=float(item.get("refPrice", 0)),
                            match_volume=item.get("TotalVol", 0),
                            match_value=item.get("TotalValue", 0.0),
                            deal_volume=item.get("TotalDealVol", 0),
                            deal_value=item.get("TotalDealVal", 0.0),
                            foreign_current_room=item.get("CurrRoom", 0),
                            foreign_buy_volume=item.get("TotalForeignBuyVol", 0),
                            foreign_buy_value=item.get("TotalForeignBuyVal", 0.0),
                            foreign_sell_volume=item.get("TotalForeignSellVol", 0),
                            foreign_sell_value=item.get("TotalForeignSellVal", 0.0),
                            foreign_net_volume=item.get("TotalForeignNetVol", 0),
                            foreign_net_value=item.get("TotalForeignNetVal", 0.0),
                            foreign_match_buy_volume=item.get(
                                "TotalForeignMatchBuyVol", 0
                            ),
                            foreign_deal_buy_volume=item.get(
                                "TotalForeignDealBuyVol", 0
                            ),
                            buy_trade_quantity=item.get("TotalBuyTrade", 0),
                            buy_trade_volume=item.get("TotalBuyTradeVol", 0.0),
                            sell_trade_quantity=item.get("TotalSellTrade", 0),
                            sell_trade_volume=item.get("TotalSellTradeVol", 0.0),
                        )
                    )

                page_index += 1

        return prices

    def __fetch_price_from_chart(
        self, timeframe_in_minute: bool = False
    ) -> list[PriceData]:
        resolution = "1" if timeframe_in_minute else "1D"
        resolution_str = "minute" if timeframe_in_minute else "day"
        datetime_format = "%Y_%m_%d_%H:%M" if timeframe_in_minute else "%Y_%m_%d"

        time_ranges = self._get_time_ranges()

        prices: list[PriceData] = []
        processed_txns = []

        chart_http_service = HttpJsonService(
            headers={
                "Accept": "*/*",
                "Accept-Encoding": "gzip,deflate,br,zstd",
                "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                "Host": "api.vietstock.vn",
                "Origin": "https://stockchart.vietstock.vn",
                "Referer": "https://stockchart.vietstock.vn/",
            }
        )

        for start, end in time_ranges:
            url = f"https://api.vietstock.vn/tvnew/history?symbol={self._symbol}&resolution={resolution}&from={start}&to={end}"

            start_str = datetime.fromtimestamp(start).strftime(datetime_format)
            end_str = datetime.fromtimestamp(end).strftime(datetime_format)
            cache_filename = f"{self._symbol}_{resolution_str}_prices_{start_str}_{end_str}-vietstock.json"

            response = chart_http_service.get(
                url=url,
                cache_foldername=self._cache_subfolder,
                cache_filename=cache_filename,
            )

            # Check if 't' is empty or not present in the response data
            if not response or "t" not in response or not response["t"]:
                print(f"No data found for {cache_filename}")

                if self._stop_scraping(start):
                    break

            history = VsPrice(
                timestamps=response.get("t", []),
                open_prices=response.get("o", []),
                close_prices=response.get("c", []),
                high_prices=response.get("h", []),
                low_prices=response.get("l", []),
                volumes=response.get("v", []),
            )

            for i in range(len(history.timestamps)):
                if history.timestamps[i] in processed_txns:
                    print(f"Duplicate timestamp {history.timestamps[i]}, skipping...")
                    continue

                processed_txns.append(history.timestamps[i])

                if timeframe_in_minute:
                    prices.append(
                        MinutePrice(
                            symbol=self._symbol,
                            timestamp=history.timestamps[i],
                            open_price=float(history.open_prices[i]),
                            close_price=float(history.close_prices[i]),
                            highest_price=float(history.high_prices[i]),
                            lowest_price=float(history.low_prices[i]),
                            match_volume=float(history.volumes[i]),
                        )
                    )
                else:
                    prices.append(
                        DailyPrice(
                            symbol=self._symbol,
                            timestamp=history.timestamps[i],
                            open_price=float(history.open_prices[i]),
                            close_price=float(history.close_prices[i]),
                            highest_price=float(history.high_prices[i]),
                            lowest_price=float(history.low_prices[i]),
                            match_volume=float(history.volumes[i]),
                        )
                    )

        # Sort by timestamp in descending order
        prices.sort(key=lambda x: x.timestamp, reverse=True)

        for i in range(0, len(prices) - 1, 1):
            prices[i].reference_price = (
                prices[i + 1].close_price if (i + 1) < len(prices) else 0
            )
            prices[i].change_price = round(
                prices[i].close_price - prices[i].reference_price, 2
            )
            # Calculate ceiling and floor prices by adding/subtracting 7% of the reference price
            prices[i].ceiling_price = round(prices[i].reference_price * 1.07, 2)
            prices[i].floor_price = round(prices[i].reference_price / 1.07, 2)

        return prices

    @override
    def fetch_stock_symbols(self) -> list[Stock]:
        """
        (This is not work)
        """

        stocks: list[Stock] = []

        form_data = {
            "catID": "0",
            "industryID": "0",
            "page": "1",
            "pageSize": "20",
            "type": "0",
            "code": "",
            "businessTypeID": "0",
            "orderBy": "Code",
            "orderDir": "ASC",
            "__RequestVerificationToken": Constants.VIETSTOCK_PAYLOAD_REQUEST_VERIFICATION_TOKEN,
        }

        has_next = True
        page_index = 1

        while has_next:
            form_data["page"] = str(page_index)

            cache_filename = (
                f"{self._symbol}_corporate_page_{page_index}-vietstock.json"
            )
            response = self.http_service.post_form_data(
                url="https://finance.vietstock.vn/data/corporateaz",
                form_data=form_data,
                cache_foldername=self._cache_subfolder,
                cache_filename=cache_filename,
            )

            if not response:
                print(f"No data found for {cache_filename}")
                has_next = False
                break

            for item in response:
                stocks.append(
                    Stock(
                        symbol=item.get("Code", ""),
                        name=item.get("Name", ""),
                        exchange=item.get("Exchange", ""),
                        industry=item.get("IndustryName", ""),
                    )
                )

            page_index += 1

        return stocks


@dataclass
class VsPrice:
    timestamps: list[int]  # List of timestamps
    open_prices: list[float]  # List of opening prices
    close_prices: list[float]  # List of closing prices
    high_prices: list[float]  # List of highest prices
    low_prices: list[float]  # List of lowest prices
    volumes: list[int]  # List of trading volumes
