import os
from datetime import datetime
from typing import override

import pandas as pd

from stockpal.core import PriceData, SqliteService


class PriceDataDao:

    def __init__(self, sql_service: SqliteService):
        self._sql_service = sql_service
        self._column_order = """
            symbol, timestamp, open_price, close_price, highest_price, lowest_price, change_price, change_price_percent, average_price, 
            close_price_adjusted, ceiling_price, floor_price, reference_price, match_volume, match_value, deal_volume, deal_value, 
            foreign_current_room, foreign_buy_volume, foreign_buy_value, foreign_sell_volume, foreign_sell_value, 
            foreign_net_volume, foreign_net_value, foreign_match_buy_volume, foreign_deal_buy_volume, 
            buy_trade_quantity, buy_trade_volume, sell_trade_quantity, sell_trade_volume
        """

    def table_name(self) -> str:
        raise NotImplementedError

    def get_all(self, symbol: str, from_ts: int | None = None, to_ts: int | None = None) -> list[PriceData]:
        timestamp = from_ts or 0
        query = f"""SELECT {self._column_order} 
                FROM {self.table_name()} 
                WHERE symbol =? \
                    and timestamp >= {timestamp} \
                    {f'and timestamp <= {to_ts}' if to_ts is not None else ''} \
                ORDER BY timestamp"""
        params = [symbol]
        rows = self._sql_service.fetch_all(query, tuple(params))
        return [
            PriceData(
                symbol=row[0],
                timestamp=row[1],
                open_price=row[2],
                close_price=row[3],
                highest_price=row[4],
                lowest_price=row[5],
                change_price=row[6],
                change_price_percent=row[7],
                average_price=row[8],
                close_price_adjusted=row[9],
                ceiling_price=row[10],
                floor_price=row[11],
                reference_price=row[12],
                match_volume=row[13],
                match_value=row[14],
                deal_volume=row[15],
                deal_value=row[16],
                foreign_current_room=row[17],
                foreign_buy_volume=row[18],
                foreign_buy_value=row[19],
                foreign_sell_volume=row[20],
                foreign_sell_value=row[21],
                foreign_net_volume=row[22],
                foreign_net_value=row[23],
                foreign_match_buy_volume=row[24],
                foreign_deal_buy_volume=row[25],
                buy_trade_quantity=row[26],
                buy_trade_volume=row[27],
                sell_trade_quantity=row[28],
                sell_trade_volume=row[29],
            )
            for row in rows
        ]

    def get_last_number_of_prices(self, symbol: str, number: int, to_ts: int | None = None) -> list[PriceData]:
        query = f"""SELECT {self._column_order} 
                FROM {self.table_name()} 
                WHERE symbol =? \
                    and timestamp <= {to_ts if to_ts is not None else datetime.now().timestamp()} \
                ORDER BY timestamp DESC \
                LIMIT {number}"""
        params = [symbol]
        rows = self._sql_service.fetch_all(query, tuple(params))
        return [
            PriceData(
                symbol=row[0],
                timestamp=row[1],
                open_price=row[2],
                close_price=row[3],
                highest_price=row[4],
                lowest_price=row[5],
                change_price=row[6],
                change_price_percent=row[7],
                average_price=row[8],
                close_price_adjusted=row[9],
                ceiling_price=row[10],
                floor_price=row[11],
                reference_price=row[12],
                match_volume=row[13],
                match_value=row[14],
                deal_volume=row[15],
                deal_value=row[16],
                foreign_current_room=row[17],
                foreign_buy_volume=row[18],
                foreign_buy_value=row[19],
                foreign_sell_volume=row[20],
                foreign_sell_value=row[21],
                foreign_net_volume=row[22],
                foreign_net_value=row[23],
                foreign_match_buy_volume=row[24],
                foreign_deal_buy_volume=row[25],
                buy_trade_quantity=row[26],
                buy_trade_volume=row[27],
                sell_trade_quantity=row[28],
                sell_trade_volume=row[29],
            )
            for row in rows
        ]

    def upsert(self, symbol: str, prices: list[PriceData]):
        # Remove prices with close_price = 0
        prices = [price for price in prices if price.close_price != 0]

        # Sort by timestamp
        prices.sort(key=lambda x: x.timestamp)

        # Upsert
        self._sql_service.execute_many(
            f"""
            INSERT OR REPLACE INTO {self.table_name()} ({self._column_order})
            VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) 
            """,
            params_list=[
                (
                    symbol,
                    p.timestamp,
                    p.open_price,
                    p.close_price,
                    p.highest_price,
                    p.lowest_price,
                    p.change_price,
                    p.change_price_percent,
                    p.average_price,
                    p.close_price_adjusted,
                    p.ceiling_price,
                    p.floor_price,
                    p.reference_price,
                    p.match_volume,
                    p.match_value,
                    p.deal_volume,
                    p.deal_value,
                    p.foreign_current_room,
                    p.foreign_buy_volume,
                    p.foreign_buy_value,
                    p.foreign_sell_volume,
                    p.foreign_sell_value,
                    p.foreign_net_volume,
                    p.foreign_net_value,
                    p.foreign_match_buy_volume,
                    p.foreign_deal_buy_volume,
                    p.buy_trade_quantity,
                    p.buy_trade_volume,
                    p.sell_trade_quantity,
                    p.sell_trade_volume,
                )
                for p in prices
            ],
        )

    def to_excel(
        self, symbol: str, prices: list[PriceData], suffix_name: str | None = None
    ):
        # Sort by timestamp in descending order
        prices.sort(key=lambda x: x.timestamp, reverse=True)

        df = pd.DataFrame(
            [
                {
                    "Thời gian": datetime.fromtimestamp(p.timestamp).strftime(
                        "%Y-%m-%d %H:%M:%S"
                        if self.table_name() == "minute_prices"
                        else "%Y-%m-%d"
                    ),
                    "Mở cửa": p.open_price,
                    "Đóng cửa": p.close_price,
                    "Cao nhất": p.highest_price,
                    "Thấp nhất": p.lowest_price,
                    "Trung bình": p.average_price,
                    "Tham chiếu": p.reference_price,
                    "Trần": p.ceiling_price,
                    "Sàn": p.floor_price,
                    "Thay đổi": p.change_price,
                    "Thay đổi (%)": round(p.change_price_percent * 100, 2),
                    "Khối lượng": p.match_volume,
                    "Giá trị giao dịch": p.match_value,
                    "NN KL Mua": p.foreign_buy_volume,
                    "NN GT Mua": p.foreign_buy_value,
                    "NN KL Bán": p.foreign_sell_value,
                    "NN GT Bán": p.foreign_sell_volume,
                    "Room NN": p.foreign_current_room,
                    "NN KL ròng": p.foreign_net_volume,
                    "NN GT ròng": p.foreign_net_value,
                }
                for p in prices
            ]
        )

        excel_file = os.path.join(
            "test",
            f"{symbol}_{self.table_name()}_upto_{datetime.now().strftime("%Y_%m_%d")}-{suffix_name if suffix_name else ''}.xlsx",
        )

        df.to_excel(excel_writer=excel_file, index=False)

        print(f"Data exported to {excel_file}")


class DailyPriceDao(PriceDataDao):

    @override
    def table_name(self) -> str:
        return "daily_prices"


class MinutePriceDao(PriceDataDao):

    @override
    def table_name(self) -> str:
        return "minute_prices"
