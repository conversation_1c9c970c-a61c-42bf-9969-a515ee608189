"""
Application Settings

Configuration settings for the StockPal application following Clean Architecture principles.
"""

import os
from pathlib import Path
from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class DatabaseSettings:
    """Database configuration settings."""
    url: str
    pool_size: int = 10
    max_overflow: int = 20
    echo: bool = False


@dataclass
class LoggingSettings:
    """Logging configuration settings."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_dir: str = "db/logs"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class CacheSettings:
    """Cache configuration settings."""
    cache_dir: str = "cache"
    default_ttl: int = 24 * 60 * 60  # 24 hours in seconds
    max_cache_size: int = 1000  # Maximum number of cached items


@dataclass
class DataProviderSettings:
    """Data provider configuration settings."""
    default_provider: str = "ssi"
    timeout: int = 30  # seconds
    retry_attempts: int = 3
    retry_delay: int = 1  # seconds


class Settings:
    """
    Application settings manager.
    
    Centralizes configuration management and provides environment-specific
    settings for the StockPal application.
    """
    
    def __init__(self):
        """Initialize settings from environment variables and defaults."""
        self._base_dir = Path(__file__).parent.parent.parent.parent
        self._load_settings()
    
    def _load_settings(self):
        """Load settings from environment variables with defaults."""
        # Database settings
        self.database = DatabaseSettings(
            url=os.getenv("DATABASE_URL", f"sqlite:///{self._base_dir}/db/stock_data.db"),
            pool_size=int(os.getenv("DB_POOL_SIZE", "10")),
            max_overflow=int(os.getenv("DB_MAX_OVERFLOW", "20")),
            echo=os.getenv("DB_ECHO", "false").lower() == "true"
        )
        
        # Logging settings
        self.logging = LoggingSettings(
            level=os.getenv("LOG_LEVEL", "INFO"),
            format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            log_dir=os.getenv("LOG_DIR", str(self._base_dir / "db" / "logs")),
            max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", str(10 * 1024 * 1024))),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
        )
        
        # Cache settings
        self.cache = CacheSettings(
            cache_dir=os.getenv("CACHE_DIR", str(self._base_dir / "cache")),
            default_ttl=int(os.getenv("CACHE_TTL", str(24 * 60 * 60))),
            max_cache_size=int(os.getenv("CACHE_MAX_SIZE", "1000"))
        )
        
        # Data provider settings
        self.data_provider = DataProviderSettings(
            default_provider=os.getenv("DEFAULT_PROVIDER", "ssi"),
            timeout=int(os.getenv("PROVIDER_TIMEOUT", "30")),
            retry_attempts=int(os.getenv("PROVIDER_RETRY_ATTEMPTS", "3")),
            retry_delay=int(os.getenv("PROVIDER_RETRY_DELAY", "1"))
        )
        
        # Application settings
        self.app_name = os.getenv("APP_NAME", "StockPal")
        self.app_version = os.getenv("APP_VERSION", "1.0.0")
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        self.environment = os.getenv("ENVIRONMENT", "development")
    
    @property
    def base_dir(self) -> Path:
        """Get the base directory path."""
        return self._base_dir
    
    def get_log_file_path(self, symbol: str, indicator: str) -> Path:
        """
        Get the log file path for a specific symbol and indicator.
        
        Args:
            symbol: Stock symbol
            indicator: Technical indicator name
            
        Returns:
            Path: Log file path
        """
        log_dir = Path(self.logging.log_dir) / symbol.upper()
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir / f"{indicator.lower()}.log"
    
    def get_cache_file_path(self, cache_key: str) -> Path:
        """
        Get the cache file path for a cache key.
        
        Args:
            cache_key: Cache key
            
        Returns:
            Path: Cache file path
        """
        cache_dir = Path(self.cache.cache_dir)
        cache_dir.mkdir(parents=True, exist_ok=True)
        return cache_dir / f"{cache_key}.json"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert settings to dictionary.
        
        Returns:
            Dict[str, Any]: Settings as dictionary
        """
        return {
            "app_name": self.app_name,
            "app_version": self.app_version,
            "debug": self.debug,
            "environment": self.environment,
            "database": self.database.__dict__,
            "logging": self.logging.__dict__,
            "cache": self.cache.__dict__,
            "data_provider": self.data_provider.__dict__
        }


# Global settings instance
settings = Settings()
