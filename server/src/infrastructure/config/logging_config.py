"""
Logging configuration module for StockPal.

This module provides centralized logging configuration for the StockPal application,
including separate log files for different components and technical indicators.
"""

import logging
import logging.handlers
import os
from datetime import datetime
from typing import Dict, Optional

from .util import Utils


class StockPalLogger:
    """
    Centralized logging configuration for StockPal application.
    
    This class manages logging configuration for different components of the
    StockPal application, creating separate log files for different indicators
    and symbols to facilitate debugging and monitoring.
    """
    
    _loggers: Dict[str, logging.Logger] = {}
    _log_dir: Optional[str] = None
    _initialized: bool = False
    
    @classmethod
    def initialize(cls, log_level: str = "INFO") -> None:
        """
        Initialize the logging system.
        
        Args:
            log_level (str): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        if cls._initialized:
            return
            
        # Create logs directory
        cls._log_dir = os.path.join(Utils.get_server_dir(), "db", "logs")
        os.makedirs(cls._log_dir, exist_ok=True)
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),  # Console output
                logging.FileHandler(
                    os.path.join(cls._log_dir, "stockpal.log"),
                    encoding='utf-8'
                )
            ]
        )
        
        cls._initialized = True
    
    @classmethod
    def get_logger(cls, name: str, symbol: Optional[str] = None) -> logging.Logger:
        """
        Get a logger instance for a specific component and symbol.
        
        Args:
            name (str): Logger name (e.g., 'rsi', 'macd', 'data_scraper')
            symbol (Optional[str]): Stock symbol for symbol-specific logging
            
        Returns:
            logging.Logger: Configured logger instance
        """
        if not cls._initialized:
            cls.initialize()
            
        # Create unique logger key
        logger_key = f"{name}_{symbol}" if symbol else name
        
        if logger_key not in cls._loggers:
            logger = logging.getLogger(logger_key)
            
            # Create symbol-specific log directory if needed
            if symbol:
                symbol_log_dir = os.path.join(cls._log_dir, symbol.upper())
                os.makedirs(symbol_log_dir, exist_ok=True)
                
                # Create rotating file handler for symbol-specific logs
                log_file = os.path.join(symbol_log_dir, f"{name}.log")
                file_handler = logging.handlers.RotatingFileHandler(
                    log_file,
                    maxBytes=10*1024*1024,  # 10MB
                    backupCount=5,
                    encoding='utf-8'
                )
            else:
                # Create general component log file
                log_file = os.path.join(cls._log_dir, f"{name}.log")
                file_handler = logging.handlers.RotatingFileHandler(
                    log_file,
                    maxBytes=10*1024*1024,  # 10MB
                    backupCount=5,
                    encoding='utf-8'
                )
            
            # Configure formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            
            # Add handler to logger
            logger.addHandler(file_handler)
            logger.setLevel(logging.DEBUG)
            
            # Prevent duplicate logs in parent loggers
            logger.propagate = False
            
            cls._loggers[logger_key] = logger
            
        return cls._loggers[logger_key]
    
    @classmethod
    def get_indicator_logger(cls, indicator_name: str, symbol: str) -> logging.Logger:
        """
        Get a logger specifically for technical indicators.
        
        Args:
            indicator_name (str): Name of the technical indicator (e.g., 'rsi', 'macd')
            symbol (str): Stock symbol
            
        Returns:
            logging.Logger: Configured logger for the indicator
        """
        return cls.get_logger(f"indicator_{indicator_name}", symbol)
    
    @classmethod
    def get_data_logger(cls, provider: str, symbol: Optional[str] = None) -> logging.Logger:
        """
        Get a logger specifically for data operations.
        
        Args:
            provider (str): Data provider name (e.g., 'ssi', 'vietstock')
            symbol (Optional[str]): Stock symbol
            
        Returns:
            logging.Logger: Configured logger for data operations
        """
        logger_name = f"data_{provider}"
        return cls.get_logger(logger_name, symbol)
    
    @classmethod
    def get_analysis_logger(cls, symbol: str) -> logging.Logger:
        """
        Get a logger specifically for stock analysis operations.
        
        Args:
            symbol (str): Stock symbol
            
        Returns:
            logging.Logger: Configured logger for analysis operations
        """
        return cls.get_logger("analysis", symbol)
    
    @classmethod
    def log_calculation_start(cls, logger: logging.Logger, indicator: str, 
                            symbol: str, params: Dict) -> None:
        """
        Log the start of an indicator calculation.
        
        Args:
            logger: Logger instance
            indicator: Indicator name
            symbol: Stock symbol
            params: Calculation parameters
        """
        logger.info(f"Starting {indicator} calculation for {symbol} with params: {params}")
    
    @classmethod
    def log_calculation_end(cls, logger: logging.Logger, indicator: str, 
                          symbol: str, result_count: int, duration: float) -> None:
        """
        Log the completion of an indicator calculation.
        
        Args:
            logger: Logger instance
            indicator: Indicator name
            symbol: Stock symbol
            result_count: Number of calculated values
            duration: Calculation duration in seconds
        """
        logger.info(f"Completed {indicator} calculation for {symbol}: "
                   f"{result_count} values in {duration:.3f}s")
    
    @classmethod
    def log_data_fetch(cls, logger: logging.Logger, operation: str, 
                      symbol: str, count: int, source: str) -> None:
        """
        Log data fetching operations.
        
        Args:
            logger: Logger instance
            operation: Type of operation (e.g., 'fetch_prices', 'fetch_symbols')
            symbol: Stock symbol
            count: Number of records fetched
            source: Data source (cache/api)
        """
        logger.info(f"{operation} for {symbol}: {count} records from {source}")


# Convenience function for getting loggers
def get_logger(name: str, symbol: Optional[str] = None) -> logging.Logger:
    """
    Convenience function to get a logger instance.
    
    Args:
        name (str): Logger name
        symbol (Optional[str]): Stock symbol
        
    Returns:
        logging.Logger: Configured logger instance
    """
    return StockPalLogger.get_logger(name, symbol)
