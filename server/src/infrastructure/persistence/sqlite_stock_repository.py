"""
SQLite Stock Repository Implementation

This module provides the SQLite implementation of the stock repository interface,
handling all database operations for stock data.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from src.domain.entities.stock_models import PricePoint, StockAnalysis
from src.domain.exceptions.stock_exceptions import (
    DatabaseException, SymbolNotFoundException
)
from src.infrastructure.persistence.sql_service import SqliteService
from src.infrastructure.persistence.price_dao import DailyPriceDao, MinutePriceDao
from src.infrastructure.persistence.symbol_dao import SymbolDao


logger = logging.getLogger(__name__)


class SqliteStockRepository:
    """
    SQLite implementation of the stock repository.

    This class handles all database operations for stock data using SQLite
    as the underlying storage mechanism.
    """

    def __init__(self, sql_service: Optional[SqliteService] = None):
        """
        Initialize the repository.

        Args:
            sql_service: Optional SQL service instance. If None, creates a new one.
        """
        self._sql_service = sql_service or SqliteService()
        self._daily_price_dao = DailyPriceDao(sql_service=self._sql_service)
        self._minute_price_dao = MinutePriceDao(sql_service=self._sql_service)
        self._symbol_dao = SymbolDao(sql_service=self._sql_service)
        self._logger = logger

    def get_daily_prices(
        self,
        symbol: str,
        days: int,
        end_date: Optional[datetime] = None
    ) -> List[PricePoint]:
        """
        Get daily price data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to retrieve
            end_date: End date for the data range (defaults to now)

        Returns:
            List of price points

        Raises:
            DatabaseException: If database operation fails
            SymbolNotFoundException: If symbol is not found
        """
        try:
            self._logger.info(f"Fetching daily prices for {symbol}, {days} days")

            # Get daily prices from DAO
            daily_prices = self._daily_price_dao.get_prices(
                symbol=symbol,
                days=days,
                end_date=end_date
            )

            # Convert to domain entities
            price_points = [
                PricePoint(
                    symbol=price.symbol,
                    timestamp=price.timestamp,
                    open_price=price.open_price,
                    high_price=price.highest_price,
                    low_price=price.lowest_price,
                    close_price=price.close_price,
                    volume=price.match_volume,
                    change_price=price.change_price,
                    change_percent=price.change_price_percent
                )
                for price in daily_prices
            ]

            self._logger.info(f"Retrieved {len(price_points)} daily price points for {symbol}")
            return price_points

        except Exception as e:
            self._logger.error(f"Failed to get daily prices for {symbol}: {e}")
            raise DatabaseException(f"Database error: {e}")

    def get_minute_prices(
        self,
        symbol: str,
        days: int,
        end_date: Optional[datetime] = None
    ) -> List[PricePoint]:
        """
        Get minute price data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to retrieve
            end_date: End date for the data range (defaults to now)

        Returns:
            List of price points

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            self._logger.info(f"Fetching minute prices for {symbol}, {days} days")

            # Get minute prices from DAO
            minute_prices = self._minute_price_dao.get_prices(
                symbol=symbol,
                days=days,
                end_date=end_date
            )

            # Convert to domain entities
            price_points = [
                PricePoint(
                    symbol=price.symbol,
                    timestamp=price.timestamp,
                    open_price=price.open_price,
                    high_price=price.highest_price,
                    low_price=price.lowest_price,
                    close_price=price.close_price,
                    volume=price.match_volume,
                    change_price=price.change_price,
                    change_percent=price.change_price_percent
                )
                for price in minute_prices
            ]

            self._logger.info(f"Retrieved {len(price_points)} minute price points for {symbol}")
            return price_points

        except Exception as e:
            self._logger.error(f"Failed to get minute prices for {symbol}: {e}")
            raise DatabaseException(f"Database error: {e}")

    def save_daily_prices(self, symbol: str, prices: List[PricePoint]) -> None:
        """
        Save daily price data.

        Args:
            symbol: Stock symbol
            prices: List of price points to save

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            self._logger.info(f"Saving {len(prices)} daily prices for {symbol}")

            # Convert domain entities to DAO entities
            from src.domain.entities.stock import DailyPrice

            daily_prices = [
                DailyPrice(
                    symbol=symbol,
                    timestamp=price.timestamp,
                    open_price=price.open_price,
                    highest_price=price.high_price,
                    lowest_price=price.low_price,
                    close_price=price.close_price,
                    match_volume=price.volume,
                    change_price=price.change_price or 0.0,
                    change_price_percent=price.change_percent or 0.0
                )
                for price in prices
            ]

            # Save using DAO
            self._daily_price_dao.upsert(symbol=symbol, prices=daily_prices)

            self._logger.info(f"Successfully saved daily prices for {symbol}")

        except Exception as e:
            self._logger.error(f"Failed to save daily prices for {symbol}: {e}")
            raise DatabaseException(f"Database error: {e}")

    def save_minute_prices(self, symbol: str, prices: List[PricePoint]) -> None:
        """
        Save minute price data.

        Args:
            symbol: Stock symbol
            prices: List of price points to save

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            self._logger.info(f"Saving {len(prices)} minute prices for {symbol}")

            # Convert domain entities to DAO entities
            from src.domain.entities.stock import MinutePrice

            minute_prices = [
                MinutePrice(
                    symbol=symbol,
                    timestamp=price.timestamp,
                    open_price=price.open_price,
                    highest_price=price.high_price,
                    lowest_price=price.low_price,
                    close_price=price.close_price,
                    match_volume=price.volume,
                    change_price=price.change_price or 0.0,
                    change_price_percent=price.change_percent or 0.0
                )
                for price in prices
            ]

            # Save using DAO
            self._minute_price_dao.upsert(symbol=symbol, prices=minute_prices)

            self._logger.info(f"Successfully saved minute prices for {symbol}")

        except Exception as e:
            self._logger.error(f"Failed to save minute prices for {symbol}: {e}")
            raise DatabaseException(f"Database error: {e}")

    def get_symbols(self) -> List[str]:
        """
        Get all available symbols.

        Returns:
            List of symbol strings

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            self._logger.info("Fetching all symbols")

            symbols = self._symbol_dao.get_all_symbols()
            symbol_list = [symbol.symbol for symbol in symbols]

            self._logger.info(f"Retrieved {len(symbol_list)} symbols")
            return symbol_list

        except Exception as e:
            self._logger.error(f"Failed to get symbols: {e}")
            raise DatabaseException(f"Database error: {e}")

    def symbol_exists(self, symbol: str) -> bool:
        """
        Check if a symbol exists in the database.

        Args:
            symbol: Stock symbol to check

        Returns:
            True if symbol exists, False otherwise
        """
        try:
            symbols = self.get_symbols()
            return symbol in symbols
        except Exception:
            return False

    def get_latest_price(self, symbol: str) -> Optional[PricePoint]:
        """
        Get the latest price for a symbol.

        Args:
            symbol: Stock symbol

        Returns:
            Latest price point or None if not found
        """
        try:
            prices = self.get_daily_prices(symbol, days=1)
            return prices[0] if prices else None
        except Exception:
            return None
