from src.infrastructure.config import SqliteService


class Schema:
    def __init__(self):
        self.sql_service = SqliteService("stock_data.db")

    def init(self):
        self.__create_symbols_table()
        self.__create_price_table("daily_prices")
        self.__create_price_table("minute_prices")
        self.__create_trading_dates_table()
        self.__create_orders_table()

    def __create_symbols_table(self):
        self.sql_service.execute(
            """
            CREATE TABLE IF NOT EXISTS `symbols` (
                code CHAR(5) NOT NULL PRIMARY KEY,
                name TEXT NOT NULL,
                exchange TEXT NOT NULL,
                industry TEXT NULL,
                sector TEXT NULL,
                UNIQUE(code)
            )
            """
        )

    def __create_price_table(self, table_name: str):
        self.sql_service.execute(
            f"""
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp INTEGER NOT NULL,
                open_price DECIMAL(10,2) DEFAULT 0,
                close_price DECIMAL(10,2) DEFAULT 0,
                highest_price DECIMAL(10,2) DEFAULT 0,
                lowest_price DECIMAL(10,2) DEFAULT 0,
                change_price DECIMAL(10,2) DEFAULT 0,
                change_price_percent DECIMAL(10,2) DEFAULT 0,
                average_price DECIMAL(10,2) DEFAULT 0,
                close_price_adjusted DECIMAL(10,2) DEFAULT 0,
                ceiling_price DECIMAL(10,2) DEFAULT 0,
                floor_price DECIMAL(10,2) DEFAULT 0,
                reference_price DECIMAL(10,2) DEFAULT 0,
                match_volume DECIMAL(15,2) DEFAULT 0,
                match_value DECIMAL(15,2) DEFAULT 0,
                deal_volume DECIMAL(15,2) DEFAULT 0,
                deal_value DECIMAL(15,2) DEFAULT 0,
                foreign_current_room INTEGER DEFAULT 0,
                foreign_buy_volume DECIMAL(15,2) DEFAULT 0,
                foreign_buy_value DECIMAL(15,2) DEFAULT 0,
                foreign_sell_volume DECIMAL(15,2) DEFAULT 0,
                foreign_sell_value DECIMAL(15,2) DEFAULT 0,
                foreign_net_volume DECIMAL(15,2) DEFAULT 0,
                foreign_net_value DECIMAL(15,2) DEFAULT 0,
                foreign_match_buy_volume DECIMAL(15,2) DEFAULT 0,
                foreign_deal_buy_volume DECIMAL(15,2) DEFAULT 0,
                buy_trade_quantity INTEGER DEFAULT 0,
                buy_trade_volume DECIMAL(15,2) DEFAULT 0,
                sell_trade_quantity INTEGER DEFAULT 0,
                sell_trade_volume DECIMAL(15,2) DEFAULT 0,
                UNIQUE(symbol, timestamp)
            )
            """
        )

    def __create_trading_dates_table(self):
        self.sql_service.execute(
            """
            CREATE TABLE IF NOT EXISTS `trading_dates` (
                `date` DATE NOT NULL PRIMARY KEY,
                `dow` INTEGER NOT NULL,
                UNIQUE(`date`)
            )
            """
        )

    def __create_orders_table(self):
        """Initialize required database tables"""
        # Create the table with correct schema
        self.sql_service.execute(
            """
            CREATE TABLE IF NOT EXISTS `orders` (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                order_date DATETIME NOT NULL,
                order_id TEXT NOT NULL,
                accumulated_volume INTEGER NOT NULL,
                accumulated_value DECIMAL(15,2) NOT NULL,
                volume INTEGER NOT NULL,
                change_type TEXT NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                price_change DECIMAL(10,2) NOT NULL,
                price_change_percent DECIMAL(10,2) NOT NULL,
                reference_price DECIMAL(10,2) NOT NULL,
                side TEXT NOT NULL,
                time TEXT NOT NULL
            )
            """
        )
        self.sql_service.execute(
            """
            CREATE INDEX IF NOT EXISTS idx_symbol_order ON orders (symbol, order_date)
            """
        )

    def __create_volume_table(self):
        """Initialize required database tables"""
        # Create the table with correct schema
        self.sql_service.execute(
            """
            CREATE TABLE IF NOT EXISTS `volume` (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                trade_time DATETIME NOT NULL,
                volume INTEGER NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                price_change DECIMAL(10,2) NOT NULL,
                price_change_percent DECIMAL(10,2) NOT NULL,
                action TEXT,
                UNIQUE(symbol, trade_time)
            )
            """
        )

    def __create_eod_ticker_table(self):
        """Initialize eod ticker table"""
        self.sql_service.execute(
            """
            CREATE TABLE IF NOT EXISTS `eod_ticker` (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                
                -- Stock identification
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                stock_number TEXT NOT NULL,
                company_name_en TEXT NOT NULL,
                company_name_vn TEXT NOT NULL,
                
                -- Price information
                average_price DECIMAL(10,2) NOT NULL,
                ceiling_price DECIMAL(10,2) NOT NULL,
                floor_price DECIMAL(10,2) NOT NULL,
                high_price DECIMAL(10,2) NOT NULL,
                low_price DECIMAL(10,2) NOT NULL,
                open_price DECIMAL(10,2) NOT NULL,
                match_price DECIMAL(10,2) NOT NULL,
                reference_price DECIMAL(10,2) NOT NULL,
                last_match_price DECIMAL(10,2) NOT NULL,
                change_price DECIMAL(10,2) NOT NULL,
                
                -- Volume information
                last_volume INTEGER NOT NULL,
                match_volume INTEGER NOT NULL,
                total_shares INTEGER NOT NULL,
                market_cap DECIMAL(15,2) NOT NULL,
                shares_outstanding INTEGER NOT NULL,
                
                -- Order book information
                bid1_price DECIMAL(10,2) NOT NULL,
                bid1_volume INTEGER NOT NULL,
                bid2_price DECIMAL(10,2) NOT NULL,
                bid2_volume INTEGER NOT NULL,
                bid3_price DECIMAL(10,2) NOT NULL,
                bid3_volume INTEGER NOT NULL,
                
                ask1_price DECIMAL(10,2) NOT NULL,
                ask1_volume INTEGER NOT NULL,
                ask2_price DECIMAL(10,2) NOT NULL,
                ask2_volume INTEGER NOT NULL,
                ask3_price DECIMAL(10,2) NOT NULL,
                ask3_volume INTEGER NOT NULL,
                
                -- Trading information
                market_status TEXT NOT NULL,
                trading_status TEXT NOT NULL,
                total_trading_value DECIMAL(15,2) NOT NULL,
                total_trading_shares INTEGER NOT NULL,
                trading_unit INTEGER NOT NULL,
                
                -- Additional information
                indices TEXT,  -- Stored as comma-separated list
                last_trading_date TEXT,
                last_update_time INTEGER NOT NULL,
                
                -- Ensure uniqueness of symbol at any given time
                UNIQUE(symbol)
            )
            """
        )
