"""
Price data repository for accessing and managing stock price data.

This module provides a clean interface for accessing price data from various sources
while abstracting away the underlying storage mechanisms.
"""

from datetime import datetime
from typing import List, Optional, Protocol

from src.infrastructure.persistence.sql_service import SqliteService
from src.domain.entities.stock import DailyPrice, MinutePrice, PriceData
from src.infrastructure.persistence import DailyPriceDao, MinutePriceDao

from src.domain.entities.stock_models import PricePoint
from src.domain.exceptions.stock_exceptions import DatabaseException, SymbolNotFoundException
from shared.utils.validation import validate_symbol, validate_days_back


class PriceRepositoryInterface(Protocol):
    """Interface for price data repositories."""

    def get_daily_prices(
        self,
        symbol: str,
        days: int,
        end_date: Optional[datetime] = None
    ) -> List[PricePoint]:
        """Get daily price data for a symbol."""
        ...

    def get_minute_prices(
        self,
        symbol: str,
        days: int,
        end_date: Optional[datetime] = None
    ) -> List[PricePoint]:
        """Get minute price data for a symbol."""
        ...

    def save_daily_prices(self, symbol: str, prices: List[PricePoint]) -> None:
        """Save daily price data."""
        ...

    def save_minute_prices(self, symbol: str, prices: List[PricePoint]) -> None:
        """Save minute price data."""
        ...


class SqlitePriceRepository:
    """SQLite implementation of the price repository."""

    def __init__(self, sql_service: Optional[SqliteService] = None):
        """
        Initialize the repository.

        Args:
            sql_service: Optional SQL service instance. If None, creates a new one.
        """
        self._sql_service = sql_service or SqliteService()
        self._daily_price_dao = DailyPriceDao(sql_service=self._sql_service)
        self._minute_price_dao = MinutePriceDao(sql_service=self._sql_service)

    def get_daily_prices(
        self,
        symbol: str,
        days: int,
        end_date: Optional[datetime] = None
    ) -> List[PricePoint]:
        """
        Get daily price data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to retrieve
            end_date: End date for the data range (defaults to now)

        Returns:
            List of price points

        Raises:
            DatabaseException: If database operation fails
            SymbolNotFoundException: If symbol is not found
        """
        try:
            symbol = validate_symbol(symbol)
            days = validate_days_back(days)

            to_ts = (
                int(end_date.timestamp())
                if end_date
                else int(datetime.now().timestamp())
            )

            daily_prices = self._daily_price_dao.get_last_number_of_prices(
                symbol=symbol,
                number=days,
                to_ts=to_ts
            )

            if not daily_prices:
                raise SymbolNotFoundException(f"No price data found for symbol: {symbol}")

            return [self._convert_to_price_point(price) for price in daily_prices]

        except Exception as e:
            if isinstance(e, (SymbolNotFoundException,)):
                raise
            raise DatabaseException(f"Failed to get daily prices for {symbol}: {str(e)}")

    def get_minute_prices(
        self,
        symbol: str,
        days: int,
        end_date: Optional[datetime] = None
    ) -> List[PricePoint]:
        """
        Get minute price data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to retrieve
            end_date: End date for the data range (defaults to now)

        Returns:
            List of price points

        Raises:
            DatabaseException: If database operation fails
            SymbolNotFoundException: If symbol is not found
        """
        try:
            symbol = validate_symbol(symbol)
            days = validate_days_back(days)

            to_ts = (
                int(end_date.timestamp())
                if end_date
                else int(datetime.now().timestamp())
            )

            minute_prices = self._minute_price_dao.get_last_number_of_prices(
                symbol=symbol,
                number=days * 390,  # Approximate minutes per trading day
                to_ts=to_ts
            )

            if not minute_prices:
                raise SymbolNotFoundException(f"No minute price data found for symbol: {symbol}")

            return [self._convert_to_price_point(price) for price in minute_prices]

        except Exception as e:
            if isinstance(e, (SymbolNotFoundException,)):
                raise
            raise DatabaseException(f"Failed to get minute prices for {symbol}: {str(e)}")

    def save_daily_prices(self, symbol: str, prices: List[PricePoint]) -> None:
        """
        Save daily price data.

        Args:
            symbol: Stock symbol
            prices: List of price points to save

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            symbol = validate_symbol(symbol)

            daily_prices = [
                DailyPrice(
                    symbol=symbol,
                    timestamp=price.timestamp,
                    open_price=price.open_price,
                    highest_price=price.high_price,
                    lowest_price=price.low_price,
                    close_price=price.close_price,
                    match_volume=price.volume,
                    change_price=price.change_price or 0.0,
                    change_price_percent=price.change_percent or 0.0
                )
                for price in prices
            ]

            self._daily_price_dao.upsert(symbol=symbol, prices=daily_prices)

        except Exception as e:
            raise DatabaseException(f"Failed to save daily prices for {symbol}: {str(e)}")

    def save_minute_prices(self, symbol: str, prices: List[PricePoint]) -> None:
        """
        Save minute price data.

        Args:
            symbol: Stock symbol
            prices: List of price points to save

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            symbol = validate_symbol(symbol)

            minute_prices = [
                MinutePrice(
                    symbol=symbol,
                    timestamp=price.timestamp,
                    open_price=price.open_price,
                    highest_price=price.high_price,
                    lowest_price=price.low_price,
                    close_price=price.close_price,
                    match_volume=price.volume,
                    change_price=price.change_price or 0.0,
                    change_price_percent=price.change_percent or 0.0
                )
                for price in prices
            ]

            self._minute_price_dao.upsert(symbol=symbol, prices=minute_prices)

        except Exception as e:
            raise DatabaseException(f"Failed to save minute prices for {symbol}: {str(e)}")

    def _convert_to_price_point(self, price_data: PriceData) -> PricePoint:
        """Convert internal price data to domain model."""
        def safe_get(attr_name):
            return getattr(price_data, attr_name, None)

        return PricePoint(
            timestamp=price_data.timestamp,
            open_price=price_data.open_price,
            high_price=price_data.highest_price,
            low_price=price_data.lowest_price,
            close_price=price_data.close_price,
            volume=price_data.match_volume,
            change_price=safe_get('change_price'),
            change_percent=safe_get('change_price_percent'),

            # Additional fields from enhanced model
            symbol=safe_get('symbol'),
            average_price=safe_get('average_price'),
            close_price_adjusted=safe_get('close_price_adjusted'),
            ceiling_price=safe_get('ceiling_price'),
            floor_price=safe_get('floor_price'),
            reference_price=safe_get('reference_price'),

            # Trading volume and value data
            match_value=safe_get('match_value'),
            deal_value=safe_get('deal_value'),
            deal_volume=safe_get('deal_volume'),

            # Foreign trading data
            foreign_current_room=safe_get('foreign_current_room'),
            foreign_buy_volume=safe_get('foreign_buy_volume'),
            foreign_buy_value=safe_get('foreign_buy_value'),
            foreign_sell_volume=safe_get('foreign_sell_volume'),
            foreign_sell_value=safe_get('foreign_sell_value'),
            foreign_net_volume=safe_get('foreign_net_volume'),
            foreign_net_value=safe_get('foreign_net_value'),
            foreign_match_buy_volume=safe_get('foreign_match_buy_volume'),
            foreign_deal_buy_volume=safe_get('foreign_deal_buy_volume'),

            # Buy/sell trade data
            buy_trade_quantity=safe_get('buy_trade_quantity'),
            buy_trade_volume=safe_get('buy_trade_volume'),
            sell_trade_quantity=safe_get('sell_trade_quantity'),
            sell_trade_volume=safe_get('sell_trade_volume')
        )
