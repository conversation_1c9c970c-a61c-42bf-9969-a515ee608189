import os
import re

import requests

from src.infrastructure.config.util import Utils


class CompanyLogoFetcher:
    """
    Fetcher for common data used across the application.
    """

    def extract_and_download_company_logos(self) -> None:
        """
        Extract and download company logos from cache files.

        This method reads data from cache files, extracts logo URLs for stock symbols,
        and downloads the logos to the web/assets/logos directory if they don't already exist.
        """
        # Create a dictionary to store symbol-logo pairs
        symbols_and_logos: dict[str, str] = {}

        # Get cache directory
        cache_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "cache"
        )

        # Process each file
        for file_name in ["dnse-hnx.txt", "dnse-hsx.txt", "dnse-upcom.txt"]:
            file_path = os.path.join(cache_dir, file_name)

            # Extract symbol-logo pairs from file
            file_symbols = self._extract_symbol_logo_pairs(file_path)

            # Add to main dictionary
            symbols_and_logos.update(file_symbols)

        # Create directory for logos if it doesn't exist
        web_dir = os.path.join(Utils.get_web_dir(), "assets", "logos")
        os.makedirs(web_dir, exist_ok=True)

        # Download logos
        total_logos = len(symbols_and_logos)
        print(f"Found {total_logos} logos to process")

        # Count for statistics
        existing_count = 0
        downloaded_count = 0
        failed_count = 0

        # Create a function to track download status
        def track_download_status(status: str) -> None:
            nonlocal existing_count, downloaded_count, failed_count
            if status == "existing":
                existing_count += 1
            elif status == "downloaded":
                downloaded_count += 1
            elif status == "failed":
                failed_count += 1

        # Process each logo
        for symbol, logo_url in symbols_and_logos.items():
            status = self._download_logo(symbol, logo_url, web_dir)
            track_download_status(status)

        # Print summary
        print("\nDownload Summary:")
        print(f"Total logos processed: {total_logos}")
        print(f"Already existing: {existing_count}")
        print(f"Newly downloaded: {downloaded_count}")
        print(f"Failed to download: {failed_count}")

    def _extract_symbol_logo_pairs(self, file_path: str) -> dict[str, str]:
        """
        Extract symbol-logo pairs from a file.

        Args:
            file_path: Path to the file to extract from

        Returns:
            Dictionary mapping stock symbols to logo URLs
        """
        symbols_and_logos: dict[str, str] = {}

        # Check if file exists
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return symbols_and_logos

        try:
            # Read file content
            with open(file_path, "r", encoding="utf-8") as f:
                file_content = f.read()

            file_name = os.path.basename(file_path)

            # Try to find stock symbols and logos using regex
            # Look for patterns like: "symbol":"AAA"..."logo":"https://..."
            # The symbol and logo might not be adjacent in the JSON
            symbol_pattern = r'"symbol":"([A-Z0-9]+)"'
            logo_pattern = r'"logo":"(https?://[^"]+)"'

            # Find all symbols
            symbols = re.findall(symbol_pattern, file_content)
            # Find all logos
            logos = re.findall(logo_pattern, file_content)

            # If we have the same number of symbols and logos, assume they match
            if symbols and logos and len(symbols) == len(logos):
                for i in range(len(symbols)):
                    symbols_and_logos[symbols[i]] = logos[i]
                    print(f"Found logo for {symbols[i]}: {logos[i]}")

                print(f"Found {len(symbols)} symbol-logo pairs in {file_name}")
            else:
                # Try a different approach - look for complete objects
                pattern = r'"symbol":"([A-Z0-9]+)".*?"logo":"(https?://[^"]+)"'
                matches = re.findall(pattern, file_content)

                if matches:
                    for symbol, logo_url in matches:
                        symbols_and_logos[symbol] = logo_url
                        print(f"Found logo for {symbol}: {logo_url}")
                    print(
                        f"Found {len(matches)} symbol-logo pairs using alternate pattern in {file_name}"
                    )
                else:
                    print(f"No symbol-logo pairs found in {file_name}")

        except Exception as e:
            print(f"Error processing {os.path.basename(file_path)}: {e}")

        return symbols_and_logos

    def _download_logo(self, symbol: str, logo_url: str, web_dir: str) -> str:
        """
        Download a logo for a stock symbol if it doesn't already exist.

        Args:
            symbol: Stock symbol
            logo_url: URL of the logo
            web_dir: Directory to save the logo

        Returns:
            Status string: "existing", "downloaded", or "failed"
        """
        try:
            # Get file extension from URL
            file_extension = os.path.splitext(logo_url)[1]
            if not file_extension:
                file_extension = ".png"  # Default extension

            # Create filename and path
            logo_filename = f"{symbol}{file_extension}"
            logo_path = os.path.join(web_dir, logo_filename)

            # Check if the file already exists
            if os.path.exists(logo_path):
                print(f"Logo for {symbol} already exists at {logo_path}")
                return "existing"

            # Download the logo
            response = requests.get(logo_url)
            response.raise_for_status()

            # Save logo
            with open(logo_path, "wb") as f:
                f.write(response.content)
            print(f"Downloaded logo for {symbol} to {logo_path}")
            return "downloaded"

        except requests.exceptions.RequestException as e:
            print(f"Failed to download logo for {symbol}: {e}")
            return "failed"
        except Exception as e:
            print(f"An error occurred while processing {symbol}: {e}")
            return "failed"
