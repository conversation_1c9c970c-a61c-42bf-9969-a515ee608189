"""
Local caching service for StockPal application.

This module provides a comprehensive caching layer with smart invalidation
strategies for stock data, technical indicators, and analysis results.
"""

import json
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
import hashlib

from src.domain.exceptions.stock_exceptions import CacheException
from src.domain.entities.stock_models import PricePoint, StockAnalysis, TechnicalIndicator


logger = logging.getLogger(__name__)


class CacheService:
    """
    Local file-based caching service with smart invalidation strategies.

    Features:
    - TTL-based cache expiration
    - Smart invalidation based on data freshness
    - Hierarchical cache organization
    - Compression for large datasets
    - Cache statistics and monitoring
    """

    def __init__(self, cache_dir: str = "cache", default_ttl: int = 3600):
        """
        Initialize the cache service.

        Args:
            cache_dir: Directory to store cache files
            default_ttl: Default time-to-live in seconds (1 hour)
        """
        self.cache_dir = Path(cache_dir)
        self.default_ttl = default_ttl
        self._logger = logging.getLogger(__name__)

        # Create cache directory structure
        self._setup_cache_directories()

        # Cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "invalidations": 0,
            "size_bytes": 0
        }

    def _setup_cache_directories(self):
        """Setup cache directory structure."""
        try:
            # Main cache directories
            directories = [
                self.cache_dir,
                self.cache_dir / "prices",
                self.cache_dir / "indicators",
                self.cache_dir / "analysis",
                self.cache_dir / "symbols",
                self.cache_dir / "metadata"
            ]

            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)

            self._logger.info(f"Cache directories initialized at {self.cache_dir}")

        except Exception as e:
            raise CacheException(f"Failed to setup cache directories: {str(e)}")

    def get(self, key: str, category: str = "general") -> Optional[Any]:
        """
        Get value from cache.

        Args:
            key: Cache key
            category: Cache category (prices, indicators, analysis, etc.)

        Returns:
            Cached value or None if not found/expired
        """
        try:
            cache_file = self._get_cache_file_path(key, category)

            if not cache_file.exists():
                self.stats["misses"] += 1
                return None

            # Load cache entry
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_entry = json.load(f)

            # Check if expired
            if self._is_expired(cache_entry):
                self._delete_cache_file(cache_file)
                self.stats["misses"] += 1
                return None

            self.stats["hits"] += 1
            self._logger.debug(f"Cache hit for key: {key}")
            return cache_entry["data"]

        except Exception as e:
            self._logger.warning(f"Failed to get cache for key {key}: {str(e)}")
            self.stats["misses"] += 1
            return None

    def set(self, key: str, value: Any, category: str = "general",
            ttl: Optional[int] = None) -> bool:
        """
        Set value in cache.

        Args:
            key: Cache key
            value: Value to cache
            category: Cache category
            ttl: Time-to-live in seconds (uses default if None)

        Returns:
            True if successful, False otherwise
        """
        try:
            cache_file = self._get_cache_file_path(key, category)
            ttl = ttl or self.default_ttl

            cache_entry = {
                "data": value,
                "timestamp": time.time(),
                "ttl": ttl,
                "expires_at": time.time() + ttl,
                "key": key,
                "category": category
            }

            # Ensure directory exists
            cache_file.parent.mkdir(parents=True, exist_ok=True)

            # Write cache entry
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_entry, f, indent=2, default=self._json_serializer)

            # Update statistics
            self.stats["size_bytes"] += cache_file.stat().st_size

            self._logger.debug(f"Cached value for key: {key}")
            return True

        except Exception as e:
            self._logger.error(f"Failed to set cache for key {key}: {str(e)}")
            return False

    def delete(self, key: str, category: str = "general") -> bool:
        """
        Delete value from cache.

        Args:
            key: Cache key
            category: Cache category

        Returns:
            True if deleted, False if not found
        """
        try:
            cache_file = self._get_cache_file_path(key, category)

            if cache_file.exists():
                self._delete_cache_file(cache_file)
                self.stats["invalidations"] += 1
                self._logger.debug(f"Deleted cache for key: {key}")
                return True

            return False

        except Exception as e:
            self._logger.error(f"Failed to delete cache for key {key}: {str(e)}")
            return False

    def invalidate_category(self, category: str) -> int:
        """
        Invalidate all cache entries in a category.

        Args:
            category: Category to invalidate

        Returns:
            Number of entries invalidated
        """
        try:
            category_dir = self.cache_dir / category

            if not category_dir.exists():
                return 0

            count = 0
            for cache_file in category_dir.rglob("*.json"):
                self._delete_cache_file(cache_file)
                count += 1

            self.stats["invalidations"] += count
            self._logger.info(f"Invalidated {count} cache entries in category: {category}")
            return count

        except Exception as e:
            self._logger.error(f"Failed to invalidate category {category}: {str(e)}")
            return 0

    def cleanup_expired(self) -> int:
        """
        Clean up expired cache entries.

        Returns:
            Number of expired entries removed
        """
        try:
            count = 0

            for cache_file in self.cache_dir.rglob("*.json"):
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cache_entry = json.load(f)

                    if self._is_expired(cache_entry):
                        self._delete_cache_file(cache_file)
                        count += 1

                except Exception as e:
                    self._logger.warning(f"Failed to check cache file {cache_file}: {str(e)}")
                    # Delete corrupted cache files
                    self._delete_cache_file(cache_file)
                    count += 1

            self.stats["invalidations"] += count
            self._logger.info(f"Cleaned up {count} expired cache entries")
            return count

        except Exception as e:
            self._logger.error(f"Failed to cleanup expired cache: {str(e)}")
            return 0

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        # Calculate cache size
        total_size = 0
        total_files = 0

        try:
            for cache_file in self.cache_dir.rglob("*.json"):
                total_size += cache_file.stat().st_size
                total_files += 1
        except Exception as e:
            self._logger.warning(f"Failed to calculate cache size: {str(e)}")

        hit_rate = 0
        total_requests = self.stats["hits"] + self.stats["misses"]
        if total_requests > 0:
            hit_rate = self.stats["hits"] / total_requests * 100

        return {
            **self.stats,
            "hit_rate_percent": round(hit_rate, 2),
            "total_files": total_files,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2)
        }

    def _get_cache_file_path(self, key: str, category: str) -> Path:
        """Get cache file path for a key and category."""
        # Create a safe filename from the key
        safe_key = self._make_safe_filename(key)
        return self.cache_dir / category / f"{safe_key}.json"

    def _make_safe_filename(self, key: str) -> str:
        """Create a safe filename from a cache key."""
        # Hash long keys to avoid filesystem limitations
        if len(key) > 100:
            return hashlib.md5(key.encode()).hexdigest()

        # Replace unsafe characters
        safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_"
        return ''.join(c if c in safe_chars else '_' for c in key)

    def _is_expired(self, cache_entry: Dict) -> bool:
        """Check if a cache entry is expired."""
        return time.time() > cache_entry.get("expires_at", 0)

    def _delete_cache_file(self, cache_file: Path):
        """Delete a cache file and update statistics."""
        try:
            if cache_file.exists():
                size = cache_file.stat().st_size
                cache_file.unlink()
                self.stats["size_bytes"] = max(0, self.stats["size_bytes"] - size)
        except Exception as e:
            self._logger.warning(f"Failed to delete cache file {cache_file}: {str(e)}")

    def _json_serializer(self, obj):
        """Custom JSON serializer for complex objects."""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
