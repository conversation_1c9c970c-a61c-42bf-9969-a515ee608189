from typing import override

from src.infrastructure.config.constants import Constants
from src.infrastructure.adapters.http_service import HttpJsonService
from src.infrastructure.adapters.scraper import Scraper
from src.domain.entities.stock import Stock


class CafefScraper(Scraper):

    def __init__(self, symbol: str):
        super().__init__(symbol)

        self.http_service = HttpJsonService()

    @override
    def fetch_stock_symbols(self) -> list[Stock]:
        stocks: list[Stock] = []

        has_next = True
        page_index = 0
        page_size = 2000

        while has_next:
            url = f"https://cafef.vn/du-lieu/ajax/pagenew/databusiness/congtyniemyet.ashx?centerid=0&skip={page_index * page_size}&take={page_size}&major=0"

            cache_filename = f"symbols_page_{page_index}-cafef.json"
            response = self.http_service.get(
                url=url,
                cache_foldername=self._cache_subfolder,
                cache_filename=cache_filename,
            )

            if not response or "Data" not in response or not response["Data"]:
                print(f"No data found for {cache_filename}")
                has_next = False
                break

            for item in response["Data"]:
                exchange_id = int(item.get("TradeCenterId", "0"))
                if exchange_id == Constants.VIETSTOCK_HNX_EXCHANGE_ID:
                    exchange = "HNX"
                elif exchange_id == Constants.VIETSTOCK_OTC_EXCHANGE_ID:
                    exchange = "OTC"
                elif exchange_id == Constants.VIETSTOCK_UPCOM_EXCHANGE_ID:
                    exchange = "UPCOM"
                else:
                    exchange = "HOSE"
                stocks.append(
                    Stock(
                        symbol=item.get("Symbol", ""),
                        name=item.get("CompanyName", ""),
                        exchange=exchange,
                        industry=item.get("CategoryName", ""),
                    )
                )

            page_index += 1

        return stocks
