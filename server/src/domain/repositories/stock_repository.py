"""
Stock Repository Interface

Abstract interface for stock data persistence operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from ..entities.stock import Stock


class StockRepository(ABC):
    """
    Abstract repository interface for stock data operations.
    
    This interface defines the contract for stock data persistence,
    following the Repository pattern and Dependency Inversion Principle.
    """
    
    @abstractmethod
    async def save(self, stock: Stock) -> Stock:
        """
        Save a stock entity.
        
        Args:
            stock: Stock entity to save
            
        Returns:
            Stock: Saved stock entity
            
        Raises:
            RepositoryError: If save operation fails
        """
        pass
    
    @abstractmethod
    async def find_by_symbol(self, symbol: str, exchange: str) -> Optional[Stock]:
        """
        Find a stock by symbol and exchange.
        
        Args:
            symbol: Stock symbol
            exchange: Stock exchange
            
        Returns:
            Optional[Stock]: Stock entity if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def find_all(self) -> List[Stock]:
        """
        Find all stocks.
        
        Returns:
            List[Stock]: List of all stock entities
        """
        pass
    
    @abstractmethod
    async def find_by_exchange(self, exchange: str) -> List[Stock]:
        """
        Find all stocks in a specific exchange.
        
        Args:
            exchange: Exchange name
            
        Returns:
            List[Stock]: List of stocks in the exchange
        """
        pass
    
    @abstractmethod
    async def find_by_sector(self, sector: str) -> List[Stock]:
        """
        Find all stocks in a specific sector.
        
        Args:
            sector: Sector name
            
        Returns:
            List[Stock]: List of stocks in the sector
        """
        pass
    
    @abstractmethod
    async def update(self, stock: Stock) -> Stock:
        """
        Update a stock entity.
        
        Args:
            stock: Stock entity to update
            
        Returns:
            Stock: Updated stock entity
            
        Raises:
            RepositoryError: If update operation fails
            NotFoundError: If stock not found
        """
        pass
    
    @abstractmethod
    async def delete(self, symbol: str, exchange: str) -> bool:
        """
        Delete a stock by symbol and exchange.
        
        Args:
            symbol: Stock symbol
            exchange: Stock exchange
            
        Returns:
            bool: True if deleted, False if not found
            
        Raises:
            RepositoryError: If delete operation fails
        """
        pass
    
    @abstractmethod
    async def exists(self, symbol: str, exchange: str) -> bool:
        """
        Check if a stock exists.
        
        Args:
            symbol: Stock symbol
            exchange: Stock exchange
            
        Returns:
            bool: True if stock exists, False otherwise
        """
        pass
