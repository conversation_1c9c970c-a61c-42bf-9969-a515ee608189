from datetime import date

class TradingDate:
    def __init__(self, trading_date: date, dow: int):
        self.date = trading_date
        self.dow = dow

    @property
    def date(self) -> date:
        return self._date
    
    @date.setter
    def date(self, value: date):
        self._date = value

    @property
    def dow(self) -> int:
        return self._dow
    
    @dow.setter
    def dow(self, value: int):
        if not 0 <= value <= 6:
            raise ValueError("Day of week must be between 0 and 6")
        self._dow = value