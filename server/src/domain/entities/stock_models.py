"""
Stock domain models for the StockPal application.

This module contains the core domain models that represent the business entities
in the stock analysis system.
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any


class DataProvider(Enum):
    """Enumeration for data providers."""
    SSI = "ssi"
    VIETSTOCK = "vietstock"
    CAFEF = "cafef"


class TrendDirection(Enum):
    """Enumeration for trend directions."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    UNKNOWN = "unknown"


class SignalType(Enum):
    """Enumeration for signal types."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    NEUTRAL = "neutral"


class ConfidenceLevel(Enum):
    """Enumeration for confidence levels."""
    VERY_HIGH = "very_high"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"


@dataclass
class PricePoint:
    """Represents a single price point in time."""
    timestamp: int
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float  # Changed to float to match original
    change_price: Optional[float] = None
    change_percent: Optional[float] = None

    # Additional fields from original PriceData model
    symbol: Optional[str] = None
    average_price: Optional[float] = None
    close_price_adjusted: Optional[float] = None
    ceiling_price: Optional[float] = None
    floor_price: Optional[float] = None
    reference_price: Optional[float] = None

    # Trading volume and value data
    match_value: Optional[float] = None
    deal_value: Optional[float] = None
    deal_volume: Optional[float] = None

    # Foreign trading data
    foreign_current_room: Optional[int] = None
    foreign_buy_volume: Optional[float] = None
    foreign_buy_value: Optional[float] = None
    foreign_sell_volume: Optional[float] = None
    foreign_sell_value: Optional[float] = None
    foreign_net_volume: Optional[float] = None
    foreign_net_value: Optional[float] = None
    foreign_match_buy_volume: Optional[float] = None
    foreign_deal_buy_volume: Optional[float] = None

    # Buy/sell trade data
    buy_trade_quantity: Optional[int] = None
    buy_trade_volume: Optional[float] = None
    sell_trade_quantity: Optional[int] = None
    sell_trade_volume: Optional[float] = None

    def calc_change_percent(self) -> None:
        """Calculate change percentage based on reference price."""
        if (self.reference_price and self.reference_price != 0 and
                self.change_price is not None):
            self.change_percent = round(
                self.change_price / self.reference_price * 100, 2
            )


@dataclass
class TechnicalIndicator:
    """Represents a technical indicator value."""
    name: str
    value: Optional[float]
    signal: SignalType
    confidence: ConfidenceLevel
    description: str
    timestamp: int


@dataclass
class TradingZone:
    """Represents a trading zone (buy, sell, stop-loss)."""
    price: float
    confidence: ConfidenceLevel
    reason: str
    zone_type: str  # 'buy', 'sell', 'stop_loss', 'take_profit'


@dataclass
class RiskRewardRatio:
    """Represents a risk-reward analysis."""
    buy_price: float
    stop_loss_price: float
    take_profit_price: float
    ratio: float
    quality: str


@dataclass
class TrendAnalysis:
    """Represents trend analysis results."""
    direction: TrendDirection
    strength: float  # 0.0 to 1.0
    confidence: ConfidenceLevel
    duration_days: Optional[int] = None


class MarketCondition(Enum):
    """Represents overall market condition."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    VOLATILE = "volatile"


@dataclass
class StockAnalysis:
    """Complete stock analysis result."""
    symbol: str
    current_price: float
    analysis_date: datetime
    last_trading_date: datetime

    # Trend information
    trend_analysis: TrendAnalysis
    price_change: float
    price_change_percent: float

    # Technical indicators
    technical_indicators: List[TechnicalIndicator]

    # Trading zones
    buy_zones: List[TradingZone]
    stop_loss_zones: List[TradingZone]
    take_profit_zones: List[TradingZone]

    # Risk analysis
    risk_reward_ratios: List[RiskRewardRatio]

    # Recommendations
    recommendation: SignalType
    market_condition: MarketCondition

    # Additional metadata
    technical_summary: str
    confidence_score: float


@dataclass
class IndicatorConfig:
    """Configuration for technical indicators."""
    name: str
    period: int
    parameters: Dict[str, Any]
    enabled: bool = True


@dataclass
class AnalysisRequest:
    """Request for stock analysis."""
    symbol: str
    end_date: Optional[datetime] = None
    days_back: int = 365
    indicators: Optional[List[IndicatorConfig]] = None
    include_zones: bool = True
    include_risk_analysis: bool = True


@dataclass
class DataFetchRequest:
    """Request for data fetching."""
    symbol: str
    provider: DataProvider = DataProvider.SSI
    timeframe_minutes: bool = False
    force_refresh: bool = False
