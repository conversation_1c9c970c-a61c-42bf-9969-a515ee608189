"""
Price Entity

Core business entity representing price data for a stock.
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime
from decimal import Decimal


@dataclass
class Price:
    """
    Price entity representing stock price data at a specific point in time.
    
    This entity encapsulates the core business rules for price data,
    ensuring data integrity and providing business logic methods.
    """
    
    symbol: str
    date: datetime
    open_price: Decimal
    high_price: Decimal
    low_price: Decimal
    close_price: Decimal
    volume: int
    adjusted_close: Optional[Decimal] = None
    
    def __post_init__(self):
        """Validate price data after initialization."""
        if not self.symbol:
            raise ValueError("Symbol cannot be empty")
        
        if self.open_price <= 0:
            raise ValueError("Open price must be positive")
        
        if self.high_price <= 0:
            raise ValueError("High price must be positive")
        
        if self.low_price <= 0:
            raise ValueError("Low price must be positive")
        
        if self.close_price <= 0:
            raise ValueError("Close price must be positive")
        
        if self.volume < 0:
            raise ValueError("Volume cannot be negative")
        
        # Validate price relationships
        if self.high_price < max(self.open_price, self.close_price, self.low_price):
            raise ValueError("High price must be the highest price of the day")
        
        if self.low_price > min(self.open_price, self.close_price, self.high_price):
            raise ValueError("Low price must be the lowest price of the day")
        
        # Set adjusted close to close if not provided
        if self.adjusted_close is None:
            self.adjusted_close = self.close_price
        
        # Normalize symbol
        self.symbol = self.symbol.upper()
    
    def get_price_change(self) -> Decimal:
        """
        Calculate the price change from open to close.
        
        Returns:
            Decimal: Price change (close - open)
        """
        return self.close_price - self.open_price
    
    def get_price_change_percent(self) -> Decimal:
        """
        Calculate the percentage price change from open to close.
        
        Returns:
            Decimal: Percentage change
        """
        if self.open_price == 0:
            return Decimal('0')
        
        return (self.get_price_change() / self.open_price) * Decimal('100')
    
    def get_trading_range(self) -> Decimal:
        """
        Calculate the trading range (high - low).
        
        Returns:
            Decimal: Trading range
        """
        return self.high_price - self.low_price
    
    def get_typical_price(self) -> Decimal:
        """
        Calculate the typical price (HLC/3).
        
        Returns:
            Decimal: Typical price
        """
        return (self.high_price + self.low_price + self.close_price) / Decimal('3')
    
    def is_up_day(self) -> bool:
        """
        Check if this is an up day (close > open).
        
        Returns:
            bool: True if close > open
        """
        return self.close_price > self.open_price
    
    def is_down_day(self) -> bool:
        """
        Check if this is a down day (close < open).
        
        Returns:
            bool: True if close < open
        """
        return self.close_price < self.open_price
    
    def is_doji(self, threshold: Decimal = Decimal('0.1')) -> bool:
        """
        Check if this is a doji candle (open ≈ close).
        
        Args:
            threshold: Percentage threshold for doji detection
            
        Returns:
            bool: True if this is a doji
        """
        change_percent = abs(self.get_price_change_percent())
        return change_percent <= threshold
    
    def __str__(self) -> str:
        """String representation of the price."""
        return f"Price({self.symbol}, {self.date.date()}, {self.close_price})"
    
    def __eq__(self, other) -> bool:
        """Check equality based on symbol and date."""
        if not isinstance(other, Price):
            return False
        return self.symbol == other.symbol and self.date == other.date
    
    def __hash__(self) -> int:
        """Hash based on symbol and date."""
        return hash((self.symbol, self.date))
