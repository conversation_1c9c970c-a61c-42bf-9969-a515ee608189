from datetime import datetime
from typing import Dict, List, Optional, Tuple

from src.domain.entities.stock import PriceData
from src.domain.services.trend_predictor import TrendPredictor
from stockpal.indicator.ichimoku import <PERSON><PERSON><PERSON><PERSON>
from stockpal.indicator.pivot_points import PivotPoints
from src.domain.services.signal_enhancer import SignalEnhancer
from stockpal.indicator.elliott_wave import ElliottWaveAnalyzer
from stockpal.sentiment.market_sentiment import MarketSentiment
from src.domain.services.tsf import TimeSeriesForecast


class SignalSynthesizer:
    """
    Synthesizes signals from multiple indicators to provide comprehensive trading recommendations.
    """

    def __init__(self, symbol: str, prices: list[PriceData], language: str = "en"):
        self.symbol = symbol
        self.prices = prices
        self.language = language.lower()  # 'en' for English, 'vi' for Vietnamese
        
        # Initialize TrendPredictor with the same language
        self.trend_predictor = TrendPredictor(symbol=symbol, prices=prices, language=self.language)
        
        # Add Ichimoku to TrendPredictor
        self.ichimoku = <PERSON><PERSON><PERSON><PERSON>(symbol, prices)
        
        # Confidence level labels
        self.confidence_labels = {
            "en": {
                "high": "high",
                "medium": "medium",
                "low": "low"
            },
            "vi": {
                "high": "cao",
                "medium": "trung bình",
                "low": "thấp"
            }
        }
        
        # Quality labels for risk-reward ratios
        self.quality_labels = {
            "en": {
                "excellent": "excellent",
                "good": "good",
                "fair": "fair",
                "poor": "poor"
            },
            "vi": {
                "excellent": "xuất sắc",
                "good": "tốt",
                "fair": "khá",
                "poor": "kém"
            }
        }
        
        # Reason translations
        self.reason_translations = {
            "en": {},  # No translation needed for English
            "vi": {
                "Strong support level": "Vùng hỗ trợ mạnh",
                "Support level": "Vùng hỗ trợ",
                "Resistance level": "Vùng kháng cự",
                "Strong resistance level": "Vùng kháng cự mạnh"
            }
        }

    def analyze(self) -> Dict:
        """
        Analyze the stock and provide comprehensive analysis combining all technical indicators.
        
        The analysis integrates multiple indicators with adaptive weighting to determine:
        - Current trend direction and strength
        - Support and resistance levels
        - Optimal buy, stop loss, and take profit zones
        - Final trading recommendation with confidence level
        
        Returns:
            Dict containing comprehensive analysis results
        """
        # Get trend analysis and price targets from TrendPredictor
        trend = self.trend_predictor.predict_trend()
        price_targets = self.trend_predictor.calculate_price_targets()
        signals = self.trend_predictor.generate_trading_signals()

        # Current price
        current_price = trend.get("current_price")
        if not current_price:
            return {"error": "No current price available"}

        # Sort prices chronologically
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        # === ADDITIONAL INDICATOR ANALYSIS ===
        
        # Add Ichimoku Cloud analysis
        ichimoku_values = self.ichimoku.calculate()
        ichimoku_signals = self.ichimoku.get_signals()
        
        # Add pivot points analysis
        pivot = PivotPoints(self.symbol, sorted_prices)
        pivot_levels = pivot.calculate()
        pivot_signals = pivot.get_signals()
        
        # Add Elliott Wave analysis
        elliott = ElliottWaveAnalyzer(self.symbol, sorted_prices)
        wave_analysis = elliott.analyze()
        
        # Add Market Sentiment analysis
        sentiment = MarketSentiment(self.symbol)
        sentiment_data = sentiment.get_sentiment()
        
        # Add TSF (Time Series Forecast) analysis
        tsf = TimeSeriesForecast(self.symbol, sorted_prices)
        tsf_signals = tsf.get_signals()
        latest_tsf_signal = tsf_signals[-1] if tsf_signals else None
        tsf_signal_type = "Trung tính"
        tsf_action = "Theo dõi thêm"
        tsf_value = None
        if latest_tsf_signal:
            tsf_value = latest_tsf_signal["forecast"]
            if latest_tsf_signal["signal"] == "bullish":
                tsf_signal_type = "Mua"
                tsf_action = "TSF dự báo xu hướng tăng"
            elif latest_tsf_signal["signal"] == "bearish":
                tsf_signal_type = "Bán"
                tsf_action = "TSF dự báo xu hướng giảm"
        
        # === SIGNAL INTEGRATION ===
        
        # Initialize indicator signals array
        indicator_signals = []
        
        # Add pivot points signal
        indicator_signals.append({
            "name": "Pivot Points",
            "value": None,
            "signal": pivot_signals.get("signal", {}).get("type", "Trung tính") if isinstance(pivot_signals, dict) and "signal" in pivot_signals else "Trung tính",
            "action": f"S1: {pivot_levels['S1']:.2f}, R1: {pivot_levels['R1']:.2f}, Pivot: {pivot_levels['PP']:.2f}",
            "description": "Pivot Points",
            "info": "Pivot Points xác định các mức hỗ trợ/kháng cự dựa trên giá cả phiên trước."
        })
        
        # Add Ichimoku Cloud signal
        latest_ichimoku_signal = next((s for s in reversed(ichimoku_signals) if "signal" in s), None) if ichimoku_signals else None
        ichimoku_signal_type = "Trung tính"
        ichimoku_action = "Theo dõi thêm"
        
        if latest_ichimoku_signal:
            signal = latest_ichimoku_signal.get("signal", "")
            strength = latest_ichimoku_signal.get("strength", "")
            
            if "bullish" in signal:
                ichimoku_signal_type = "Mua"
                ichimoku_action = f"Tín hiệu {signal} ({strength}): " + (
                    "Tenkan-sen vượt lên trên Kijun-sen" if "tk_cross" in signal else
                    "Giá vượt lên trên mây Kumo" if "kumo_breakout" in signal else
                    "Mây Kumo đổi màu từ đỏ sang xanh" if "kumo_twist" in signal else
                    "Tín hiệu mua từ Ichimoku"
                )
            elif "bearish" in signal:
                ichimoku_signal_type = "Bán"
                ichimoku_action = f"Tín hiệu {signal} ({strength}): " + (
                    "Tenkan-sen giảm xuống dưới Kijun-sen" if "tk_cross" in signal else
                    "Giá giảm xuống dưới mây Kumo" if "kumo_breakout" in signal else
                    "Mây Kumo đổi màu từ xanh sang đỏ" if "kumo_twist" in signal else
                    "Tín hiệu bán từ Ichimoku"
                )
        
        indicator_signals.append({
            "name": "Ichimoku Cloud",
            "value": None,
            "signal": ichimoku_signal_type,
            "action": ichimoku_action,
            "description": "Ichimoku Kinko Hyo",
            "info": "Ichimoku Cloud là chỉ báo đa năng xác định xu hướng, hỗ trợ/kháng cự, và tín hiệu mua/bán dựa trên các đường Tenkan-sen, Kijun-sen và mây Kumo."
        })

        # Add Elliott Wave signal
        indicator_signals.append({
            "name": "Elliott Wave",
            "value": wave_analysis.get("current_wave"),
            "signal": wave_analysis.get("signal", "Trung tính"),
            "action": wave_analysis.get("recommendation", "Theo dõi thêm"),
            "description": "Elliott Wave Analysis",
            "info": "Phân tích sóng Elliott giúp dự đoán các mẫu giá có thể lặp lại theo 5-3 sóng."
        })

        # Add Market Sentiment signal
        indicator_signals.append({
            "name": "Market Sentiment",
            "value": sentiment_data.get("score"),
            "signal": sentiment_data.get("signal", "Trung tính"),
            "action": sentiment_data.get("recommendation", "Theo dõi thêm"),
            "description": "Phân tích tâm lý thị trường",
            "info": "Đánh giá tâm lý thị trường dựa trên các nguồn dữ liệu bên ngoài và phương tiện truyền thông xã hội."
        })

        # Add TSF (Time Series Forecast) analysis
        indicator_signals.append({
            "name": "TSF (Time Series Forecast)",
            "value": tsf_value,
            "signal": tsf_signal_type,
            "action": tsf_action,
            "description": "Time Series Forecast (TSF)",
            "info": "TSF sử dụng hồi quy tuyến tính để dự báo giá tiếp theo và xác định xu hướng."
        })

        # === ADAPTIVE SIGNAL WEIGHTING ===
        
        # Use SignalEnhancer to weight indicators based on historical effectiveness
        enhancer = SignalEnhancer()
        market_condition = trend["market_condition"]
        enhanced_signals = enhancer.enhance_signals(self.symbol, indicator_signals, market_condition)
        
        # === ZONES DETERMINATION ===
        
        # Determine buy zones with combined signals
        buy_zones = self._determine_buy_zones(trend, price_targets, signals, ichimoku_values, pivot_levels)

        # Determine stop loss zones with combined signals
        stop_loss_zones = self._determine_stop_loss_zones(trend, price_targets, signals, ichimoku_values, pivot_levels)

        # Determine take profit zones with combined signals
        take_profit_zones = self._determine_take_profit_zones(trend, price_targets, signals, ichimoku_values, pivot_levels)

        # Calculate risk-reward ratios
        risk_reward_ratios = self._calculate_risk_reward_ratios(buy_zones, stop_loss_zones, take_profit_zones)

        # Use the localized direction from trend
        trend_direction = trend["direction"]
        
        # === FINAL RECOMMENDATION ===
        
        # Generate final recommendation combining all signals
        recommendation = self._generate_combined_recommendation(
            trend, 
            buy_zones, 
            stop_loss_zones, 
            take_profit_zones, 
            enhanced_signals,
            wave_analysis,
            sentiment_data
        )

        return {
            "timestamp": datetime.now(),
            "symbol": self.symbol,
            "current_price": current_price,
            "trend_direction": trend_direction,
            "trend_strength": trend["strength"],
            "trend_confidence": trend["confidence"],
            "market_condition": trend["market_condition"],
            "buy_zones": buy_zones,
            "stop_loss_zones": stop_loss_zones,
            "take_profit_zones": take_profit_zones,
            "risk_reward_ratios": risk_reward_ratios,
            "recommendation": recommendation,
            "enhanced_signals": enhanced_signals,
            "ichimoku": {
                "values": ichimoku_values,
                "signals": ichimoku_signals[-3:] if len(ichimoku_signals) > 3 else ichimoku_signals
            },
            "elliott_wave": wave_analysis,
            "pivot_points": pivot_levels,
            "sentiment": sentiment_data
        }

    def _translate_reason(self, reason: str) -> str:
        """
        Translate reason text based on the selected language.
        
        Args:
            reason: The original reason text
        
        Returns:
            str: Translated reason text
        """
        if self.language == "en":
            return reason
        
        # For Vietnamese, translate the reason
        if self.language == "vi":
            # Add translations for new indicator reasons
            additional_translations = {
                "Ichimoku Tenkan-sen support": "Hỗ trợ Tenkan-sen của Ichimoku",
                "Ichimoku Kijun-sen support": "Hỗ trợ Kijun-sen của Ichimoku",
                "Ichimoku Kijun-sen level": "Mức Kijun-sen của Ichimoku",
                "Ichimoku Cloud support": "Hỗ trợ mây Kumo của Ichimoku",
                "Ichimoku Cloud bottom": "Đáy mây Kumo của Ichimoku",
                "Ichimoku Cloud top resistance": "Kháng cự đỉnh mây Kumo của Ichimoku",
                "Ichimoku future cloud resistance": "Kháng cự mây Kumo tương lai",
                "Pivot Point S1": "Điểm hỗ trợ S1",
                "Pivot Point S2": "Điểm hỗ trợ S2",
                "Pivot Point S3": "Điểm hỗ trợ S3",
                "Pivot Point R1": "Điểm kháng cự R1",
                "Pivot Point R2": "Điểm kháng cự R2",
                "Pivot Point R3": "Điểm kháng cự R3"
            }
            
            # Add new translations to the main dictionary
            all_translations = {**self.reason_translations["vi"], **additional_translations}
            
            # Split the reason into parts (e.g., "Strong support level (fibonacci_0)")
            parts = reason.split(" (")
            if len(parts) == 2:
                base_text = parts[0]
                indicator_part = parts[1]
                
                # Translate the base text
                translated_base = all_translations.get(base_text, base_text)
                
                # Return the translated reason
                return f"{translated_base} ({indicator_part}"
            
            # Direct translation without parentheses
            return all_translations.get(reason, reason)
        
        # If no translation or format doesn't match, return original
        return reason

    def _determine_buy_zones(
        self, trend: Dict, price_targets: Dict, signals: Dict, 
        ichimoku_values: Dict = None, pivot_levels: Dict = None
    ) -> List[Dict]:
        """
        Determine potential buy zones based on trend analysis, price targets, and additional indicators.
        
        Combines multiple sources to identify optimal entry points:
        - Trend analysis and direction
        - Support levels from price targets
        - Ichimoku Cloud components (if available)
        - Pivot Points (if available)

        Args:
            trend: Trend analysis from TrendPredictor
            price_targets: Price targets from TrendPredictor
            signals: Trading signals from TrendPredictor
            ichimoku_values: Ichimoku Cloud indicator values (optional)
            pivot_levels: Pivot Points values (optional)

        Returns:
            List of buy zones with price levels and confidence
        """
        buy_zones = []
        current_price = trend.get("current_price")
        
        # Get prices by ascending order
        prices_by_level = []

        # If bullish trend, consider current price and support levels
        if trend["raw_direction"] == "bullish":
            # Add current price as a potential buy zone with high confidence
            if trend["confidence"] > 70:
                buy_zones.append(
                    {
                        "price": current_price,
                        "confidence": "high",
                        "reason": "Strong bullish trend",
                    }
                )
            elif trend["confidence"] > 50:
                buy_zones.append(
                    {
                        "price": current_price,
                        "confidence": "medium",
                        "reason": "Moderate bullish trend",
                    }
                )

            # Check if support_levels is a list of dictionaries or a list of floats
            if "support_levels" in price_targets:
                for level in price_targets.get("support_levels", []):
                    # Handle the case where level is a dictionary
                    if isinstance(level, dict) and "level" in level:
                        if level["level"] < current_price:
                            confidence = "high" if level.get("strength") == "strong" else "medium"
                            buy_zones.append(
                                {
                                    "price": level["level"],
                                    "confidence": confidence,
                                    "reason": f"Support level ({level.get('source', 'technical')})",
                                }
                            )
                            prices_by_level.append(level["level"])
                    # Handle the case where level is a float
                    elif isinstance(level, (int, float)):
                        if level < current_price:
                            buy_zones.append(
                                {
                                    "price": level,
                                    "confidence": "medium",
                                    "reason": "Support level",
                                }
                            )
                            prices_by_level.append(level)

        # If bearish or neutral trend, only consider strong support levels
        else:
            if "support_levels" in price_targets:
                for level in price_targets.get("support_levels", []):
                    # Handle the case where level is a dictionary
                    if isinstance(level, dict) and "strength" in level and "level" in level:
                        if level["strength"] == "strong" and level["level"] < current_price:
                            buy_zones.append(
                                {
                                    "price": level["level"],
                                    "confidence": "medium",
                                    "reason": f"Strong support level ({level.get('source', 'technical')})",
                                }
                            )
                            prices_by_level.append(level["level"])
                    # Handle the case where level is a float
                    elif isinstance(level, (int, float)):
                        if level < current_price:
                            buy_zones.append(
                                {
                                    "price": level,
                                    "confidence": "medium",
                                    "reason": "Support level",
                                }
                            )
                            prices_by_level.append(level)
        
        # Add Ichimoku Cloud levels if available
        if ichimoku_values:
            # Get latest values
            latest_idx = -1
            tenkan = ichimoku_values.get("tenkan_sen", [])
            kijun = ichimoku_values.get("kijun_sen", [])
            senkou_a = ichimoku_values.get("senkou_span_a", [])
            senkou_b = ichimoku_values.get("senkou_span_b", [])
            
            # Only consider levels below current price
            if tenkan and len(tenkan) > 0 and tenkan[latest_idx] is not None and tenkan[latest_idx] < current_price:
                buy_zones.append({
                    "price": tenkan[latest_idx],
                    "confidence": "medium",
                    "reason": "Ichimoku Tenkan-sen support"
                })
                prices_by_level.append(tenkan[latest_idx])
                
            if kijun and len(kijun) > 0 and kijun[latest_idx] is not None and kijun[latest_idx] < current_price:
                buy_zones.append({
                    "price": kijun[latest_idx],
                    "confidence": "high",
                    "reason": "Ichimoku Kijun-sen support"
                })
                prices_by_level.append(kijun[latest_idx])
                
            # Use the bottom of the cloud as support if price is above the cloud
            if senkou_a and senkou_b and len(senkou_a) > 0 and len(senkou_b) > 0:
                cloud_bottom = min(senkou_a[latest_idx] or float('inf'), senkou_b[latest_idx] or float('inf'))
                if cloud_bottom != float('inf') and cloud_bottom < current_price:
                    buy_zones.append({
                        "price": cloud_bottom,
                        "confidence": "high",
                        "reason": "Ichimoku Cloud support"
                    })
                    prices_by_level.append(cloud_bottom)
        
        # Add Pivot Points if available
        if pivot_levels:
            # Add support levels from pivot points
            for level_name in ["S1", "S2", "S3"]:
                if level_name in pivot_levels and pivot_levels[level_name] < current_price:
                    confidence = "high" if level_name == "S1" else "medium"
                    buy_zones.append({
                        "price": pivot_levels[level_name],
                        "confidence": confidence,
                        "reason": f"Pivot Point {level_name}"
                    })
                    prices_by_level.append(pivot_levels[level_name])
        
        # Remove duplicate or very close price levels
        filtered_zones = []
        for zone in buy_zones:
            # Check if we already have a very similar price level
            if not any(abs(existing["price"] - zone["price"])/zone["price"] < 0.01 for existing in filtered_zones):
                filtered_zones.append(zone)
        
        # Sort buy zones by price (ascending)
        filtered_zones.sort(key=lambda x: x["price"])

        # Use localized confidence labels and translate reasons
        for zone in filtered_zones:
            raw_confidence = zone["confidence"]
            zone["confidence"] = self.confidence_labels.get(self.language, 
                                                          self.confidence_labels["en"]).get(raw_confidence)
            zone["reason"] = self._translate_reason(zone["reason"])
        
        return filtered_zones

    def _determine_stop_loss_zones(
        self, trend: Dict, price_targets: Dict, signals: Dict,
        ichimoku_values: Dict = None, pivot_levels: Dict = None
    ) -> List[Dict]:
        """
        Determine potential stop loss zones based on trend analysis, price targets and additional indicators.
        
        Combines multiple sources to identify optimal stop loss points:
        - Support levels from price targets
        - Algorithmic stop loss from TrendPredictor signals
        - Ichimoku Cloud components (if available)
        - Pivot Points (if available)

        Args:
            trend: Trend analysis from TrendPredictor
            price_targets: Price targets from TrendPredictor
            signals: Trading signals from TrendPredictor
            ichimoku_values: Ichimoku Cloud indicator values (optional)
            pivot_levels: Pivot Points values (optional)

        Returns:
            List of stop loss zones with price levels and confidence
        """
        stop_loss_zones = []
        current_price = trend.get("current_price")

        # Use stop loss from signals if available
        if signals.get("stop_loss"):
            stop_loss_zones.append(
                {
                    "price": signals["stop_loss"],
                    "confidence": "high",
                    "reason": "Algorithmic stop loss",
                }
            )

        # Add support levels as potential stop loss zones (below current price)
        if "support_levels" in price_targets:
            for level in price_targets.get("support_levels", []):
                # Handle the case where level is a dictionary
                if isinstance(level, dict) and "level" in level:
                    if level["level"] < current_price:
                        # Avoid duplicates
                        if not any(
                            abs(zone["price"] - level["level"]) / level["level"] < 0.01
                            for zone in stop_loss_zones
                        ):
                            confidence = "high" if level.get("strength") == "strong" else "medium"
                            stop_loss_zones.append(
                                {
                                    "price": level["level"],
                                    "confidence": confidence,
                                    "reason": f"Support level ({level.get('source', 'technical')})",
                                }
                            )
                # Handle the case where level is a float
                elif isinstance(level, (int, float)):
                    if level < current_price:
                        # Avoid duplicates
                        if not any(
                            abs(zone["price"] - level) / level < 0.01
                            for zone in stop_loss_zones
                        ):
                            stop_loss_zones.append(
                                {
                                    "price": level,
                                    "confidence": "medium",
                                    "reason": "Support level",
                                }
                            )
        
        # Add Ichimoku Cloud levels if available
        if ichimoku_values:
            # Get latest values
            latest_idx = -1
            kijun = ichimoku_values.get("kijun_sen", [])
            senkou_a = ichimoku_values.get("senkou_span_a", [])
            senkou_b = ichimoku_values.get("senkou_span_b", [])
            
            # Kijun-sen can act as a stop loss in a bullish trend
            if kijun and len(kijun) > 0 and kijun[latest_idx] is not None and kijun[latest_idx] < current_price:
                # Avoid duplicates
                if not any(
                    abs(zone["price"] - kijun[latest_idx]) / kijun[latest_idx] < 0.01
                    for zone in stop_loss_zones
                ):
                    stop_loss_zones.append({
                        "price": kijun[latest_idx],
                        "confidence": "medium",
                        "reason": "Ichimoku Kijun-sen level"
                    })
            
            # Cloud bottom can act as stop loss
            if senkou_a and senkou_b and len(senkou_a) > 0 and len(senkou_b) > 0:
                cloud_bottom = min(senkou_a[latest_idx] or float('inf'), senkou_b[latest_idx] or float('inf'))
                if cloud_bottom != float('inf') and cloud_bottom < current_price:
                    # Avoid duplicates
                    if not any(
                        abs(zone["price"] - cloud_bottom) / cloud_bottom < 0.01
                        for zone in stop_loss_zones
                    ):
                        stop_loss_zones.append({
                            "price": cloud_bottom,
                            "confidence": "high", 
                            "reason": "Ichimoku Cloud bottom"
                        })
        
        # Add Pivot Points if available
        if pivot_levels:
            # Use S1, S2 as stop loss levels
            for level_name in ["S1", "S2"]:
                if level_name in pivot_levels and pivot_levels[level_name] < current_price:
                    # Avoid duplicates
                    if not any(
                        abs(zone["price"] - pivot_levels[level_name]) / pivot_levels[level_name] < 0.01
                        for zone in stop_loss_zones
                    ):
                        confidence = "high" if level_name == "S1" else "medium"
                        stop_loss_zones.append({
                            "price": pivot_levels[level_name],
                            "confidence": confidence,
                            "reason": f"Pivot Point {level_name}"
                        })

        # Sort stop loss zones by price (descending)
        stop_loss_zones.sort(key=lambda x: x["price"], reverse=True)

        # Use localized confidence labels and translate reasons
        for zone in stop_loss_zones:
            raw_confidence = zone["confidence"]
            zone["confidence"] = self.confidence_labels.get(self.language, 
                                                          self.confidence_labels["en"]).get(raw_confidence)
            zone["reason"] = self._translate_reason(zone["reason"])
        
        return stop_loss_zones

    def _determine_take_profit_zones(
        self, trend: Dict, price_targets: Dict, signals: Dict,
        ichimoku_values: Dict = None, pivot_levels: Dict = None
    ) -> List[Dict]:
        """
        Determine potential take profit zones based on trend analysis, price targets and additional indicators.
        
        Combines multiple sources to identify optimal profit taking points:
        - Resistance levels from price targets
        - Algorithmic target price from TrendPredictor signals
        - Ichimoku Cloud components (if available)
        - Pivot Points resistance levels (if available)

        Args:
            trend: Trend analysis from TrendPredictor
            price_targets: Price targets from TrendPredictor
            signals: Trading signals from TrendPredictor
            ichimoku_values: Ichimoku Cloud indicator values (optional)
            pivot_levels: Pivot Points values (optional)

        Returns:
            List of take profit zones with price levels and confidence
        """
        take_profit_zones = []
        current_price = trend.get("current_price")

        # Use target price from signals if available
        if signals.get("target_price"):
            take_profit_zones.append(
                {
                    "price": signals["target_price"],
                    "confidence": "high",
                    "reason": "Algorithmic target price",
                }
            )

        # Add resistance levels as potential take profit zones (above current price)
        if "resistance_levels" in price_targets:
            for level in price_targets.get("resistance_levels", []):
                # Handle the case where level is a dictionary
                if isinstance(level, dict) and "level" in level:
                    if level["level"] > current_price:
                        # Avoid duplicates
                        if not any(
                            abs(zone["price"] - level["level"]) / level["level"] < 0.01
                            for zone in take_profit_zones
                        ):
                            confidence = "high" if level.get("strength") == "strong" else "medium"
                            take_profit_zones.append(
                                {
                                    "price": level["level"],
                                    "confidence": confidence,
                                    "reason": f"Resistance level ({level.get('source', 'technical')})",
                                }
                            )
                # Handle the case where level is a float
                elif isinstance(level, (int, float)):
                    if level > current_price:
                        # Avoid duplicates
                        if not any(
                            abs(zone["price"] - level) / level < 0.01
                            for zone in take_profit_zones
                        ):
                            take_profit_zones.append(
                                {
                                    "price": level,
                                    "confidence": "medium",
                                    "reason": "Resistance level",
                                }
                            )
        
        # Add Ichimoku Cloud levels if available
        if ichimoku_values:
            # Get latest values
            latest_idx = -1
            tenkan = ichimoku_values.get("tenkan_sen", [])
            kijun = ichimoku_values.get("kijun_sen", [])
            senkou_a = ichimoku_values.get("senkou_span_a", [])
            senkou_b = ichimoku_values.get("senkou_span_b", [])
            
            # For uptrends, cloud top can be a resistance/take profit target
            if senkou_a and senkou_b and len(senkou_a) > 0 and len(senkou_b) > 0:
                cloud_top = max(senkou_a[latest_idx] or 0, senkou_b[latest_idx] or 0)
                if cloud_top > current_price:
                    # Avoid duplicates
                    if not any(
                        abs(zone["price"] - cloud_top) / cloud_top < 0.01
                        for zone in take_profit_zones
                    ):
                        take_profit_zones.append({
                            "price": cloud_top,
                            "confidence": "medium",
                            "reason": "Ichimoku Cloud top resistance"
                        })
            
            # For stocks above the cloud, use future cloud projections
            future_idx = min(latest_idx + 26, len(senkou_a) - 1) if senkou_a else -1
            if (future_idx >= 0 and senkou_a and senkou_b and 
                len(senkou_a) > future_idx and len(senkou_b) > future_idx):
                future_cloud_top = max(senkou_a[future_idx] or 0, senkou_b[future_idx] or 0)
                if future_cloud_top > current_price:
                    # Avoid duplicates
                    if not any(
                        abs(zone["price"] - future_cloud_top) / future_cloud_top < 0.01
                        for zone in take_profit_zones
                    ):
                        take_profit_zones.append({
                            "price": future_cloud_top,
                            "confidence": "medium",
                            "reason": "Ichimoku future cloud resistance"
                        })
        
        # Add Pivot Points if available
        if pivot_levels:
            # Use R1, R2, R3 as take profit levels
            for level_name in ["R1", "R2", "R3"]:
                if level_name in pivot_levels and pivot_levels[level_name] > current_price:
                    # Avoid duplicates
                    if not any(
                        abs(zone["price"] - pivot_levels[level_name]) / pivot_levels[level_name] < 0.01
                        for zone in take_profit_zones
                    ):
                        confidence = "high" if level_name == "R1" else "medium"
                        take_profit_zones.append({
                            "price": pivot_levels[level_name],
                            "confidence": confidence,
                            "reason": f"Pivot Point {level_name}"
                        })

        # Sort take profit zones by price (ascending)
        take_profit_zones.sort(key=lambda x: x["price"])

        # Use localized confidence labels and translate reasons
        for zone in take_profit_zones:
            raw_confidence = zone["confidence"]
            zone["confidence"] = self.confidence_labels.get(self.language, 
                                                          self.confidence_labels["en"]).get(raw_confidence)
            zone["reason"] = self._translate_reason(zone["reason"])
        
        return take_profit_zones

    def _calculate_risk_reward_ratios(
        self,
        buy_zones: List[Dict],
        stop_loss_zones: List[Dict],
        take_profit_zones: List[Dict],
    ) -> List[Dict]:
        """
        Calculate risk-reward ratios for different combinations of buy, stop loss, and take profit zones.

        Args:
            buy_zones: List of buy zones
            stop_loss_zones: List of stop loss zones
            take_profit_zones: List of take profit zones

        Returns:
            List of risk-reward ratios for different combinations
        """
        risk_reward_ratios = []

        for buy in buy_zones:
            for stop in stop_loss_zones:
                for profit in take_profit_zones:
                    # Only consider valid combinations (buy > stop and profit > buy)
                    if buy["price"] > stop["price"] and profit["price"] > buy["price"]:
                        risk = buy["price"] - stop["price"]
                        reward = profit["price"] - buy["price"]
                        ratio = round(reward / risk, 2) if risk > 0 else None

                        if ratio and ratio > 0:
                            risk_reward_ratios.append(
                                {
                                    "buy_price": buy["price"],
                                    "stop_loss_price": stop["price"],
                                    "take_profit_price": profit["price"],
                                    "risk_reward_ratio": ratio,
                                    "quality": (
                                        "excellent"
                                        if ratio >= 3
                                        else "good" if ratio >= 2 else "acceptable"
                                    ),
                                }
                            )

        # Sort by risk-reward ratio (descending)
        risk_reward_ratios.sort(key=lambda x: x["risk_reward_ratio"], reverse=True)

        # Use localized quality labels
        for ratio in risk_reward_ratios:
            raw_quality = ratio["quality"]
            ratio["quality"] = self.quality_labels.get(self.language, 
                                                       self.quality_labels["en"]).get(raw_quality)
        
        return risk_reward_ratios

    def _generate_combined_recommendation(
        self,
        trend: Dict,
        buy_zones: List[Dict],
        stop_loss_zones: List[Dict],
        take_profit_zones: List[Dict],
        indicator_signals: List[Dict],
        wave_analysis: Dict,
        sentiment_data: Dict
    ) -> str:
        """
        Generate final recommendation combining all analyses with priority to dominant signals.
        
        Args:
            trend: Trend analysis dictionary
            buy_zones: List of buy zones
            stop_loss_zones: List of stop loss zones
            take_profit_zones: List of take profit zones
            indicator_signals: List of indicator signals
            wave_analysis: Elliott wave analysis results
            sentiment_data: Market sentiment data
            
        Returns:
            String containing the final recommendation
        """
        # Extract key metrics
        direction = trend.get("raw_direction", "neutral")
        strength = trend.get("raw_strength", "weak")
        confidence = trend.get("confidence", 0)
        current_price = trend.get("current_price", 0)
        
        # Count the buy/sell/neutral signals from technical indicators
        buy_signals = sum(1 for signal in indicator_signals if signal.get("signal") == "Mua")
        sell_signals = sum(1 for signal in indicator_signals if signal.get("signal") == "Bán")
        neutral_signals = sum(1 for signal in indicator_signals if signal.get("signal") == "Trung tính")
        
        # Determine predominant signal from indicators
        total_signals = buy_signals + sell_signals + neutral_signals
        if total_signals > 0:
            indicator_buy_ratio = buy_signals / total_signals
            indicator_sell_ratio = sell_signals / total_signals
        else:
            indicator_buy_ratio = indicator_sell_ratio = 0
            
        # Check for minimum number of signals to have confidence
        if total_signals < 3:
            indicator_strength = "weak"
        elif max(indicator_buy_ratio, indicator_sell_ratio) > 0.7:
            indicator_strength = "strong"
        elif max(indicator_buy_ratio, indicator_sell_ratio) > 0.5:
            indicator_strength = "moderate"
        else:
            indicator_strength = "weak"
            
        # Get predominant indicator signal
        if indicator_buy_ratio > indicator_sell_ratio and indicator_buy_ratio > 0.4:
            indicator_signal = "buy"
        elif indicator_sell_ratio > indicator_buy_ratio and indicator_sell_ratio > 0.4:
            indicator_signal = "sell"
        else:
            indicator_signal = "neutral"
        
        # Verify alignment between trend direction and technical indicators
        trend_alignment = (
            (direction == "bullish" and indicator_signal == "buy") or
            (direction == "bearish" and indicator_signal == "sell") or
            (direction == "neutral" and indicator_signal == "neutral")
        )
        
        # Analyze zone counts and best risk/reward
        has_buy_zones = len(buy_zones) > 0
        has_take_profit = len(take_profit_zones) > 0
        has_stop_loss = len(stop_loss_zones) > 0
        
        # Combine trend, indicators, wave analysis and sentiment for final decision
        if direction == "bullish" and strength in ["strong", "moderate"] and confidence >= 60 and indicator_signal == "buy":
            # Strong bullish case with high confidence and indicator alignment
            if has_buy_zones and has_take_profit and has_stop_loss:
                recommendation = "Mua mạnh" if self.language == "vi" else "Strong Buy"
            else:
                recommendation = "Mua" if self.language == "vi" else "Buy"
                
        elif direction == "bearish" and strength in ["strong", "moderate"] and confidence >= 60 and indicator_signal == "sell":
            # Strong bearish case with high confidence and indicator alignment
            recommendation = "Bán" if self.language == "vi" else "Sell"
            
        elif direction == "bullish" and indicator_signal == "buy" and trend_alignment:
            # Bullish but with moderate alignment or strength
            if has_buy_zones:
                recommendation = "Tích lũy dần" if self.language == "vi" else "Accumulate"
            else:
                recommendation = "Quan sát mua" if self.language == "vi" else "Watch for Buy"
                
        elif direction == "bearish" and indicator_signal == "sell" and trend_alignment:
            # Bearish but with moderate alignment or strength
            recommendation = "Giảm tỷ trọng" if self.language == "vi" else "Reduce"
            
        elif not trend_alignment and (indicator_strength == "strong" or (indicator_strength == "moderate" and confidence < 50)):
            # Indicators contradict trend direction with strong signals - trust the indicators
            if indicator_signal == "buy":
                recommendation = "Xem xét mua khi có xác nhận" if self.language == "vi" else "Consider Buy with Confirmation"
            elif indicator_signal == "sell":
                recommendation = "Xem xét bán khi có xác nhận" if self.language == "vi" else "Consider Sell with Confirmation"
            else:
                recommendation = "Đứng ngoài" if self.language == "vi" else "Stay Out"
                
        elif direction == "neutral" or (strength == "weak" and confidence < 50):
            # Lack of clear direction
            recommendation = "Đứng ngoài" if self.language == "vi" else "Stay Out"
            
        else:
            # Default case - wait for clearer signals
            recommendation = "Theo dõi thêm" if self.language == "vi" else "Wait for Better Setup"
        
        # Incorporate Elliott Wave analysis for additional context
        wave_signal = wave_analysis.get("signal", "")
        if wave_signal and (
            (wave_signal == "bullish" and recommendation in ["Mua", "Mua mạnh", "Tích lũy dần", "Buy", "Strong Buy", "Accumulate"]) or
            (wave_signal == "bearish" and recommendation in ["Bán", "Giảm tỷ trọng", "Sell", "Reduce"])
        ):
            # Strengthen recommendation if wave analysis confirms
            if self.language == "vi":
                recommendation += " (xác nhận bởi sóng Elliott)"
            else:
                recommendation += " (confirmed by Elliott Wave)"
                
        # Incorporate sentiment data
        sentiment_signal = sentiment_data.get("signal", "")
        if sentiment_signal and (
            (sentiment_signal == "bullish" and "mua" in recommendation.lower()) or
            (sentiment_signal == "bearish" and "bán" in recommendation.lower())
        ):
            # Add sentiment context
            if self.language == "vi":
                recommendation += " - Tâm lý thị trường đồng thuận"
            else:
                recommendation += " - Market Sentiment Aligned"
                
        return recommendation

    def format_analysis(self, analysis: Dict) -> str:
        """
        Format the analysis results into a readable string.

        Args:
            analysis: The analysis results dictionary

        Returns:
            str: Formatted analysis text
        """
        # Define section titles based on language
        titles = {
            "en": {
                "analysis": "=== ANALYSIS FOR {} ===",
                "current_price": "Current price: {:.2f}",
                "trend": "Trend: {}",
                "strength": "Trend strength: {}",
                "confidence": "Trend confidence: {:.2f}%",
                "market_condition": "Market condition: {}",
                "buy_zones": "=== BUY ZONES ===",
                "stop_loss_zones": "=== STOP LOSS ZONES ===",
                "take_profit_zones": "=== TAKE PROFIT ZONES ===",
                "risk_reward": "=== BEST RISK-REWARD SCENARIOS ===",
                "buy": "Buy: {:.2f}",
                "stop_loss": "Stop Loss: {:.2f}",
                "take_profit": "Take Profit: {:.2f}",
                "ratio": "Risk-Reward Ratio: {:.2f} ({})",
                "recommendation": "=== RECOMMENDATION ===",
                "no_buy_zones": "No favorable buy zones identified.",
                "price": "Price: {:.2f}",
                "confidence": "Confidence: {}",
                "reason": "Reason: {}"
            },
            "vi": {
                "analysis": "=== PHÂN TÍCH TÍN HIỆU CHO {} ===",
                "current_price": "Giá hiện tại: {}",
                "trend": "Xu hướng: {}",
                "strength": "Độ mạnh xu hướng: {}",
                "confidence": "Độ tin cậy xu hướng: {:.2f}%",
                "market_condition": "Tình trạng của thị trường: {}",
                "buy_zones": "=== VÙNG MUA ===",
                "stop_loss_zones": "=== VÙNG DỪNG LỖ ===",
                "take_profit_zones": "=== VÙNG CHỐT LỜI ===",
                "risk_reward": "=== KỊCH BẢN RỦI RO - LỢI NHUẬN TỐT NHẤT ===",
                "buy": "Mua: {}",
                "stop_loss": "Dừng lỗ: {}",
                "take_profit": "Chốt lời: {}",
                "ratio": "Tỷ Lệ Rủi Ro - Lợi Nhuận: {:.2f} ({})",
                "recommendation": "=== KHUYẾN NGHỊ ===",
                "no_buy_zones": "Không xác định được vùng mua thuận lợi.",
                "price": "Giá: {}",
                "confidence": "Độ tin cậy: {}",
                "reason": "Lý do: {}"
            }
        }
        
        # Get the appropriate language titles
        t = titles.get(self.language, titles["en"])
        
        # Format price in Vietnamese format (no decimal, comma separator)
        def format_vn_price(price):
            if isinstance(price, str):
                try:
                    price = float(price)
                except ValueError:
                    return price
            return f"{int(price):,}".replace(",", ".")
        
        # Translate market condition if in Vietnamese
        market_condition = analysis['market_condition']
        if self.language == "vi":
            if market_condition == "trending":
                market_condition = "xu hướng"
            elif market_condition == "ranging":
                market_condition = "tích lũy"

        result = []
        result.append(t["analysis"].format(analysis['symbol']))
        
        if self.language == "vi":
            result.append(t["current_price"].format(format_vn_price(analysis['current_price'])))
        else:
            result.append(t["current_price"].format(analysis['current_price']))
            
        result.append(t["trend"].format(analysis['trend_direction']))
        result.append(t["strength"].format(analysis['trend_strength']))
        result.append(t["confidence"].format(analysis['trend_confidence']))
        result.append(t["market_condition"].format(market_condition))
        result.append("")

        result.append(t["buy_zones"])
        if analysis["buy_zones"]:
            for i, zone in enumerate(analysis["buy_zones"], 1):
                if self.language == "vi":
                    result.append(
                        f"{i}. {t['price'].format(format_vn_price(zone['price']))} - {t['confidence'].format(zone['confidence'])} - {t['reason'].format(zone['reason'])}"
                    )
                else:
                    result.append(
                        f"{i}. {t['price'].format(zone['price'])} - {t['confidence'].format(zone['confidence'])} - {t['reason'].format(zone['reason'])}"
                    )
        else:
            result.append(t["no_buy_zones"])
        result.append("")

        result.append(t["stop_loss_zones"])
        if analysis["stop_loss_zones"]:
            for i, zone in enumerate(analysis["stop_loss_zones"], 1):
                if self.language == "vi":
                    result.append(
                        f"{i}. {t['price'].format(format_vn_price(zone['price']))} - {t['confidence'].format(zone['confidence'])} - {t['reason'].format(zone['reason'])}"
                    )
                else:
                    result.append(
                        f"{i}. {t['price'].format(zone['price'])} - {t['confidence'].format(zone['confidence'])} - {t['reason'].format(zone['reason'])}"
                    )
        else:
            result.append("No favorable stop loss zones identified.")
        result.append("")

        result.append(t["take_profit_zones"])
        if analysis["take_profit_zones"]:
            for i, zone in enumerate(analysis["take_profit_zones"], 1):
                if self.language == "vi":
                    result.append(
                        f"{i}. {t['price'].format(format_vn_price(zone['price']))} - {t['confidence'].format(zone['confidence'])} - {t['reason'].format(zone['reason'])}"
                    )
                else:
                    result.append(
                        f"{i}. {t['price'].format(zone['price'])} - {t['confidence'].format(zone['confidence'])} - {t['reason'].format(zone['reason'])}"
                    )
        else:
            result.append("No favorable take profit zones identified.")
        result.append("")

        result.append(t["risk_reward"])
        if analysis["risk_reward_ratios"]:
            for i, scenario in enumerate(
                analysis["risk_reward_ratios"][:3], 1
            ):  # Show top 3
                if self.language == "vi":
                    result.append(
                        f"{i}. {t['buy'].format(format_vn_price(scenario['buy_price']))}, {t['stop_loss'].format(format_vn_price(scenario['stop_loss_price']))}, "
                        f"{t['take_profit'].format(format_vn_price(scenario['take_profit_price']))}"
                    )
                else:
                    result.append(
                        f"{i}. {t['buy'].format(scenario['buy_price'])}, {t['stop_loss'].format(scenario['stop_loss_price'])}, "
                        f"{t['take_profit'].format(scenario['take_profit_price'])}"
                    )
                
                # Đảm bảo trường 'risk_reward_ratio' tồn tại trước khi truy cập
                ratio_value = None
                if 'risk_reward_ratio' in scenario:
                    ratio_value = float(scenario['risk_reward_ratio'])
                elif 'ratio' in scenario:
                    ratio_value = float(scenario['ratio'])
                
                if ratio_value is not None:
                    result.append(
                        f"   {t['ratio'].format(ratio_value, scenario['quality'])}"
                    )
        else:
            result.append("No favorable risk-reward scenarios identified.")
        result.append("")

        result.append(t["recommendation"])
        result.append(analysis["recommendation"])

        return "\n".join(result)

    def _detect_chart_patterns(self) -> dict:
        """Detect common chart patterns including double tops/bottoms, head and shoulders, triangles, etc."""
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        # Implement zigzag algorithm to identify swing highs and lows
        swings = self._identify_swing_points(sorted_prices, deviation=0.03)
        
        patterns = {
            "double_top": self._detect_double_top(swings),
            "double_bottom": self._detect_double_bottom(swings),
            "head_shoulders": self._detect_head_shoulders(swings),
            "inv_head_shoulders": self._detect_inv_head_shoulders(swings),
            "triangles": self._detect_triangles(sorted_prices),
            "flags": self._detect_flags(sorted_prices),
            "channels": self._detect_channels(sorted_prices)
        }
        
        return patterns

    def _analyze_volume_profile(self) -> dict:
        # Existing code...
        
        # Add value area calculations
        total_volume = sum(volume_profile)
        cumulative_volume = 0
        value_area_low_idx = None
        value_area_high_idx = None
        
        # Find the peak volume node
        peak_volume_idx = volume_profile.index(max(volume_profile))
        
        # Extend from peak to find 70% value area
        for idx in range(len(volume_profile)):
            cumulative_volume += volume_profile[idx]
            if cumulative_volume >= 0.7 * total_volume:
                value_area_high_idx = idx
                break
                
        # Calculate Point of Control and Value Area
        poc_price = min_price + (peak_volume_idx + 0.5) * bin_size
        value_area_high = min_price + (value_area_high_idx + 0.5) * bin_size
        value_area_low = min_price + (value_area_low_idx + 0.5) * bin_size
        
        return {
            # Existing code...
            "point_of_control": poc_price,
            "value_area_high": value_area_high,
            "value_area_low": value_area_low
        }

    def _check_divergence(self, indicator_type: str) -> Optional[dict]:
        """Enhanced divergence detection with more details and confidence levels"""
        # Existing code...
        
        divergence = None
        confidence = 0
        
        if indicator_type == "rsi":
            # Existing code for detecting divergence...
            
            # Add divergence strength calculation
            if divergence_type:
                price_change_pct = abs((price_highs[price_high_indices[0]] - price_highs[price_high_indices[1]]) / price_highs[price_high_indices[1]])
                rsi_change_pct = abs((rsi_values[price_high_indices[0]] - rsi_values[price_high_indices[1]]) / rsi_values[price_high_indices[1]])
                divergence_strength = rsi_change_pct / price_change_pct if price_change_pct else 0
                
                confidence = min(100, divergence_strength * 100)
                
        return {
            "type": divergence_type,
            "confidence": confidence,
            "price_points": [price_highs[price_high_indices[0]], price_highs[price_high_indices[1]]],
            "indicator_points": [rsi_values[price_high_indices[0]], rsi_values[price_high_indices[1]]]
        } if divergence_type else None
