from typing import Dict, List, Optional, Tuple, Union, Any
import numpy as np
from datetime import datetime

from src.domain.entities.stock import PriceData
from .base import BaseIndicator


class <PERSON><PERSON>moku(BaseIndicator):
    """
    Implements the Ichimoku Cloud (Ichimoku Kinko Hyo) technical indicator.
    
    The Ichimoku Cloud is a comprehensive indicator that defines support and resistance,
    identifies trend direction, gauges momentum, and provides trading signals.
    """

    def __init__(self, symbol: str, prices: List[PriceData], 
                 tenkan_period: int = 9, 
                 kijun_period: int = 26, 
                 senkou_span_b_period: int = 52,
                 displacement: int = 26):
        """
        Initialize the Ichimoku Cloud indicator.
        
        Args:
            symbol: The stock symbol
            prices: List of price data objects
            tenkan_period: Period for Tenkan-sen (Conversion Line), default is 9
            kijun_period: Period for Kijun-sen (Base Line), default is 26
            senkou_span_b_period: Period for Senkou Span B (Leading Span B), default is 52
            displacement: Displacement for Senkou Span A and B (the cloud), default is 26
        """
        super().__init__(symbol, prices, tenkan_period=tenkan_period, kijun_period=kijun_period, senkou_span_b_period=senkou_span_b_period, displacement=displacement)
        self.tenkan_period = tenkan_period
        self.kijun_period = kijun_period
        self.senkou_span_b_period = senkou_span_b_period
        self.displacement = displacement

    def calculate(self) -> Dict[str, List[float]]:
        """
        Calculate all components of the Ichimoku Cloud.
        
        Returns:
            Dict containing the following components:
                - tenkan_sen: Conversion Line (short-term trend)
                - kijun_sen: Base Line (medium-term trend)
                - senkou_span_a: Leading Span A (first component of the cloud)
                - senkou_span_b: Leading Span B (second component of the cloud)
                - chikou_span: Lagging Span (current price plotted 26 periods behind)
        """
        if not self.prices:
            return {
                "tenkan_sen": [],
                "kijun_sen": [],
                "senkou_span_a": [],
                "senkou_span_b": [],
                "chikou_span": []
            }
        
        # Extract high and low prices
        high_prices = [p.highest_price for p in self.prices]
        low_prices = [p.lowest_price for p in self.prices]
        close_prices = [p.close_price for p in self.prices]
        
        # Calculate Tenkan-sen (Conversion Line): (highest high + lowest low) / 2 for the past 9 periods
        tenkan_sen = self._calculate_midpoint(high_prices, low_prices, self.tenkan_period)
        
        # Calculate Kijun-sen (Base Line): (highest high + lowest low) / 2 for the past 26 periods
        kijun_sen = self._calculate_midpoint(high_prices, low_prices, self.kijun_period)
        
        # Calculate Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2 plotted 26 periods ahead
        senkou_span_a = []
        for i in range(len(tenkan_sen)):
            if tenkan_sen[i] is not None and kijun_sen[i] is not None:
                senkou_span_a.append((tenkan_sen[i] + kijun_sen[i]) / 2)
            else:
                senkou_span_a.append(None)
        
        # Calculate Senkou Span B (Leading Span B): (highest high + lowest low) / 2 for the past 52 periods, plotted 26 periods ahead
        senkou_span_b = self._calculate_midpoint(high_prices, low_prices, self.senkou_span_b_period)
        
        # Calculate Chikou Span (Lagging Span): Current closing price plotted 26 periods behind
        chikou_span = [None] * len(close_prices)
        for i in range(len(close_prices)):
            if i + self.displacement < len(close_prices):
                chikou_span[i + self.displacement] = close_prices[i]
        
        # Adjust cloud components by shifting forward by displacement
        senkou_span_a_displaced = [None] * self.displacement + senkou_span_a[:-self.displacement] if len(senkou_span_a) > self.displacement else []
        senkou_span_b_displaced = [None] * self.displacement + senkou_span_b[:-self.displacement] if len(senkou_span_b) > self.displacement else []
        
        # Ensure all lists have the same length
        max_len = len(self.prices)
        senkou_span_a_displaced = self._pad_list(senkou_span_a_displaced, max_len)
        senkou_span_b_displaced = self._pad_list(senkou_span_b_displaced, max_len)
        chikou_span = self._pad_list(chikou_span, max_len)
        
        return {
            "tenkan_sen": tenkan_sen,
            "kijun_sen": kijun_sen,
            "senkou_span_a": senkou_span_a_displaced,
            "senkou_span_b": senkou_span_b_displaced,
            "chikou_span": chikou_span
        }
    
    def _calculate_midpoint(self, high_prices: List[float], low_prices: List[float], period: int) -> List[Optional[float]]:
        """
        Calculate the midpoint between highest high and lowest low for the given period.
        
        Args:
            high_prices: List of high prices
            low_prices: List of low prices
            period: Period to calculate over
            
        Returns:
            List of midpoints for each point in the data
        """
        result = []
        for i in range(len(high_prices)):
            if i < period - 1:
                result.append(None)
                continue
                
            period_high = max(high_prices[i - period + 1:i + 1])
            period_low = min(low_prices[i - period + 1:i + 1])
            midpoint = (period_high + period_low) / 2
            result.append(midpoint)
            
        return result
    
    def _pad_list(self, input_list: List[Optional[float]], target_length: int) -> List[Optional[float]]:
        """
        Pad a list with None values to reach the target length.
        
        Args:
            input_list: List to pad
            target_length: Target length for the list
            
        Returns:
            Padded list with None values
        """
        if len(input_list) >= target_length:
            return input_list[:target_length]
        else:
            return input_list + [None] * (target_length - len(input_list))
    
    def get_signals(self) -> List[Dict]:
        """
        Generate Ichimoku Cloud signals.
        
        Returns:
            List of signal dictionaries with date, signal type, and strength
        """
        if not self.prices:
            return []
            
        ichimoku_values = self.calculate()
        tenkan_sen = ichimoku_values["tenkan_sen"]
        kijun_sen = ichimoku_values["kijun_sen"]
        senkou_span_a = ichimoku_values["senkou_span_a"]
        senkou_span_b = ichimoku_values["senkou_span_b"]
        chikou_span = ichimoku_values["chikou_span"]
        
        signals = []
        close_prices = [p.close_price for p in self.prices]
        
        for i in range(len(self.prices)):
            # Skip if any of the required values are None
            if (tenkan_sen[i] is None or kijun_sen[i] is None or 
                senkou_span_a[i] is None or senkou_span_b[i] is None):
                continue
                
            signal_dict = {
                "date": self.prices[i].timestamp,
                "price": close_prices[i],
                "signal": None,
                "strength": None,
                "price_above_cloud": False,
                "price_below_cloud": False,
                "price_in_cloud": False
            }
            
            # Determine if price is above, below, or in the cloud
            # Both senkou values are guaranteed to be non-None at this point due to the check above
            if close_prices[i] > max(senkou_span_a[i], senkou_span_b[i]):
                signal_dict["price_above_cloud"] = True
            elif close_prices[i] < min(senkou_span_a[i], senkou_span_b[i]):
                signal_dict["price_below_cloud"] = True
            else:
                signal_dict["price_in_cloud"] = True
                
            # Check for TK Cross (Tenkan-sen crosses Kijun-sen)
            if i > 0 and tenkan_sen[i-1] is not None and kijun_sen[i-1] is not None:
                # Bullish TK Cross: Tenkan-sen crosses above Kijun-sen
                if tenkan_sen[i-1] <= kijun_sen[i-1] and tenkan_sen[i] > kijun_sen[i]:
                    signal_dict["signal"] = "bullish_tk_cross"
                    # Strength of signal based on cloud position
                    if signal_dict["price_above_cloud"]:
                        signal_dict["strength"] = "strong"
                    elif signal_dict["price_in_cloud"]:
                        signal_dict["strength"] = "moderate"
                    else:
                        signal_dict["strength"] = "weak"
                        
                # Bearish TK Cross: Tenkan-sen crosses below Kijun-sen
                elif tenkan_sen[i-1] >= kijun_sen[i-1] and tenkan_sen[i] < kijun_sen[i]:
                    signal_dict["signal"] = "bearish_tk_cross"
                    # Strength of signal based on cloud position
                    if signal_dict["price_below_cloud"]:
                        signal_dict["strength"] = "strong"
                    elif signal_dict["price_in_cloud"]:
                        signal_dict["strength"] = "moderate"
                    else:
                        signal_dict["strength"] = "weak"
            
            # Check for Kumo Breakout (Price breaks above/below the cloud)
            if i > 0 and senkou_span_a[i-1] is not None and senkou_span_b[i-1] is not None:
                # Price breaks above the cloud (bullish)
                prev_max_cloud = max(senkou_span_a[i-1], senkou_span_b[i-1])
                if close_prices[i-1] <= prev_max_cloud and close_prices[i] > max(senkou_span_a[i], senkou_span_b[i]):
                    signal_dict["signal"] = "bullish_kumo_breakout"
                    signal_dict["strength"] = "strong"
                    
                # Price breaks below the cloud (bearish)
                prev_min_cloud = min(senkou_span_a[i-1], senkou_span_b[i-1])
                if close_prices[i-1] >= prev_min_cloud and close_prices[i] < min(senkou_span_a[i], senkou_span_b[i]):
                    signal_dict["signal"] = "bearish_kumo_breakout"
                    signal_dict["strength"] = "strong"
            
            # Check for Kumo Twist (Senkou Span A crosses Senkou Span B)
            future_idx = min(i + self.displacement, len(self.prices) - 1)
            if i > 0 and future_idx < len(senkou_span_a) and future_idx < len(senkou_span_b):
                future_span_a = senkou_span_a[future_idx]
                future_span_b = senkou_span_b[future_idx]
                
                # Make sure both previous and future values are not None before comparison
                if (senkou_span_a[i-1] is not None and senkou_span_b[i-1] is not None and
                    future_span_a is not None and future_span_b is not None):
                    
                    # Bullish Kumo Twist: Senkou Span A crosses above Senkou Span B
                    if senkou_span_a[i-1] <= senkou_span_b[i-1] and future_span_a > future_span_b:
                        signal_dict["signal"] = "bullish_kumo_twist"
                        signal_dict["strength"] = "moderate"
                        
                    # Bearish Kumo Twist: Senkou Span A crosses below Senkou Span B
                    elif senkou_span_a[i-1] >= senkou_span_b[i-1] and future_span_a < future_span_b:
                        signal_dict["signal"] = "bearish_kumo_twist"
                        signal_dict["strength"] = "moderate"
            
            if signal_dict["signal"]:
                signals.append(signal_dict)
                
        return signals 

    def predict_trend(self) -> Dict[str, Any]:
        ichimoku_values = self.calculate()
        tenkan_sen = ichimoku_values["tenkan_sen"]
        kijun_sen = ichimoku_values["kijun_sen"]
        latest_tenkan = next((v for v in reversed(tenkan_sen) if v is not None), 0.0)
        latest_kijun = next((v for v in reversed(kijun_sen) if v is not None), 0.0)
        if latest_tenkan > latest_kijun:
            return {"trend": "uptrend", "confidence": 1.0}
        elif latest_tenkan < latest_kijun:
            return {"trend": "downtrend", "confidence": 1.0}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        ichimoku_values = self.calculate()
        tenkan_sen = ichimoku_values["tenkan_sen"]
        kijun_sen = ichimoku_values["kijun_sen"]
        latest_tenkan = next((v for v in reversed(tenkan_sen) if v is not None), 0.0)
        latest_kijun = next((v for v in reversed(kijun_sen) if v is not None), 0.0)
        if latest_tenkan > latest_kijun:
            return "Buy (Tenkan above Kijun)"
        elif latest_tenkan < latest_kijun:
            return "Sell (Tenkan below Kijun)"
        else:
            return "Hold (Tenkan equals Kijun)" 