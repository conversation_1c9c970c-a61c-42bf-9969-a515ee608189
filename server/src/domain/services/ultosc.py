from typing import List, Dict, Any
from src.domain.entities.stock import PriceData
from .base import BaseIndicator

"""
Ultimate Oscillator (UO) là chỉ báo động lượng kết hợp 3 khoảng thời gian khác nhau,
gi<PERSON><PERSON> gi<PERSON>m thiểu nhiễu và cải thiện độ chính xác của tín hiệu.

Tín hiệu lực mua/bán của Ultimate Oscillator:
- Dải giá trị: 0-100
- Lực mua cao: UO > 70
  + Hành động: <PERSON><PERSON> nhắc bán hoặc thận trọng khi mua
- Lực bán cao: UO < 30
  + Hành động: Cân nhắc mua với xác suất thành công cao
- Phân kỳ tăng: Gi<PERSON> tạo đáy thấp hơn nhưng UO tạo đáy cao hơn
  + Hành động: Tín hiệu mua mạnh
"""

class UltimateOscillator(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period1: int = 7, period2: int = 14, period3: int = 28):
        super().__init__(symbol, prices, period1=period1, period2=period2, period3=period3)
        self.period1 = period1
        self.period2 = period2
        self.period3 = period3

    def calculate(self) -> List[float]:
        """
        Tính toán chỉ báo Ultimate Oscillator.
        
        Ultimate Oscillator kết hợp ba khoảng thời gian khác nhau (mặc định là 7, 14, 28 ngày)
        để cung cấp tín hiệu với độ nhiễu thấp.
        
        Returns:
            list[float]: Danh sách giá trị Ultimate Oscillator
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        bp = []  # Buying Pressure
        tr = []  # True Range
        for i in range(len(sorted_prices)):
            if i == 0:
                bp.append(0)
                tr.append(0)
            else:
                close_prev = sorted_prices[i-1].close_price
                low = sorted_prices[i].lowest_price
                high = sorted_prices[i].highest_price
                close = sorted_prices[i].close_price
                bp.append(close - min(low, close_prev))
                tr.append(max(high, close_prev) - min(low, close_prev))
        def sum_n(arr, idx, n):
            if idx - n + 1 < 0:
                return None
            return sum(arr[idx - n + 1:idx + 1])
        ultosc = []
        for i in range(len(sorted_prices)):
            avg7 = sum_n(bp, i, self.period1) / sum_n(tr, i, self.period1) if sum_n(tr, i, self.period1) else None
            avg14 = sum_n(bp, i, self.period2) / sum_n(tr, i, self.period2) if sum_n(tr, i, self.period2) else None
            avg28 = sum_n(bp, i, self.period3) / sum_n(tr, i, self.period3) if sum_n(tr, i, self.period3) else None
            if avg7 is None or avg14 is None or avg28 is None:
                ultosc.append(None)
            else:
                value = 100 * (4 * avg7 + 2 * avg14 + avg28) / 7
                ultosc.append(round(value, 2))
        return ultosc

    def get_signals(self, overbought: float = 70, oversold: float = 30) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị Ultimate Oscillator.
        
        Args:
            overbought (float): Ngưỡng quá mua (mặc định 70)
            oversold (float): Ngưỡng quá bán (mặc định 30)
            
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        ultosc_values = self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(1, len(sorted_prices)):
            if i >= len(ultosc_values) or ultosc_values[i] is None:
                continue
            
            value = ultosc_values[i]
            prev_value = ultosc_values[i-1] if i > 0 and i-1 < len(ultosc_values) and ultosc_values[i-1] is not None else None
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Xác định tín hiệu dựa trên giá trị Ultimate Oscillator
            if value > overbought:
                signal_type = "overbought"
                buying_power = "Lực mua cao, thị trường có thể quá mua"
                action = "Cân nhắc bán hoặc thận trọng khi mua mới"
                
            elif value < oversold:
                signal_type = "oversold"
                buying_power = "Lực bán cao, thị trường có thể quá bán"
                action = "Cân nhắc mua với xác suất thành công cao"
                
            # Phát hiện đảo chiều
            elif prev_value is not None:
                if prev_value < oversold and value >= oversold:
                    signal_type = "bullish_reversal"
                    buying_power = "Lực mua đang tăng từ vùng quá bán"
                    action = "Tín hiệu mua mạnh, xem xét mở vị thế mua"
                    
                elif prev_value > overbought and value <= overbought:
                    signal_type = "bearish_reversal"
                    buying_power = "Lực bán đang tăng từ vùng quá mua"
                    action = "Tín hiệu bán mạnh, xem xét mở vị thế bán"
                
                # Xác định xu hướng từ vùng trung tính
                elif 40 <= value <= 60:
                    if prev_value < 40 and value >= 40:
                        signal_type = "bullish_trend"
                        buying_power = "Lực mua đang tăng từ vùng trung tính"
                        action = "Cân nhắc mua nếu phù hợp với chiến lược"
                    elif prev_value > 60 and value <= 60:
                        signal_type = "bearish_trend"
                        buying_power = "Lực bán đang tăng từ vùng trung tính"
                        action = "Cân nhắc bán nếu phù hợp với chiến lược"
            
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "value": value,
                    "signal": signal_type,
                    "buying_power": buying_power,
                    "action": action
                })
        
        return signals 

    def predict_trend(self) -> Dict[str, Any]:
        ultosc_values = self.calculate()
        latest_ultosc = next((v for v in reversed(ultosc_values) if v is not None), 0.0)
        if latest_ultosc > 70:
            return {"trend": "overbought", "confidence": 1.0}
        elif latest_ultosc < 30:
            return {"trend": "oversold", "confidence": 1.0}
        else:
            return {"trend": "neutral", "confidence": 0.5}

    def get_recommendation(self) -> str:
        ultosc_values = self.calculate()
        latest_ultosc = next((v for v in reversed(ultosc_values) if v is not None), 0.0)
        if latest_ultosc > 70:
            return "Sell (Overbought)"
        elif latest_ultosc < 30:
            return "Buy (Oversold)"
        else:
            return "Hold (Neutral)" 