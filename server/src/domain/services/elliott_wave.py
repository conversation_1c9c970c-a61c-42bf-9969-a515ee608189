from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from datetime import datetime

from src.domain.entities.stock import PriceData


class ElliottWaveAnalyzer:
    """
    Implements Elliott Wave Pattern recognition and analysis.
    
    Elliott Wave Theory is a form of technical analysis that describes price movements in financial markets
    in terms of natural patterns of waves, specifically a 5-3 wave pattern consisting of 5 "motive" waves
    and 3 "corrective" waves.
    """

    def __init__(self, symbol: str, prices: list[PriceData], zigzag_threshold: float = 0.05):
        """
        Initialize the Elliott Wave Analyzer.
        
        Args:
            symbol: The stock symbol
            prices: List of price data objects
            zigzag_threshold: Minimum percentage change required to identify a zigzag point (default: 5%)
        """
        self.symbol = symbol
        self.prices = sorted(prices, key=lambda x: x.timestamp)
        self.zigzag_threshold = zigzag_threshold
        
        # Wave characteristics
        self.wave_definitions = {
            "1": {"description": "Initial impulse wave", "retracement": None},
            "2": {"description": "Corrective wave", "retracement": 0.618},  # Usually retraces 50-61.8% of wave 1
            "3": {"description": "Strongest impulse wave", "retracement": None},  # Typically the longest
            "4": {"description": "Corrective wave", "retracement": 0.382},  # Usually retraces 38.2% of wave 3
            "5": {"description": "Final impulse wave", "retracement": None},  # Often weaker than wave 3
            "A": {"description": "First corrective wave", "retracement": None},
            "B": {"description": "Second corrective wave", "retracement": 0.618},  # Usually retraces 50-61.8% of wave A
            "C": {"description": "Third corrective wave", "retracement": None},  # Often equal to wave A
        }

    def analyze(self) -> Dict:
        """
        Analyze price data to identify Elliott Wave patterns.
        
        Returns:
            Dictionary containing Elliott Wave analysis
        """
        if len(self.prices) < 30:  # Need enough data for meaningful analysis
            return {
                "current_wave": None,
                "confidence": 0,
                "signal": "Trung tính",
                "recommendation": "Chưa đủ dữ liệu để phân tích sóng Elliott",
                "wave_points": []
            }
        
        # Identify swing points using zigzag algorithm
        swing_points = self._identify_swing_points()
        
        # Need at least 5 swing points to identify an Elliott Wave pattern
        if len(swing_points) < 5:
            return {
                "current_wave": None,
                "confidence": 0,
                "signal": "Trung tính",
                "recommendation": "Chưa xác định được mô hình sóng Elliott",
                "wave_points": swing_points
            }
        
        # Attempt to identify wave patterns from the most recent swing points
        wave_patterns = self._identify_wave_patterns(swing_points)
        
        # Determine the current wave position
        current_wave, confidence = self._determine_current_wave(wave_patterns, swing_points)
        
        # Generate trading signals based on current wave
        signal, recommendation = self._generate_signals(current_wave, confidence, wave_patterns)
        
        return {
            "current_wave": current_wave,
            "confidence": confidence,
            "signal": signal,
            "recommendation": recommendation,
            "wave_patterns": wave_patterns,
            "wave_points": swing_points
        }
    
    def _identify_swing_points(self) -> List[Dict]:
        """
        Identify swing highs and swing lows using the ZigZag method.
        
        Returns:
            List of swing points with price, timestamp, and direction
        """
        if not self.prices:
            return []
            
        close_prices = [p.close_price for p in self.prices]
        timestamps = [p.timestamp for p in self.prices]
        
        swing_points = []
        direction = None  # None = starting, 1 = up, -1 = down
        last_extreme_idx = 0
        last_extreme_price = close_prices[0]
        
        for i in range(1, len(close_prices)):
            current_price = close_prices[i]
            
            # Calculate percentage change from last extreme
            pct_change = (current_price - last_extreme_price) / last_extreme_price
            
            # If starting, determine initial direction
            if direction is None:
                if abs(pct_change) >= self.zigzag_threshold:
                    direction = 1 if pct_change > 0 else -1
                    swing_points.append({
                        "index": 0,
                        "price": close_prices[0],
                        "timestamp": timestamps[0],
                        "direction": -direction  # Opposite of current direction
                    })
            
            # Look for reversal points
            if direction == 1:  # If trending up, look for swing high
                if current_price > last_extreme_price:
                    last_extreme_idx = i
                    last_extreme_price = current_price
                elif pct_change <= -self.zigzag_threshold:  # Reversal threshold reached
                    # Add the swing high point
                    swing_points.append({
                        "index": last_extreme_idx,
                        "price": last_extreme_price,
                        "timestamp": timestamps[last_extreme_idx],
                        "direction": direction
                    })
                    # Reset for the next swing
                    direction = -1
                    last_extreme_idx = i
                    last_extreme_price = current_price
            
            elif direction == -1:  # If trending down, look for swing low
                if current_price < last_extreme_price:
                    last_extreme_idx = i
                    last_extreme_price = current_price
                elif pct_change >= self.zigzag_threshold:  # Reversal threshold reached
                    # Add the swing low point
                    swing_points.append({
                        "index": last_extreme_idx,
                        "price": last_extreme_price,
                        "timestamp": timestamps[last_extreme_idx],
                        "direction": direction
                    })
                    # Reset for the next swing
                    direction = 1
                    last_extreme_idx = i
                    last_extreme_price = current_price
        
        # Add the last extreme point if it hasn't been added
        if swing_points and last_extreme_idx != swing_points[-1]["index"]:
            swing_points.append({
                "index": last_extreme_idx,
                "price": last_extreme_price,
                "timestamp": timestamps[last_extreme_idx],
                "direction": direction
            })
        
        return swing_points
    
    def _identify_wave_patterns(self, swing_points: List[Dict]) -> List[Dict]:
        """
        Identify potential Elliott Wave patterns from swing points.
        
        Args:
            swing_points: List of swing points
            
        Returns:
            List of potential wave patterns
        """
        if len(swing_points) < 5:
            return []
            
        # Start from the most recent potential waves
        patterns = []
        
        # Try to identify 5-wave motive patterns
        for i in range(len(swing_points) - 5, -1, -1):
            if i + 5 <= len(swing_points):
                potential_motive = swing_points[i:i+5]
                
                # Check if the alternating directions are correct for a motive wave
                if (potential_motive[0]["direction"] != potential_motive[1]["direction"] and
                    potential_motive[1]["direction"] != potential_motive[2]["direction"] and
                    potential_motive[2]["direction"] != potential_motive[3]["direction"] and
                    potential_motive[3]["direction"] != potential_motive[4]["direction"]):
                    
                    # Validate wave 2 retracement (should not exceed 100% of wave 1)
                    wave1_size = abs(potential_motive[1]["price"] - potential_motive[0]["price"])
                    wave2_size = abs(potential_motive[2]["price"] - potential_motive[1]["price"])
                    wave2_retracement = wave2_size / wave1_size if wave1_size > 0 else 0
                    
                    # Validate wave 4 retracement (should not exceed 100% of wave 3)
                    wave3_size = abs(potential_motive[3]["price"] - potential_motive[2]["price"])
                    wave4_size = abs(potential_motive[4]["price"] - potential_motive[3]["price"])
                    wave4_retracement = wave4_size / wave3_size if wave3_size > 0 else 0
                    
                    # Check rule: Wave 3 cannot be the shortest of the three motive waves
                    wave5_size = abs(swing_points[i+5]["price"] - potential_motive[4]["price"]) if i+5 < len(swing_points) else 0
                    if wave3_size >= wave1_size or wave3_size >= wave5_size:
                        # Check rule: Wave 4 should not overlap with Wave 1 territory
                        wave1_end = potential_motive[1]["price"]
                        wave4_range = (min(potential_motive[3]["price"], potential_motive[4]["price"]),
                                      max(potential_motive[3]["price"], potential_motive[4]["price"]))
                        
                        if ((potential_motive[0]["direction"] == 1 and wave4_range[1] < wave1_end) or
                           (potential_motive[0]["direction"] == -1 and wave4_range[0] > wave1_end)):
                            
                            # Calculate confidence score based on how well the pattern meets Elliott Wave rules
                            confidence = self._calculate_pattern_confidence(
                                potential_motive, wave1_size, wave2_size, wave3_size, wave4_size, wave5_size,
                                wave2_retracement, wave4_retracement)
                            
                            patterns.append({
                                "type": "motive",
                                "start_idx": i,
                                "end_idx": i + 4,
                                "points": potential_motive,
                                "confidence": confidence,
                                "wave_sizes": {
                                    "1": wave1_size,
                                    "2": wave2_size,
                                    "3": wave3_size,
                                    "4": wave4_size,
                                    "5": wave5_size if i+5 < len(swing_points) else None
                                },
                                "retracements": {
                                    "2": wave2_retracement,
                                    "4": wave4_retracement
                                }
                            })
        
        # Try to identify 3-wave corrective patterns (A-B-C)
        for i in range(len(swing_points) - 3, -1, -1):
            if i + 3 <= len(swing_points):
                potential_corrective = swing_points[i:i+3]
                
                # Check if the alternating directions are correct for a corrective wave
                if (potential_corrective[0]["direction"] != potential_corrective[1]["direction"] and
                    potential_corrective[1]["direction"] != potential_corrective[2]["direction"]):
                    
                    # Validate wave B retracement (typically 50-61.8% of wave A)
                    waveA_size = abs(potential_corrective[1]["price"] - potential_corrective[0]["price"])
                    waveB_size = abs(potential_corrective[2]["price"] - potential_corrective[1]["price"])
                    waveB_retracement = waveB_size / waveA_size if waveA_size > 0 else 0
                    
                    # Calculate wave C (if available)
                    waveC_size = abs(swing_points[i+3]["price"] - potential_corrective[2]["price"]) if i+3 < len(swing_points) else 0
                    
                    # Calculate confidence score
                    confidence = self._calculate_corrective_confidence(
                        potential_corrective, waveA_size, waveB_size, waveC_size, waveB_retracement)
                    
                    patterns.append({
                        "type": "corrective",
                        "start_idx": i,
                        "end_idx": i + 2,
                        "points": potential_corrective,
                        "confidence": confidence,
                        "wave_sizes": {
                            "A": waveA_size,
                            "B": waveB_size,
                            "C": waveC_size if i+3 < len(swing_points) else None
                        },
                        "retracements": {
                            "B": waveB_retracement
                        }
                    })
        
        # Sort patterns by confidence
        return sorted(patterns, key=lambda x: x["confidence"], reverse=True)
    
    def _calculate_pattern_confidence(self, points, wave1_size, wave2_size, wave3_size, wave4_size, wave5_size,
                                     wave2_retracement, wave4_retracement) -> float:
        """
        Calculate confidence score for a motive wave pattern.
        
        Returns:
            Confidence score from 0 to 100
        """
        confidence = 50  # Base confidence
        
        # Rule 1: Wave 3 is never the shortest impulse wave (1, 3, 5)
        if wave3_size <= wave1_size and wave3_size <= wave5_size:
            confidence -= 30
        elif wave3_size > wave1_size and wave3_size > wave5_size:
            confidence += 10  # Wave 3 is the longest (common)
        
        # Rule 2: Wave 2 never retraces more than 100% of Wave 1
        if wave2_retracement > 1.0:
            confidence -= 30
        elif 0.5 <= wave2_retracement <= 0.618:  # Ideal Fibonacci retracement
            confidence += 10
        
        # Rule 3: Wave 4 never enters the price territory of Wave 1
        if points[3]["price"] < points[1]["price"] and points[0]["direction"] == 1:
            confidence -= 20
        elif points[3]["price"] > points[1]["price"] and points[0]["direction"] == -1:
            confidence -= 20
        
        # Rule 4: Wave 4 typically retraces 38.2% of Wave 3
        if 0.368 <= wave4_retracement <= 0.4:
            confidence += 10
        
        # Rule 5: Waves 2 and 4 often alternate between simple and complex corrections
        # (Simplified - can't determine complexity easily)
        
        # Ensure confidence is between 0 and 100
        return max(0, min(100, confidence))
    
    def _calculate_corrective_confidence(self, points, waveA_size, waveB_size, waveC_size, waveB_retracement) -> float:
        """
        Calculate confidence score for a corrective wave pattern.
        
        Returns:
            Confidence score from 0 to 100
        """
        confidence = 50  # Base confidence
        
        # Rule 1: Wave B typically retraces 50-61.8% of Wave A
        if 0.5 <= waveB_retracement <= 0.618:
            confidence += 15
        elif waveB_retracement > 1.0:  # Wave B can exceed 100% (unlike Wave 2 in motive wave)
            confidence += 5  # Less common but possible
        
        # Rule 2: Wave C is often equal to or 1.618 times Wave A
        if waveC_size:
            c_to_a_ratio = waveC_size / waveA_size if waveA_size > 0 else 0
            if 0.9 <= c_to_a_ratio <= 1.1:  # Nearly equal
                confidence += 15
            elif 1.5 <= c_to_a_ratio <= 1.7:  # Near 1.618 (golden ratio)
                confidence += 20
        
        # Ensure confidence is between 0 and 100
        return max(0, min(100, confidence))
    
    def _determine_current_wave(self, patterns: List[Dict], swing_points: List[Dict]) -> Tuple[Optional[str], float]:
        """
        Determine the current Elliott Wave position based on identified patterns.
        
        Returns:
            Tuple of (current wave position, confidence level)
        """
        if not patterns or not swing_points:
            return None, 0
        
        # Get the most recent high-confidence pattern
        for pattern in patterns:
            if pattern["confidence"] >= 60:
                if pattern["type"] == "motive":
                    # Check if we're at the end of the pattern or potentially in wave 5
                    if pattern["end_idx"] >= len(swing_points) - 1:
                        return "5", pattern["confidence"]
                    elif pattern["end_idx"] == len(swing_points) - 2:
                        return "5", pattern["confidence"] * 0.8  # Less confidence if we're projecting
                    
                    # Calculate the index within the pattern
                    relative_idx = pattern["end_idx"] - pattern["start_idx"]
                    current_wave = str(relative_idx + 1)
                    return current_wave, pattern["confidence"]
                    
                elif pattern["type"] == "corrective":
                    # Check if we're at the end of the pattern or potentially in wave C
                    if pattern["end_idx"] >= len(swing_points) - 1:
                        return "C", pattern["confidence"]
                    elif pattern["end_idx"] == len(swing_points) - 2:
                        return "C", pattern["confidence"] * 0.8  # Less confidence if we're projecting
                    
                    # Calculate the index within the pattern
                    relative_idx = pattern["end_idx"] - pattern["start_idx"]
                    current_wave = ["A", "B", "C"][relative_idx]
                    return current_wave, pattern["confidence"]
        
        # If no high confidence patterns but we have some patterns
        if patterns:
            best_pattern = patterns[0]  # First pattern is highest confidence
            
            # Make a best guess based on the latest swing points
            if best_pattern["type"] == "motive":
                if len(swing_points) >= best_pattern["end_idx"] + 2:
                    # Potentially completing a 5-wave sequence
                    return "5", best_pattern["confidence"] * 0.6
                else:
                    return "3", best_pattern["confidence"] * 0.5  # Most powerful wave
            else:  # Corrective
                return "B", best_pattern["confidence"] * 0.5  # Middle of corrective
            
        return None, 0
    
    def _generate_signals(self, current_wave: Optional[str], confidence: float, patterns: List[Dict]) -> Tuple[str, str]:
        """
        Generate trading signals based on the current Elliott Wave position.
        
        Returns:
            Tuple of (signal type, recommendation text)
        """
        if not current_wave or confidence < 50:
            return "Trung tính", "Không xác định được vị trí sóng Elliott với độ tin cậy cao"
        
        # Signals based on current wave position
        if current_wave == "1":
            return "Mua", "Sóng 1 đang phát triển. Cân nhắc mở vị thế mua."
        
        elif current_wave == "2":
            return "Chờ", "Đang trong sóng điều chỉnh 2. Chờ kết thúc sóng 2 để tìm điểm mua."
        
        elif current_wave == "3":
            return "Mua", "Đang trong sóng 3 - sóng mạnh nhất. Cơ hội mua tốt, theo xu hướng."
        
        elif current_wave == "4":
            return "Chờ", "Đang trong sóng điều chỉnh 4. Có thể cân nhắc chốt lời một phần và chờ đợi sóng 5."
        
        elif current_wave == "5":
            return "Bán", "Đang trong sóng 5 - sóng cuối chu kỳ tăng. Cân nhắc chốt lời và chuẩn bị cho xu hướng đảo chiều."
        
        elif current_wave == "A":
            return "Bán", "Sóng điều chỉnh A đang bắt đầu, xu hướng giảm. Cân nhắc rút lui hoặc mở vị thế bán."
        
        elif current_wave == "B":
            return "Chờ", "Đang trong sóng B - hồi phục tạm thời. Không nên mua vào trong sóng B, đây là cơ hội thoát hàng."
        
        elif current_wave == "C":
            return "Bán", "Đang trong sóng C - sóng giảm cuối cùng. Cơ hội mở vị thế bán, hoặc chờ kết thúc chu kỳ để mua."
        
        return "Trung tính", "Không xác định được khuyến nghị rõ ràng dựa trên phân tích sóng Elliott" 