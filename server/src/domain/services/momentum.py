from typing import List, Dict, Any, Optional
from src.domain.entities.stock import PriceData
from .base import BaseIndicator

"""
Momentum là chỉ báo đo lường tốc độ thay đổi giá trong một khoảng thời gian nhất định.
Chỉ báo này giúp đánh giá động lượng và sức mạnh của xu hướng.

Tín hiệu lực mua/bán của Momentum:
- Đ<PERSON>ờng 0 là đường tham chiếu:
  + Momentum > 0: <PERSON> hư<PERSON> tăng, giá hiện tại cao hơn giá trong quá khứ
  + Momentum < 0: <PERSON> hướ<PERSON> giả<PERSON>, giá hiện tại thấp hơn giá trong quá khứ
- Đảo chiều:
  + Momentum từ dưới 0 vư<PERSON>t lên trên 0: Tín hiệu mua
  + Momentum từ trên 0 rơi xuống dưới 0: Tín hiệu bán
- <PERSON><PERSON> kỳ:
  + <PERSON><PERSON><PERSON> tạo đỉnh cao hơn nhưng Momentum tạo đỉnh thấp hơn: <PERSON><PERSON><PERSON> báo giảm
  + <PERSON><PERSON><PERSON> tạo đáy thấp hơn nhưng Momentum tạo đáy cao hơn: Cảnh báo tăng
"""


class Momentum(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 10):
        """
        Khởi tạo chỉ báo Momentum.

        Args:
            symbol: Mã chứng khoán
            prices: Danh sách dữ liệu giá
            period: Khoảng thời gian cho Momentum (mặc định là 10)
        """
        super().__init__(symbol, prices, period=period)
        self.period = period

    def calculate(self) -> List[Optional[float]]:
        """
        Tính toán chỉ báo Momentum.

        Returns:
            List[Optional[float]]: Danh sách giá trị Momentum
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        if len(sorted_prices) <= self.period:
            return [None] * len(sorted_prices)
        
        momentum_values = []
        
        # Thêm giá trị None cho các phần tử đầu tiên
        for i in range(self.period):
            momentum_values.append(None)
        
        # Tính Momentum cho các giá trị còn lại
        for i in range(self.period, len(sorted_prices)):
            current_price = sorted_prices[i].close_price
            past_price = sorted_prices[i - self.period].close_price
            
            # Tính Momentum là hiệu của giá hiện tại với giá trong quá khứ
            momentum = current_price - past_price
            momentum_values.append(momentum)
        
        return momentum_values

    def calculate_percent(self) -> List[Optional[float]]:
        """
        Tính toán chỉ báo Momentum theo phần trăm.

        Returns:
            List[Optional[float]]: Danh sách giá trị Momentum theo phần trăm
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        if len(sorted_prices) <= self.period:
            return [None] * len(sorted_prices)
        
        momentum_pct_values = []
        
        # Thêm giá trị None cho các phần tử đầu tiên
        for i in range(self.period):
            momentum_pct_values.append(None)
        
        # Tính Momentum theo phần trăm cho các giá trị còn lại
        for i in range(self.period, len(sorted_prices)):
            current_price = sorted_prices[i].close_price
            past_price = sorted_prices[i - self.period].close_price
            
            # Tính Momentum theo phần trăm
            if past_price != 0:
                momentum_pct = (current_price / past_price - 1) * 100
                momentum_pct_values.append(momentum_pct)
            else:
                momentum_pct_values.append(None)
        
        return momentum_pct_values

    def get_signals(self, use_percent: bool = False) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị Momentum.
        
        Args:
            use_percent: Sử dụng Momentum theo phần trăm thay vì giá trị tuyệt đối
            
        Returns:
            List[Dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        momentum_values = self.calculate_percent() if use_percent else self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(1, len(sorted_prices)):
            if i >= len(momentum_values) or momentum_values[i] is None or momentum_values[i-1] is None:
                continue
            
            current_momentum = momentum_values[i]
            prev_momentum = momentum_values[i-1]
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Xác định tín hiệu dựa trên đường 0
            if prev_momentum < 0 and current_momentum >= 0:
                signal_type = "zero_line_crossover"
                buying_power = "Lực mua tăng, đảo chiều từ xu hướng giảm sang tăng"
                action = "Tín hiệu mua, xem xét mở vị thế mua"
                
            elif prev_momentum > 0 and current_momentum <= 0:
                signal_type = "zero_line_crossunder"
                buying_power = "Lực bán tăng, đảo chiều từ xu hướng tăng sang giảm"
                action = "Tín hiệu bán, xem xét mở vị thế bán"
            
            # Tín hiệu dựa trên cực đại/cực tiểu
            elif i >= 3 and momentum_values[i-2] is not None:
                # Cực đại cục bộ (đỉnh)
                if prev_momentum > momentum_values[i-2] and prev_momentum > current_momentum:
                    if prev_momentum > 0:
                        signal_type = "peak_above_zero"
                        buying_power = "Lực mua đạt đỉnh, có thể giảm"
                        action = "Cân nhắc chốt lời, đặt stoploss để bảo vệ lợi nhuận"
                    else:
                        signal_type = "peak_below_zero"
                        buying_power = "Lực bán giảm, có thể đảo chiều"
                        action = "Theo dõi tín hiệu xác nhận đảo chiều tăng"
                
                # Cực tiểu cục bộ (đáy)
                elif prev_momentum < momentum_values[i-2] and prev_momentum < current_momentum:
                    if prev_momentum < 0:
                        signal_type = "trough_below_zero"
                        buying_power = "Lực bán đạt đỉnh, có thể giảm"
                        action = "Cân nhắc chốt lỗ, theo dõi tín hiệu xác nhận đảo chiều"
                    else:
                        signal_type = "trough_above_zero"
                        buying_power = "Lực mua giảm, nhưng vẫn trong xu hướng tăng"
                        action = "Thận trọng, có thể điều chỉnh giảm trong xu hướng tăng"
            
            # Xác định phân kỳ (cần ít nhất 5 giá trị)
            if i >= 10:
                # Tìm đỉnh trong momentum và giá
                price_peaks = []
                momentum_peaks = []
                
                for j in range(i-10, i):
                    if j < 2 or j >= len(momentum_values) - 2 or momentum_values[j] is None:
                        continue
                    
                    # Xác định đỉnh giá
                    if sorted_prices[j].close_price > sorted_prices[j-1].close_price and \
                       sorted_prices[j].close_price > sorted_prices[j+1].close_price:
                        price_peaks.append((j, sorted_prices[j].close_price))
                    
                    # Xác định đỉnh momentum
                    if momentum_values[j] > momentum_values[j-1] and momentum_values[j] > momentum_values[j+1]:
                        momentum_peaks.append((j, momentum_values[j]))
                
                # Phân kỳ âm: Giá tạo đỉnh cao hơn nhưng momentum tạo đỉnh thấp hơn
                if len(price_peaks) >= 2 and len(momentum_peaks) >= 2:
                    price_peaks = sorted(price_peaks, key=lambda x: x[0])
                    momentum_peaks = sorted(momentum_peaks, key=lambda x: x[0])
                    
                    if price_peaks[-1][1] > price_peaks[-2][1] and momentum_peaks[-1][1] < momentum_peaks[-2][1]:
                        signal_type = "bearish_divergence"
                        buying_power = "Lực mua suy yếu dù giá tăng, cảnh báo đảo chiều"
                        action = "Thận trọng, cân nhắc chốt lời hoặc thu hẹp vị thế"
                
                # Tìm đáy trong momentum và giá
                price_troughs = []
                momentum_troughs = []
                
                for j in range(i-10, i):
                    if j < 2 or j >= len(momentum_values) - 2 or momentum_values[j] is None:
                        continue
                    
                    # Xác định đáy giá
                    if sorted_prices[j].close_price < sorted_prices[j-1].close_price and \
                       sorted_prices[j].close_price < sorted_prices[j+1].close_price:
                        price_troughs.append((j, sorted_prices[j].close_price))
                    
                    # Xác định đáy momentum
                    if momentum_values[j] < momentum_values[j-1] and momentum_values[j] < momentum_values[j+1]:
                        momentum_troughs.append((j, momentum_values[j]))
                
                # Phân kỳ dương: Giá tạo đáy thấp hơn nhưng momentum tạo đáy cao hơn
                if len(price_troughs) >= 2 and len(momentum_troughs) >= 2:
                    price_troughs = sorted(price_troughs, key=lambda x: x[0])
                    momentum_troughs = sorted(momentum_troughs, key=lambda x: x[0])
                    
                    if price_troughs[-1][1] < price_troughs[-2][1] and momentum_troughs[-1][1] > momentum_troughs[-2][1]:
                        signal_type = "bullish_divergence"
                        buying_power = "Lực bán suy yếu dù giá giảm, cảnh báo đảo chiều"
                        action = "Theo dõi, có thể xuất hiện cơ hội mua"
            
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "momentum": round(current_momentum, 4),
                    "signal": signal_type,
                    "buying_power": buying_power,
                    "action": action
                })
        
        return signals

    def predict_trend(self) -> Dict[str, Any]:
        mom_values = self.calculate()
        latest_mom = next((v for v in reversed(mom_values) if v is not None), 0.0)
        if latest_mom > 0:
            return {"trend": "uptrend", "confidence": min(1.0, abs(latest_mom)/10)}
        elif latest_mom < 0:
            return {"trend": "downtrend", "confidence": min(1.0, abs(latest_mom)/10)}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        mom_values = self.calculate()
        latest_mom = next((v for v in reversed(mom_values) if v is not None), 0.0)
        if latest_mom > 0:
            return "Buy (Momentum Positive)"
        elif latest_mom < 0:
            return "Sell (Momentum Negative)"
        else:
            return "Hold (Momentum Neutral)" 