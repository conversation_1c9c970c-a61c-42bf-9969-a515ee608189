from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from datetime import datetime, timedelta
import os
import json
import requests
from collections import defaultdict

from src.infrastructure.config.util import Utils


class MarketSentiment:
    """
    Analyzes market sentiment for a stock from various sources including news, social media,
    and institutional activity.
    """

    def __init__(self, symbol: str, cache_dir: Optional[str] = None):
        """
        Initialize the Market Sentiment analyzer.

        Args:
            symbol: Stock symbol
            cache_dir: Optional directory for caching data
        """
        self.symbol = symbol
        self.cache_dir = cache_dir or os.path.join(
            Utils.get_cache_dir(), "sentiment", symbol
        )

        # Ensure cache directory exists
        os.makedirs(self.cache_dir, exist_ok=True)

        # Cache file for sentiment data
        self.cache_file = os.path.join(self.cache_dir, "sentiment.json")

        # Default sentiment sources
        self.sources = [
            "news",
            "social_media",
            "analyst_ratings",
            "institutional_activity",
        ]

        # Weights for different sources
        self.source_weights = {
            "news": 0.30,
            "social_media": 0.15,
            "analyst_ratings": 0.35,
            "institutional_activity": 0.20,
        }

    def get_sentiment(self, force_refresh: bool = False) -> Dict:
        """
        Get the combined sentiment analysis for the stock.

        Args:
            force_refresh: Whether to force refreshing data even if a recent cache exists

        Returns:
            Dictionary with sentiment analysis
        """
        # Try to load cached data if it's recent (less than 24 hours old)
        cached_data = self._load_cached_sentiment()

        if not force_refresh and cached_data and self._is_cache_fresh(cached_data):
            return cached_data

        # Collect sentiment data from all sources
        sentiment_data = {
            "symbol": self.symbol,
            "timestamp": datetime.now().isoformat(),
            "sources": {},
        }

        # Collect data from each source
        for source in self.sources:
            source_data = self._collect_source_data(source)
            sentiment_data["sources"][source] = source_data

        # Calculate combined sentiment score
        combined_score = self._calculate_combined_score(sentiment_data["sources"])
        sentiment_data["score"] = combined_score

        # Determine signal based on score
        sentiment_data["signal"] = self._determine_signal(combined_score)

        # Generate recommendation
        sentiment_data["recommendation"] = self._generate_recommendation(
            combined_score, sentiment_data["sources"]
        )

        # Cache the results
        self._cache_sentiment(sentiment_data)

        return sentiment_data

    def _collect_source_data(self, source: str) -> Dict:
        """
        Collect sentiment data from a specific source.

        Args:
            source: Name of the source to collect data from

        Returns:
            Dictionary with sentiment data for the source
        """
        # In a production environment, this would make API calls or scrape data
        # For simulation, we'll generate realistic-looking data

        if source == "news":
            return self._simulate_news_sentiment()
        elif source == "social_media":
            return self._simulate_social_media_sentiment()
        elif source == "analyst_ratings":
            return self._simulate_analyst_ratings()
        elif source == "institutional_activity":
            return self._simulate_institutional_activity()

        return {"score": 0, "confidence": 0, "details": []}

    def _simulate_news_sentiment(self) -> Dict:
        """Simulate news sentiment analysis."""
        # Generate a sentiment score between -1 (very negative) and 1 (very positive)
        # with a slight positive bias (most news has mild positive bias for stocks)
        base_score = np.random.normal(0.2, 0.5)
        score = max(-1.0, min(1.0, base_score))

        # Generate confidence level (higher for well-known stocks)
        confidence = np.random.uniform(0.6, 0.9)

        # Generate fake news headlines
        headlines = []
        num_headlines = np.random.randint(3, 8)

        for _ in range(num_headlines):
            # Headline sentiment varies around the main score
            headline_score = max(-1.0, min(1.0, score + np.random.normal(0, 0.3)))
            sentiment = (
                "positive"
                if headline_score > 0.2
                else "negative" if headline_score < -0.2 else "neutral"
            )

            headlines.append(
                {
                    "title": f"News about {self.symbol}",  # In a real implementation, this would be actual headlines
                    "source": np.random.choice(
                        [
                            "Bloomberg",
                            "Reuters",
                            "CNBC",
                            "Financial Times",
                            "Wall Street Journal",
                        ]
                    ),
                    "date": (
                        datetime.now() - timedelta(days=np.random.randint(0, 7))
                    ).isoformat(),
                    "sentiment": sentiment,
                    "score": headline_score,
                }
            )

        return {"score": score, "confidence": confidence, "details": headlines}

    def _simulate_social_media_sentiment(self) -> Dict:
        """Simulate social media sentiment analysis."""
        # Social media tends to be more volatile
        base_score = np.random.normal(0.0, 0.7)
        score = max(-1.0, min(1.0, base_score))

        # Usually lower confidence than news
        confidence = np.random.uniform(0.5, 0.8)

        # Generate fake social media posts
        posts = []
        num_posts = np.random.randint(5, 15)

        platforms = [
            "Twitter",
            "Reddit",
            "StockTwits",
            "Seeking Alpha",
            "Yahoo Finance",
        ]
        sentiments = [
            "very positive",
            "positive",
            "neutral",
            "negative",
            "very negative",
        ]

        for _ in range(num_posts):
            post_score = max(-1.0, min(1.0, score + np.random.normal(0, 0.5)))
            sentiment_idx = int((post_score + 1) * 2.5)  # Map -1...1 to 0...4
            sentiment_idx = max(0, min(4, sentiment_idx))

            posts.append(
                {
                    "platform": np.random.choice(platforms),
                    "date": (
                        datetime.now() - timedelta(hours=np.random.randint(0, 72))
                    ).isoformat(),
                    "sentiment": sentiments[sentiment_idx],
                    "score": post_score,
                    "engagement": np.random.randint(1, 1000),
                }
            )

        return {"score": score, "confidence": confidence, "details": posts}

    def _simulate_analyst_ratings(self) -> Dict:
        """Simulate analyst ratings."""
        # Analyst ratings tend to be positively biased
        base_score = np.random.normal(0.3, 0.4)
        score = max(-1.0, min(1.0, base_score))

        # Usually high confidence
        confidence = np.random.uniform(0.7, 0.95)

        # Generate fake analyst ratings
        ratings = []
        num_ratings = np.random.randint(3, 10)

        rating_types = ["Strong Buy", "Buy", "Hold", "Sell", "Strong Sell"]
        firms = [
            "Goldman Sachs",
            "Morgan Stanley",
            "JP Morgan",
            "Bank of America",
            "Citi",
            "Wells Fargo",
        ]

        for _ in range(num_ratings):
            rating_score = max(-1.0, min(1.0, score + np.random.normal(0, 0.2)))
            rating_idx = int((rating_score + 1) * 2)  # Map -1...1 to 0...4
            rating_idx = max(0, min(4, rating_idx))

            ratings.append(
                {
                    "firm": np.random.choice(firms),
                    "rating": rating_types[rating_idx],
                    "previous_rating": np.random.choice(rating_types),
                    "date": (
                        datetime.now() - timedelta(days=np.random.randint(0, 90))
                    ).isoformat(),
                    "price_target": np.random.randint(
                        80, 120
                    ),  # Would be based on actual stock price
                    "score": rating_score,
                }
            )

        return {"score": score, "confidence": confidence, "details": ratings}

    def _simulate_institutional_activity(self) -> Dict:
        """Simulate institutional activity such as insider trading and hedge fund positions."""
        # Institutional activity can be a strong signal
        base_score = np.random.normal(0.1, 0.6)
        score = max(-1.0, min(1.0, base_score))

        # Moderate to high confidence
        confidence = np.random.uniform(0.6, 0.9)

        # Generate fake institutional activity
        activities = []
        num_activities = np.random.randint(2, 8)

        activity_types = [
            "Insider Purchase",
            "Insider Sale",
            "Hedge Fund Increase",
            "Hedge Fund Decrease",
            "ETF Holding Change",
        ]

        for _ in range(num_activities):
            activity_type = np.random.choice(activity_types)
            amount = np.random.randint(10000, 1000000)

            if activity_type in ["Insider Sale", "Hedge Fund Decrease"]:
                activity_score = np.random.uniform(-1.0, -0.2)  # Negative sentiment
            else:
                activity_score = np.random.uniform(0.2, 1.0)  # Positive sentiment

            activities.append(
                {
                    "type": activity_type,
                    "entity": f"Entity {np.random.randint(1, 100)}",  # Would be actual entity names
                    "amount": amount,
                    "date": (
                        datetime.now() - timedelta(days=np.random.randint(0, 45))
                    ).isoformat(),
                    "score": activity_score,
                }
            )

        return {"score": score, "confidence": confidence, "details": activities}

    def _calculate_combined_score(self, source_data: Dict) -> float:
        """
        Calculate combined sentiment score from all sources.

        Args:
            source_data: Dictionary of sentiment data by source

        Returns:
            Combined sentiment score from -1 to 1
        """
        if not source_data:
            return 0.0

        weighted_sum = 0.0
        weight_sum = 0.0

        for source, data in source_data.items():
            if "score" in data and "confidence" in data:
                source_weight = self.source_weights.get(source, 0.25)
                confidence_adjusted_weight = source_weight * data["confidence"]

                weighted_sum += data["score"] * confidence_adjusted_weight
                weight_sum += confidence_adjusted_weight

        if weight_sum > 0:
            return weighted_sum / weight_sum
        else:
            return 0.0

    def _determine_signal(self, score: float) -> str:
        """
        Determine signal type based on sentiment score.

        Args:
            score: Sentiment score from -1 to 1

        Returns:
            Signal string (Mua/Bán/Trung tính)
        """
        if score >= 0.5:
            return "Mua"  # Strong positive sentiment
        elif score >= 0.2:
            return "Tích cực"  # Positive sentiment
        elif score <= -0.5:
            return "Bán"  # Strong negative sentiment
        elif score <= -0.2:
            return "Tiêu cực"  # Negative sentiment
        else:
            return "Trung tính"  # Neutral sentiment

    def _generate_recommendation(self, score: float, source_data: Dict) -> str:
        """
        Generate a recommendation based on sentiment.

        Args:
            score: Overall sentiment score
            source_data: Detailed source data

        Returns:
            Recommendation string
        """
        if score >= 0.5:
            return f"Tâm lý thị trường rất tích cực đối với {self.symbol}. Xem xét mua."
        elif score >= 0.2:
            return f"Tâm lý thị trường nghiêng về tích cực đối với {self.symbol}."
        elif score <= -0.5:
            return f"Tâm lý thị trường rất tiêu cực đối với {self.symbol}. Xem xét bán."
        elif score <= -0.2:
            return f"Tâm lý thị trường nghiêng về tiêu cực đối với {self.symbol}."
        else:
            return f"Tâm lý thị trường trung tính đối với {self.symbol}. Tiếp tục theo dõi."

    def _load_cached_sentiment(self) -> Optional[Dict]:
        """
        Load sentiment data from cache.

        Returns:
            Cached sentiment data if available, None otherwise
        """
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"Error loading cached sentiment: {e}")
            return None

    def _cache_sentiment(self, data: Dict) -> None:
        """
        Cache sentiment data.

        Args:
            data: Sentiment data to cache
        """
        try:
            with open(self.cache_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error caching sentiment data: {e}")

    def _is_cache_fresh(self, data: Dict) -> bool:
        """
        Check if cached data is fresh (less than 24 hours old).

        Args:
            data: Cached data

        Returns:
            Whether the data is fresh
        """
        if "timestamp" not in data:
            return False

        try:
            timestamp = datetime.fromisoformat(data["timestamp"])
            age = datetime.now() - timestamp
            return age < timedelta(hours=24)
        except:
            return False
