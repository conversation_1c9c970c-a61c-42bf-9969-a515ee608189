"""
Backtesting service for StockPal trading strategies.

This module provides comprehensive backtesting capabilities for validating
trading strategies and investment recommendations.
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from src.domain.entities.stock_models import (
    PricePoint, TechnicalIndicator, SignalType, ConfidenceLevel
)
from src.domain.exceptions.stock_exceptions import AnalysisException, InsufficientDataException


logger = logging.getLogger(__name__)


class OrderType(Enum):
    """Order types for backtesting."""
    BUY = "buy"
    SELL = "sell"


@dataclass
class Trade:
    """Represents a completed trade."""
    symbol: str
    entry_date: datetime
    exit_date: datetime
    entry_price: float
    exit_price: float
    quantity: int
    order_type: OrderType
    profit_loss: float
    profit_loss_percent: float
    holding_period_days: int
    entry_signal: str
    exit_signal: str


@dataclass
class Position:
    """Represents an open position."""
    symbol: str
    entry_date: datetime
    entry_price: float
    quantity: int
    order_type: OrderType
    entry_signal: str
    current_value: float = 0.0
    unrealized_pnl: float = 0.0


@dataclass
class BacktestResult:
    """Comprehensive backtest results."""
    symbol: str
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_capital: float
    total_return: float
    total_return_percent: float

    # Trade statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float

    # Performance metrics
    sharpe_ratio: float
    max_drawdown: float
    max_drawdown_percent: float
    volatility: float

    # Trade details
    trades: List[Trade]
    daily_returns: List[float]
    equity_curve: List[float]

    # Risk metrics
    var_95: float
    var_99: float
    calmar_ratio: float


class BacktestingService:
    """
    Backtesting service for trading strategy validation.

    Features:
    - Strategy backtesting with realistic trading costs
    - Performance metrics calculation
    - Risk assessment
    - Trade analysis
    - Portfolio simulation
    """

    def __init__(self, initial_capital: float = 100000.0,
                 commission_rate: float = 0.0015):
        """
        Initialize the backtesting service.

        Args:
            initial_capital: Starting capital for backtests
            commission_rate: Commission rate per trade (0.15% default)
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self._logger = logging.getLogger(__name__)

    def backtest_strategy(self, symbol: str, prices: List[PricePoint],
                         signals: List[Tuple[datetime, SignalType, float]],
                         stop_loss_percent: float = 0.05,
                         take_profit_percent: float = 0.10) -> BacktestResult:
        """
        Backtest a trading strategy.

        Args:
            symbol: Stock symbol
            prices: Historical price data
            signals: List of (date, signal_type, confidence) tuples
            stop_loss_percent: Stop loss percentage (5% default)
            take_profit_percent: Take profit percentage (10% default)

        Returns:
            Comprehensive backtest results

        Raises:
            InsufficientDataException: If not enough data for backtesting
            AnalysisException: If backtesting fails
        """
        try:
            if len(prices) < 30:
                raise InsufficientDataException(
                    f"Insufficient data for backtesting: {len(prices)} points (minimum 30 required)"
                )

            if len(signals) == 0:
                raise InsufficientDataException("No trading signals provided")

            self._logger.info(f"Starting backtest for {symbol}")

            # Initialize backtest state
            capital = self.initial_capital
            positions: List[Position] = []
            completed_trades: List[Trade] = []
            equity_curve = [capital]
            daily_returns = []

            # Create price lookup for efficient access
            price_dict = {datetime.fromtimestamp(p.timestamp): p for p in prices}
            sorted_dates = sorted(price_dict.keys())

            # Process each trading day
            for i, current_date in enumerate(sorted_dates):
                current_price_data = price_dict[current_date]
                current_price = current_price_data.close_price

                # Check for stop loss and take profit on existing positions
                positions, new_trades = self._check_exit_conditions(
                    positions, current_price_data, stop_loss_percent, take_profit_percent
                )
                completed_trades.extend(new_trades)

                # Update capital from completed trades
                for trade in new_trades:
                    capital += trade.profit_loss

                # Check for new signals
                current_signals = [s for s in signals if s[0].date() == current_date.date()]

                for signal_date, signal_type, confidence in current_signals:
                    if signal_type == SignalType.BUY and len(positions) == 0:
                        # Open new long position
                        position = self._open_position(
                            symbol, current_date, current_price, capital, OrderType.BUY,
                            f"BUY signal (confidence: {confidence})"
                        )
                        if position:
                            positions.append(position)
                            capital -= position.quantity * position.entry_price * (1 + self.commission_rate)

                    elif signal_type == SignalType.SELL and len(positions) > 0:
                        # Close existing positions
                        for position in positions:
                            trade = self._close_position(
                                position, current_date, current_price,
                                f"SELL signal (confidence: {confidence})"
                            )
                            completed_trades.append(trade)
                            capital += trade.profit_loss
                        positions = []

                # Calculate current portfolio value
                portfolio_value = capital
                for position in positions:
                    position.current_value = position.quantity * current_price
                    position.unrealized_pnl = position.current_value - (position.quantity * position.entry_price)
                    portfolio_value += position.current_value

                # Record equity curve
                equity_curve.append(portfolio_value)

                # Calculate daily return
                if i > 0:
                    daily_return = (portfolio_value - equity_curve[-2]) / equity_curve[-2]
                    daily_returns.append(daily_return)

            # Close any remaining positions at the end
            if positions:
                final_price = sorted_dates[-1]
                final_price_data = price_dict[final_price]
                for position in positions:
                    trade = self._close_position(
                        position, final_price, final_price_data.close_price, "End of backtest"
                    )
                    completed_trades.append(trade)
                    capital += trade.profit_loss

            # Calculate performance metrics
            final_capital = capital
            total_return = final_capital - self.initial_capital
            total_return_percent = (total_return / self.initial_capital) * 100

            # Trade statistics
            total_trades = len(completed_trades)
            winning_trades = len([t for t in completed_trades if t.profit_loss > 0])
            losing_trades = total_trades - winning_trades
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

            # Risk metrics
            sharpe_ratio = self._calculate_sharpe_ratio(daily_returns)
            max_drawdown, max_drawdown_percent = self._calculate_max_drawdown(equity_curve)
            volatility = np.std(daily_returns) * np.sqrt(252) if daily_returns else 0
            var_95 = np.percentile(daily_returns, 5) if daily_returns else 0
            var_99 = np.percentile(daily_returns, 1) if daily_returns else 0
            calmar_ratio = (total_return_percent / abs(max_drawdown_percent)) if max_drawdown_percent != 0 else 0

            result = BacktestResult(
                symbol=symbol,
                start_date=sorted_dates[0],
                end_date=sorted_dates[-1],
                initial_capital=self.initial_capital,
                final_capital=final_capital,
                total_return=total_return,
                total_return_percent=total_return_percent,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                max_drawdown_percent=max_drawdown_percent,
                volatility=volatility,
                trades=completed_trades,
                daily_returns=daily_returns,
                equity_curve=equity_curve,
                var_95=var_95,
                var_99=var_99,
                calmar_ratio=calmar_ratio
            )

            self._logger.info(f"Backtest completed for {symbol}: {total_return_percent:.2f}% return")
            return result

        except Exception as e:
            if isinstance(e, (InsufficientDataException, AnalysisException)):
                raise
            raise AnalysisException(f"Backtesting failed for {symbol}: {str(e)}")

    def backtest_ml_strategy(self, symbol: str, prices: List[PricePoint],
                           ml_predictions: List[Dict[str, Any]],
                           confidence_threshold: float = 0.6,
                           position_sizing: str = "fixed") -> BacktestResult:
        """
        Backtest ML-based trading strategy with advanced features.

        Args:
            symbol: Stock symbol
            prices: Historical price data
            ml_predictions: List of ML predictions with timestamps
            confidence_threshold: Minimum confidence for trades (0.6 default)
            position_sizing: Position sizing method ("fixed", "kelly", "volatility")

        Returns:
            Comprehensive backtest results for ML strategy
        """
        try:
            if len(prices) < 50:
                raise InsufficientDataException(
                    f"Insufficient data for ML backtesting: {len(prices)} points (minimum 50 required)"
                )

            if len(ml_predictions) == 0:
                raise InsufficientDataException("No ML predictions provided")

            self._logger.info(f"Starting ML strategy backtest for {symbol}")

            # Initialize backtest state
            capital = self.initial_capital
            positions: List[Position] = []
            completed_trades: List[Trade] = []
            equity_curve = [capital]
            daily_returns = []

            # Create price lookup
            price_dict = {datetime.fromtimestamp(p.timestamp): p for p in prices}
            sorted_dates = sorted(price_dict.keys())

            # Process ML predictions and generate trades
            for prediction in ml_predictions:
                pred_date = prediction.get('timestamp')
                if isinstance(pred_date, (int, float)):
                    pred_date = datetime.fromtimestamp(pred_date)
                elif isinstance(pred_date, str):
                    pred_date = datetime.fromisoformat(pred_date)

                confidence = prediction.get('confidence', 0.0)
                predicted_direction = prediction.get('trend_direction', 'neutral')

                # Skip low confidence predictions
                if confidence < confidence_threshold:
                    continue

                # Find closest price date
                closest_date = min(sorted_dates, key=lambda x: abs((x - pred_date).days))
                if abs((closest_date - pred_date).days) > 5:  # Skip if too far
                    continue

                current_price = price_dict[closest_date].close_price

                # Determine position size based on strategy
                position_size = self._calculate_position_size(
                    capital, current_price, confidence, position_sizing, prices, closest_date
                )

                # Generate trade signal
                if predicted_direction.lower() == 'bullish' and not positions:
                    # Open long position
                    shares = int(position_size / current_price)
                    if shares > 0:
                        cost = shares * current_price * (1 + self.commission_rate)
                        if cost <= capital:
                            position = Position(
                                symbol=symbol,
                                shares=shares,
                                entry_price=current_price,
                                entry_date=closest_date,
                                position_type="long",
                                stop_loss=current_price * 0.95,  # 5% stop loss
                                take_profit=current_price * 1.15  # 15% take profit
                            )
                            positions.append(position)
                            capital -= cost

                elif predicted_direction.lower() == 'bearish' and positions:
                    # Close existing positions
                    for position in positions[:]:
                        exit_price = current_price
                        proceeds = position.shares * exit_price * (1 - self.commission_rate)
                        capital += proceeds

                        # Create trade record
                        trade = Trade(
                            symbol=symbol,
                            entry_date=position.entry_date,
                            exit_date=closest_date,
                            entry_price=position.entry_price,
                            exit_price=exit_price,
                            shares=position.shares,
                            profit_loss=proceeds - (position.shares * position.entry_price),
                            return_percent=(exit_price - position.entry_price) / position.entry_price,
                            trade_type=position.position_type,
                            confidence=confidence
                        )
                        completed_trades.append(trade)
                        positions.remove(position)

            # Close any remaining positions at the end
            if positions and sorted_dates:
                final_date = sorted_dates[-1]
                final_price = price_dict[final_date].close_price

                for position in positions:
                    proceeds = position.shares * final_price * (1 - self.commission_rate)
                    capital += proceeds

                    trade = Trade(
                        symbol=symbol,
                        entry_date=position.entry_date,
                        exit_date=final_date,
                        entry_price=position.entry_price,
                        exit_price=final_price,
                        shares=position.shares,
                        profit_loss=proceeds - (position.shares * position.entry_price),
                        return_percent=(final_price - position.entry_price) / position.entry_price,
                        trade_type=position.position_type,
                        confidence=confidence_threshold
                    )
                    completed_trades.append(trade)

            # Calculate performance metrics
            final_capital = capital
            total_return = final_capital - self.initial_capital
            total_return_percent = (total_return / self.initial_capital) * 100

            # Calculate trade statistics
            total_trades = len(completed_trades)
            winning_trades = sum(1 for trade in completed_trades if trade.profit_loss > 0)
            losing_trades = total_trades - winning_trades
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

            # Calculate additional ML-specific metrics
            avg_confidence = np.mean([trade.confidence for trade in completed_trades]) if completed_trades else 0
            high_confidence_trades = sum(1 for trade in completed_trades if trade.confidence > 0.8)
            high_confidence_win_rate = 0
            if high_confidence_trades > 0:
                high_confidence_wins = sum(1 for trade in completed_trades
                                         if trade.confidence > 0.8 and trade.profit_loss > 0)
                high_confidence_win_rate = (high_confidence_wins / high_confidence_trades) * 100

            # Build result
            result = BacktestResult(
                symbol=symbol,
                start_date=sorted_dates[0] if sorted_dates else datetime.now(),
                end_date=sorted_dates[-1] if sorted_dates else datetime.now(),
                initial_capital=self.initial_capital,
                final_capital=final_capital,
                total_return=total_return,
                total_return_percent=total_return_percent,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                sharpe_ratio=0.0,  # Would need daily returns calculation
                max_drawdown=0.0,  # Would need equity curve analysis
                max_drawdown_percent=0.0,
                volatility=0.0,
                trades=completed_trades,
                daily_returns=[],
                equity_curve=equity_curve,
                var_95=0.0,
                var_99=0.0,
                calmar_ratio=0.0
            )

            # Add ML-specific metrics to result
            result.ml_metrics = {
                "confidence_threshold": confidence_threshold,
                "avg_confidence": avg_confidence,
                "high_confidence_trades": high_confidence_trades,
                "high_confidence_win_rate": high_confidence_win_rate,
                "position_sizing_method": position_sizing,
                "total_predictions_used": len([p for p in ml_predictions if p.get('confidence', 0) >= confidence_threshold])
            }

            self._logger.info(f"ML backtest completed for {symbol}: {total_return_percent:.2f}% return, {win_rate:.1f}% win rate")
            return result

        except Exception as e:
            if isinstance(e, (InsufficientDataException, AnalysisException)):
                raise
            raise AnalysisException(f"ML backtesting failed for {symbol}: {str(e)}")

    def _calculate_position_size(self, capital: float, price: float, confidence: float,
                               sizing_method: str, prices: List[PricePoint],
                               current_date: datetime) -> float:
        """Calculate position size based on sizing method."""
        if sizing_method == "fixed":
            return capital * 0.1  # 10% of capital

        elif sizing_method == "confidence":
            # Size based on confidence level
            base_size = capital * 0.05  # 5% base
            confidence_multiplier = confidence * 2  # 0.6 confidence = 1.2x, 0.8 = 1.6x
            return min(capital * 0.2, base_size * confidence_multiplier)

        elif sizing_method == "volatility":
            # Size based on recent volatility
            recent_prices = [p.close_price for p in prices[-20:] if datetime.fromtimestamp(p.timestamp) <= current_date]
            if len(recent_prices) < 10:
                return capital * 0.1

            volatility = np.std(recent_prices) / np.mean(recent_prices)
            # Lower volatility = larger position
            vol_multiplier = max(0.5, min(2.0, 1.0 / (volatility * 10)))
            return capital * 0.1 * vol_multiplier

        else:
            return capital * 0.1  # Default to 10%

    def analyze_strategy_performance(self, results: List[BacktestResult]) -> Dict[str, Any]:
        """
        Analyze performance across multiple backtest results.

        Args:
            results: List of backtest results

        Returns:
            Aggregated performance analysis
        """
        if not results:
            return {}

        # Aggregate metrics
        total_trades = sum(r.total_trades for r in results)
        total_winning_trades = sum(r.winning_trades for r in results)

        avg_return = np.mean([r.total_return_percent for r in results])
        avg_sharpe = np.mean([r.sharpe_ratio for r in results])
        avg_max_drawdown = np.mean([r.max_drawdown_percent for r in results])
        avg_win_rate = np.mean([r.win_rate for r in results])

        # Best and worst performers
        best_performer = max(results, key=lambda r: r.total_return_percent)
        worst_performer = min(results, key=lambda r: r.total_return_percent)

        # Consistency metrics
        return_std = np.std([r.total_return_percent for r in results])
        consistency_score = avg_return / return_std if return_std > 0 else 0

        return {
            "summary": {
                "total_symbols": len(results),
                "total_trades": total_trades,
                "total_winning_trades": total_winning_trades,
                "overall_win_rate": (total_winning_trades / total_trades * 100) if total_trades > 0 else 0
            },
            "performance": {
                "average_return_percent": avg_return,
                "average_sharpe_ratio": avg_sharpe,
                "average_max_drawdown_percent": avg_max_drawdown,
                "average_win_rate": avg_win_rate,
                "return_standard_deviation": return_std,
                "consistency_score": consistency_score
            },
            "best_performer": {
                "symbol": best_performer.symbol,
                "return_percent": best_performer.total_return_percent,
                "sharpe_ratio": best_performer.sharpe_ratio,
                "win_rate": best_performer.win_rate
            },
            "worst_performer": {
                "symbol": worst_performer.symbol,
                "return_percent": worst_performer.total_return_percent,
                "sharpe_ratio": worst_performer.sharpe_ratio,
                "win_rate": worst_performer.win_rate
            }
        }

    # === PRIVATE HELPER METHODS ===

    def _open_position(self, symbol: str, date: datetime, price: float,
                      available_capital: float, order_type: OrderType,
                      signal: str) -> Optional[Position]:
        """Open a new position."""
        # Calculate position size (use 90% of available capital)
        position_value = available_capital * 0.9
        quantity = int(position_value / (price * (1 + self.commission_rate)))

        if quantity <= 0:
            return None

        return Position(
            symbol=symbol,
            entry_date=date,
            entry_price=price,
            quantity=quantity,
            order_type=order_type,
            entry_signal=signal
        )

    def _close_position(self, position: Position, exit_date: datetime,
                       exit_price: float, exit_signal: str) -> Trade:
        """Close a position and create a trade record."""
        # Calculate profit/loss
        if position.order_type == OrderType.BUY:
            gross_pnl = (exit_price - position.entry_price) * position.quantity
        else:
            gross_pnl = (position.entry_price - exit_price) * position.quantity

        # Subtract commissions
        entry_commission = position.entry_price * position.quantity * self.commission_rate
        exit_commission = exit_price * position.quantity * self.commission_rate
        net_pnl = gross_pnl - entry_commission - exit_commission

        pnl_percent = (net_pnl / (position.entry_price * position.quantity)) * 100

        holding_period = (exit_date - position.entry_date).days

        return Trade(
            symbol=position.symbol,
            entry_date=position.entry_date,
            exit_date=exit_date,
            entry_price=position.entry_price,
            exit_price=exit_price,
            quantity=position.quantity,
            order_type=position.order_type,
            profit_loss=net_pnl,
            profit_loss_percent=pnl_percent,
            holding_period_days=holding_period,
            entry_signal=position.entry_signal,
            exit_signal=exit_signal
        )

    def _check_exit_conditions(self, positions: List[Position], price_data: PricePoint,
                              stop_loss_percent: float, take_profit_percent: float) -> Tuple[List[Position], List[Trade]]:
        """Check stop loss and take profit conditions."""
        remaining_positions = []
        completed_trades = []

        current_price = price_data.close_price
        current_date = datetime.fromtimestamp(price_data.timestamp)

        for position in positions:
            should_exit = False
            exit_reason = ""

            if position.order_type == OrderType.BUY:
                # Check stop loss
                if current_price <= position.entry_price * (1 - stop_loss_percent):
                    should_exit = True
                    exit_reason = f"Stop loss triggered ({stop_loss_percent*100}%)"

                # Check take profit
                elif current_price >= position.entry_price * (1 + take_profit_percent):
                    should_exit = True
                    exit_reason = f"Take profit triggered ({take_profit_percent*100}%)"

            if should_exit:
                trade = self._close_position(position, current_date, current_price, exit_reason)
                completed_trades.append(trade)
            else:
                remaining_positions.append(position)

        return remaining_positions, completed_trades

    def _calculate_sharpe_ratio(self, daily_returns: List[float],
                               risk_free_rate: float = 0.03) -> float:
        """Calculate Sharpe ratio."""
        if not daily_returns or len(daily_returns) < 2:
            return 0.0

        returns_array = np.array(daily_returns)
        excess_returns = np.mean(returns_array) * 252 - risk_free_rate
        volatility = np.std(returns_array) * np.sqrt(252)

        return excess_returns / volatility if volatility > 0 else 0.0

    def _calculate_max_drawdown(self, equity_curve: List[float]) -> Tuple[float, float]:
        """Calculate maximum drawdown."""
        if len(equity_curve) < 2:
            return 0.0, 0.0

        equity_array = np.array(equity_curve)
        running_max = np.maximum.accumulate(equity_array)
        drawdown = equity_array - running_max

        max_drawdown = np.min(drawdown)
        max_drawdown_percent = (max_drawdown / np.max(running_max)) * 100 if np.max(running_max) > 0 else 0

        return max_drawdown, max_drawdown_percent
