from typing import Dict, List, Optional, Tuple, Union, Any
import numpy as np
from datetime import datetime

from src.domain.entities.stock import PriceData
from .base import BaseIndicator


class PivotPoints(BaseIndicator):
    """
    Implements Pivot Points technical indicator.
    
    Pivot Points are a technical analysis indicator used to determine potential support and resistance levels
    based on the previous period's high, low, and closing prices.
    """

    def __init__(self, symbol: str, prices: List[PriceData], method: str = "standard"):
        """
        Initialize the Pivot Points indicator.
        
        Args:
            symbol: The stock symbol
            prices: List of price data objects
            method: Method to calculate pivot points ('standard', 'fibonacci', 'woodie', 'camarilla', 'demark')
        """
        super().__init__(symbol, prices, method=method)
        self.method = method
        self.supported_methods = ["standard", "fibonacci", "woodie", "camarilla", "demark"]
        
        if self.method not in self.supported_methods:
            raise ValueError(f"Method '{method}' not supported. Supported methods: {self.supported_methods}")

    def calculate(self) -> Dict[str, float]:
        """
        Calculate pivot points and support/resistance levels.
        
        Returns:
            Dictionary containing pivot point (PP) and support/resistance levels (S1-S3, R1-R3)
        """
        if not self.prices or len(self.prices) < 2:
            return {}
        # Use the previous day's data to calculate pivot points
        prev_price = self.prices[-2]
        high = prev_price.highest_price
        low = prev_price.lowest_price
        close = prev_price.close_price
        open_price = prev_price.open_price if hasattr(prev_price, 'open_price') else close
        # Calculate pivot points based on the selected method
        if self.method == "standard":
            return self._calculate_standard(high, low, close)
        elif self.method == "fibonacci":
            return self._calculate_fibonacci(high, low, close)
        elif self.method == "woodie":
            return self._calculate_woodie(high, low, close, open_price)
        elif self.method == "camarilla":
            return self._calculate_camarilla(high, low, close)
        elif self.method == "demark":
            return self._calculate_demark(high, low, close)
        return {}
    
    def _calculate_standard(self, high: float, low: float, close: float) -> Dict[str, float]:
        """
        Calculate standard pivot points.
        
        Args:
            high: Previous period's high
            low: Previous period's low
            close: Previous period's close
            
        Returns:
            Dictionary with pivot point and support/resistance levels
        """
        PP = (high + low + close) / 3
        R1 = (2 * PP) - low
        S1 = (2 * PP) - high
        R2 = PP + (high - low)
        S2 = PP - (high - low)
        R3 = high + 2 * (PP - low)
        S3 = low - 2 * (high - PP)
        
        return {
            "PP": PP,
            "R1": R1,
            "R2": R2,
            "R3": R3,
            "S1": S1,
            "S2": S2,
            "S3": S3
        }
    
    def _calculate_fibonacci(self, high: float, low: float, close: float) -> Dict[str, float]:
        """
        Calculate Fibonacci pivot points.
        
        Args:
            high: Previous period's high
            low: Previous period's low
            close: Previous period's close
            
        Returns:
            Dictionary with pivot point and support/resistance levels
        """
        PP = (high + low + close) / 3
        R1 = PP + 0.382 * (high - low)
        S1 = PP - 0.382 * (high - low)
        R2 = PP + 0.618 * (high - low)
        S2 = PP - 0.618 * (high - low)
        R3 = PP + 1.000 * (high - low)
        S3 = PP - 1.000 * (high - low)
        
        return {
            "PP": PP,
            "R1": R1,
            "R2": R2,
            "R3": R3,
            "S1": S1,
            "S2": S2,
            "S3": S3
        }
    
    def _calculate_woodie(self, high: float, low: float, close: float, open_price: float) -> Dict[str, float]:
        """
        Calculate Woodie's pivot points.
        
        Args:
            high: Previous period's high
            low: Previous period's low
            close: Previous period's close
            open_price: Current period's open
            
        Returns:
            Dictionary with pivot point and support/resistance levels
        """
        PP = (high + low + 2 * close) / 4
        R1 = (2 * PP) - low
        S1 = (2 * PP) - high
        R2 = PP + (high - low)
        S2 = PP - (high - low)
        
        return {
            "PP": PP,
            "R1": R1,
            "R2": R2,
            "S1": S1,
            "S2": S2
        }
    
    def _calculate_camarilla(self, high: float, low: float, close: float) -> Dict[str, float]:
        """
        Calculate Camarilla pivot points.
        
        Args:
            high: Previous period's high
            low: Previous period's low
            close: Previous period's close
            
        Returns:
            Dictionary with pivot point and support/resistance levels
        """
        PP = (high + low + close) / 3
        R1 = close + (high - low) * 1.1 / 12
        S1 = close - (high - low) * 1.1 / 12
        R2 = close + (high - low) * 1.1 / 6
        S2 = close - (high - low) * 1.1 / 6
        R3 = close + (high - low) * 1.1 / 4
        S3 = close - (high - low) * 1.1 / 4
        R4 = close + (high - low) * 1.1 / 2
        S4 = close - (high - low) * 1.1 / 2
        
        return {
            "PP": PP,
            "R1": R1,
            "R2": R2,
            "R3": R3,
            "R4": R4,
            "S1": S1,
            "S2": S2,
            "S3": S3,
            "S4": S4
        }
    
    def _calculate_demark(self, high: float, low: float, close: float) -> Dict[str, float]:
        """
        Calculate DeMark's pivot points.
        
        Args:
            high: Previous period's high
            low: Previous period's low
            close: Previous period's close
            
        Returns:
            Dictionary with pivot point and support/resistance levels
        """
        x = close if close < open else (high + low + 2 * close) / 4
        PP = x / 2 + high / 4 + low / 4
        R1 = x + (x - low)
        S1 = x - (high - x)
        
        return {
            "PP": PP,
            "R1": R1,
            "S1": S1
        }
    
    def get_signals(self) -> Dict:
        """
        Generate trading signals based on pivot points.
        
        Returns:
            Dictionary with signals and levels
        """
        if not self.prices or len(self.prices) < 2:
            return {}
            
        pivot_levels = self.calculate()
        current_price = self.prices[-1].close_price
        
        # Determine if current price is near any pivot level
        nearest_level = None
        nearest_distance = float('inf')
        nearest_type = None
        
        for level_type, level_price in pivot_levels.items():
            distance = abs(current_price - level_price)
            if distance < nearest_distance:
                nearest_distance = distance
                nearest_level = level_price
                nearest_type = level_type
        
        # Calculate percentage distance to nearest level
        percentage_distance = (nearest_distance / current_price) * 100
        
        # Determine if price is testing a support or resistance level
        is_support = current_price > nearest_level
        is_resistance = current_price < nearest_level
        
        # Determine signal strength based on proximity
        signal_strength = "weak"
        if percentage_distance < 0.5:  # Within 0.5% of a level
            signal_strength = "strong"
        elif percentage_distance < 1.0:  # Within 1% of a level
            signal_strength = "moderate"
        
        # Determine if the price has bounced off or broken through a level
        signal_type = None
        if percentage_distance < 0.5:  # Close to a level
            if is_support and current_price > self.prices[-2].close_price:
                signal_type = "bounce_off_support"
            elif is_resistance and current_price < self.prices[-2].close_price:
                signal_type = "bounce_off_resistance"
            elif is_support and current_price < self.prices[-2].close_price:
                signal_type = "break_support"
            elif is_resistance and current_price > self.prices[-2].close_price:
                signal_type = "break_resistance"
        
        return {
            "pivot_levels": pivot_levels,
            "current_price": current_price,
            "nearest_level": {
                "type": nearest_type,
                "price": nearest_level,
                "distance_percent": percentage_distance
            },
            "signal": {
                "type": signal_type,
                "strength": signal_strength
            }
        }

    def predict_trend(self) -> Dict[str, Any]:
        vals = self.calculate()
        pivot = vals.get("PP", 0.0)
        close = self.prices[-1].close_price if self.prices else 0.0
        if close > pivot:
            return {"trend": "uptrend", "confidence": 1.0}
        elif close < pivot:
            return {"trend": "downtrend", "confidence": 1.0}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        vals = self.calculate()
        pivot = vals.get("PP", 0.0)
        close = self.prices[-1].close_price if self.prices else 0.0
        if close > pivot:
            return "Buy (Above Pivot)"
        elif close < pivot:
            return "Sell (Below Pivot)"
        else:
            return "Hold (At Pivot)" 