"""
StockPal Main Application Entry Point

This module sets up dependency injection and initializes the Clean Architecture
components. It serves as the composition root for the entire application.
"""

import sys
import logging
from pathlib import Path

# Add the server directory to Python path for imports
server_dir = Path(__file__).parent.parent
sys.path.insert(0, str(server_dir))

# Clean Architecture imports
from src.infrastructure.config.logging_config import StockPalLogger
from src.domain.services.trend_predictor import TrendPredictor
from src.infrastructure.adapters.data_scraper import DataScraper
from src.application.use_cases.analyze_stock import AnalyzeStockUseCase
from src.infrastructure.persistence.sqlite_stock_repository import (
    SqliteStockRepository
)
from src.infrastructure.adapters.ssi_adapter import SSIAdapter
from src.infrastructure.adapters.vietstock_adapter import VietStockAdapter
from src.infrastructure.adapters.cafef_adapter import CafeFAdapter


class DependencyContainer:
    """
    Dependency injection container for StockPal Clean Architecture.

    This class is responsible for creating and wiring up all dependencies
    according to Clean Architecture principles.
    """

    def __init__(self):
        self._logger = StockPalLogger.get_logger(__name__)
        self._setup_dependencies()

    def _setup_dependencies(self):
        """Set up all application dependencies."""
        self._logger.info("Setting up StockPal dependencies...")

        # Initialize Clean Architecture components
        self._stock_repository = SqliteStockRepository()
        self._ssi_adapter = SSIAdapter()
        self._vietstock_adapter = VietStockAdapter()
        self._cafef_adapter = CafeFAdapter()

        # Initialize use cases (will be created on demand)
        self._analyze_stock_use_case = None

        self._logger.info("Dependencies setup complete")

    def get_trend_predictor(self, symbol: str = "DEMO", prices=None):
        """Get the trend predictor instance."""
        return TrendPredictor(symbol, prices or [])

    def get_data_scraper(self, symbol: str = "DEMO"):
        """Get the data scraper instance."""
        return DataScraper(symbol)

    def get_stock_repository(self):
        """Get the stock repository instance."""
        return self._stock_repository

    def get_analyze_stock_use_case(self, symbol: str = "DEMO", prices=None):
        """Get the analyze stock use case instance."""
        if self._analyze_stock_use_case is None:
            trend_predictor = TrendPredictor(symbol, prices or [])
            data_scraper = DataScraper(symbol)
            self._analyze_stock_use_case = AnalyzeStockUseCase(
                trend_predictor=trend_predictor,
                data_scraper=data_scraper
            )
        return self._analyze_stock_use_case

    def get_data_adapters(self):
        """Get all data adapter instances."""
        return {
            'ssi': self._ssi_adapter,
            'vietstock': self._vietstock_adapter,
            'cafef': self._cafef_adapter
        }


def create_app() -> DependencyContainer:
    """
    Create and configure the StockPal application.

    Returns:
        DependencyContainer: Configured dependency container
    """
    return DependencyContainer()


if __name__ == "__main__":
    try:
        # Initialize the application
        print("Initializing StockPal Clean Architecture...")
        app = create_app()
        print("StockPal Clean Architecture initialized successfully!")

        # Test basic functionality
        print("\nTesting basic functionality...")
        print("Clean Architecture components loaded successfully")
        print("Clean Architecture container initialized")
        print("Ready to process stock analysis requests")

        # Example usage with dummy data
        from src.domain.entities.stock import DailyPrice
        from datetime import datetime

        # Create some dummy price data for testing
        dummy_prices = [
            DailyPrice(
                symbol="VIC",
                timestamp=int(datetime.now().timestamp()),
                open_price=100.0,
                highest_price=105.0,
                lowest_price=95.0,
                close_price=102.0,
                match_volume=1000000,
                change_price=2.0,
                change_price_percent=2.0
            )
        ]

        trend_predictor = app.get_trend_predictor("VIC", dummy_prices)
        data_scraper = app.get_data_scraper("VIC")
        stock_repository = app.get_stock_repository()
        analyze_use_case = app.get_analyze_stock_use_case("VIC", dummy_prices)
        data_adapters = app.get_data_adapters()

        print(f"Trend Predictor: {trend_predictor}")
        print(f"Data Scraper: {data_scraper}")
        print(f"Stock Repository: {stock_repository}")
        print(f"Analyze Use Case: {analyze_use_case}")
        print(f"Data Adapters: {list(data_adapters.keys())}")

    except Exception as e:
        print(f"Error initializing StockPal: {e}")
        import traceback
        traceback.print_exc()
