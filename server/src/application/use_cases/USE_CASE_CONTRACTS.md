# Use Case Contracts Documentation

This document defines the input/output contracts for the two main use cases in the StockPal application.

## 1. Data Fetching Use Case

**Purpose**: Retrieve and store stock data from external providers without any analysis logic.

### Input Contracts

#### `fetch_daily_prices()`
```python
Input:
- symbol: str (required) - Valid stock symbol (e.g., "VIC", "VHM")
- days: int (default: 365) - Number of days to retrieve (1-3650)
- provider: DataProvider (default: SSI) - Data source (SSI, VIETSTOCK, CAFEF)
- force_refresh: bool (default: False) - Bypass local cache
- end_date: Optional[datetime] - End date for data range

Output:
- List[PricePoint] - Validated price data points
- Raises: DataFetchException, SymbolNotFoundException
```

#### `fetch_minute_prices()`
```python
Input:
- symbol: str (required) - Valid stock symbol
- days: int (default: 5) - Number of days to retrieve (1-30)
- provider: DataProvider (default: SSI) - Data source
- force_refresh: bool (default: False) - Bypass local cache
- end_date: Optional[datetime] - End date for data range

Output:
- List[PricePoint] - Validated minute-level price data
- Raises: DataFetchException, SymbolNotFoundException
```

#### `bulk_refresh_symbols()`
```python
Input:
- symbols: List[str] (required) - List of symbols to refresh
- provider: DataProvider (default: SSI) - Data source
- max_workers: int (default: 10) - Parallel processing limit

Output:
- dict - Operation results with success/failure counts
  {
    "success": int,
    "failed": int,
    "details": List[dict],
    "message": str
  }
```

### Data Quality Guarantees
- All returned PricePoint objects have valid timestamps
- Price values are positive numbers
- Volume data is non-negative
- Data is sorted by timestamp (oldest first)
- Duplicate timestamps are removed

---

## 2. Stock Analysis Use Case

**Purpose**: Perform technical analysis on existing stock data without any data fetching logic.

### Input Contracts

#### `analyze_stock()`
```python
Input:
- symbol: str (required) - Valid stock symbol
- price_data: List[PricePoint] (required) - Historical price data (min 20 points)
- days_back: int (default: 365) - Analysis period
- indicators: Optional[List[IndicatorConfig]] - Specific indicators to calculate
- include_zones: bool (default: True) - Calculate trading zones
- include_risk_analysis: bool (default: True) - Perform risk analysis

Output:
- StockAnalysis - Complete analysis result
  {
    "symbol": str,
    "current_price": float,
    "recommendation": str,  # "BUY", "SELL", "HOLD"
    "confidence_score": float,  # 0-100
    "trend_direction": TrendDirection,
    "technical_indicators": List[TechnicalIndicator],
    "trading_zones": List[TradingZone],
    "risk_analysis": RiskAnalysis
  }
- Raises: AnalysisException, InsufficientDataException
```

#### `calculate_technical_indicators()`
```python
Input:
- price_data: List[PricePoint] (required) - Historical price data
- indicators: List[IndicatorConfig] (required) - Indicators to calculate

Output:
- List[TechnicalIndicator] - Calculated indicators
  Each indicator contains:
  {
    "name": str,
    "value": float,
    "signal": str,  # "BUY", "SELL", "NEUTRAL"
    "confidence": float,
    "parameters": dict
  }
```

#### `identify_trading_zones()`
```python
Input:
- symbol: str (required) - Stock symbol
- price_data: List[PricePoint] (required) - Historical price data
- indicators: Optional[List[TechnicalIndicator]] - Pre-calculated indicators

Output:
- List[TradingZone] - Identified zones
  Each zone contains:
  {
    "zone_type": str,  # "BUY", "SELL", "STOP_LOSS"
    "price_level": float,
    "confidence": float,
    "reasoning": str
  }
```

#### `generate_quick_recommendation()`
```python
Input:
- symbol: str (required) - Stock symbol
- price_data: List[PricePoint] (required) - Recent price data (min 10 points)

Output:
- dict - Quick recommendation summary
  {
    "symbol": str,
    "recommendation": str,
    "confidence": float,
    "current_price": float,
    "trend": str,
    "risk_level": str,
    "summary": str
  }
```

### Analysis Quality Guarantees
- Confidence scores are between 0-100
- All price levels in trading zones are positive
- Risk-reward ratios are calculated based on identified zones
- Trend analysis uses minimum 20 data points
- Indicator calculations handle edge cases gracefully

---

## Separation of Concerns

### Data Fetching Use Case Responsibilities
✅ **DOES:**
- Fetch data from external APIs
- Validate data integrity
- Store data in local repositories
- Handle network errors and retries
- Manage data refresh operations
- Validate provider connections

❌ **DOES NOT:**
- Calculate technical indicators
- Generate trading recommendations
- Perform trend analysis
- Calculate risk metrics
- Identify trading zones

### Stock Analysis Use Case Responsibilities
✅ **DOES:**
- Calculate technical indicators
- Identify trading patterns
- Generate investment recommendations
- Perform risk-reward analysis
- Compare multiple stocks
- Trend analysis and confidence scoring

❌ **DOES NOT:**
- Fetch data from external sources
- Store data in repositories
- Handle network operations
- Manage data refresh
- Validate provider connections

---

## Error Handling Contracts

### Data Fetching Errors
- `DataFetchException`: Network issues, API errors, data validation failures
- `SymbolNotFoundException`: Invalid or non-existent stock symbols
- `NetworkException`: Connection timeouts, service unavailable

### Analysis Errors
- `AnalysisException`: Calculation errors, invalid parameters
- `InsufficientDataException`: Not enough data for reliable analysis
- `SymbolNotFoundException`: Symbol validation failures

### Error Response Format
```python
{
  "success": False,
  "error_type": str,
  "error_message": str,
  "error_details": dict,
  "timestamp": datetime
}
```

---

## Performance Contracts

### Data Fetching
- Daily prices: < 5 seconds for 1 year of data
- Minute prices: < 10 seconds for 5 days of data
- Bulk refresh: Parallel processing with configurable workers
- Cache hit ratio: > 80% for repeated requests

### Analysis
- Basic analysis: < 2 seconds for 1 year of data
- Complex indicators: < 5 seconds for comprehensive analysis
- Quick recommendations: < 1 second for recent data
- Memory usage: < 100MB per analysis session
