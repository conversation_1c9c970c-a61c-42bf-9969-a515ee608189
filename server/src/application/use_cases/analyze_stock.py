"""
Analyze Stock Use Case

Application use case for performing comprehensive stock analysis.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

# Legacy imports (to be wrapped in adapters)
import sys
from pathlib import Path
server_dir = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(server_dir))

from src.domain.services.trend_predictor import TrendPredictor
from src.infrastructure.adapters.data_scraper import DataScraper
from src.infrastructure.config.logging_config import StockPalLogger


@dataclass
class AnalysisRequest:
    """Request object for stock analysis."""
    symbol: str
    exchange: str = "HOSE"
    days_back: int = 252  # 1 year of trading days
    provider: str = "ssi"


@dataclass
class AnalysisResult:
    """Result object for stock analysis."""
    symbol: str
    analysis_date: datetime
    trend_prediction: Dict[str, Any]
    technical_indicators: Dict[str, Any]
    trading_signals: Dict[str, Any]
    risk_metrics: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None


class AnalyzeStockUseCase:
    """
    Use case for analyzing a stock and generating trading recommendations.
    
    This use case orchestrates the stock analysis process by:
    1. Fetching historical price data
    2. Calculating technical indicators
    3. Generating trend predictions
    4. Producing trading signals and risk metrics
    """
    
    def __init__(self, 
                 trend_predictor: TrendPredictor,
                 data_scraper: DataScraper):
        """
        Initialize the use case with required dependencies.
        
        Args:
            trend_predictor: Legacy trend prediction component
            data_scraper: Legacy data fetching component
        """
        self._trend_predictor = trend_predictor
        self._data_scraper = data_scraper
        self._logger = StockPalLogger.get_logger(__name__)
    
    async def execute(self, request: AnalysisRequest) -> AnalysisResult:
        """
        Execute the stock analysis use case.
        
        Args:
            request: Analysis request parameters
            
        Returns:
            AnalysisResult: Comprehensive analysis results
        """
        try:
            self._logger.info(f"Starting analysis for {request.symbol}")
            
            # Step 1: Fetch historical data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=request.days_back)
            
            price_data = self._data_scraper.get_historical_data(
                symbol=request.symbol,
                start_date=start_date,
                end_date=end_date,
                provider=request.provider
            )
            
            if not price_data:
                return AnalysisResult(
                    symbol=request.symbol,
                    analysis_date=datetime.now(),
                    trend_prediction={},
                    technical_indicators={},
                    trading_signals={},
                    risk_metrics={},
                    success=False,
                    error_message=f"No price data available for {request.symbol}"
                )
            
            # Step 2: Perform trend analysis
            trend_result = self._trend_predictor.predict_trend(
                symbol=request.symbol,
                price_data=price_data
            )
            
            # Step 3: Extract analysis components
            analysis_result = AnalysisResult(
                symbol=request.symbol,
                analysis_date=datetime.now(),
                trend_prediction=trend_result.get('trend_prediction', {}),
                technical_indicators=trend_result.get('technical_indicators', {}),
                trading_signals=trend_result.get('trading_signals', {}),
                risk_metrics=trend_result.get('risk_metrics', {}),
                success=True
            )
            
            self._logger.info(f"Analysis completed successfully for {request.symbol}")
            return analysis_result
            
        except Exception as e:
            self._logger.error(f"Analysis failed for {request.symbol}: {str(e)}")
            return AnalysisResult(
                symbol=request.symbol,
                analysis_date=datetime.now(),
                trend_prediction={},
                technical_indicators={},
                trading_signals={},
                risk_metrics={},
                success=False,
                error_message=str(e)
            )
    
    def _validate_request(self, request: AnalysisRequest) -> None:
        """
        Validate the analysis request.
        
        Args:
            request: Request to validate
            
        Raises:
            ValueError: If request is invalid
        """
        if not request.symbol:
            raise ValueError("Symbol is required")
        
        if request.days_back <= 0:
            raise ValueError("Days back must be positive")
        
        if request.days_back > 365 * 5:  # 5 years max
            raise ValueError("Days back cannot exceed 5 years")
