"""
Stock Analysis Use Case

This module provides a focused use case for performing technical analysis
on existing stock data. It handles only analysis operations without any
data fetching logic.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from src.domain.entities.stock_models import (
    PricePoint, StockAnalysis, AnalysisRequest, TechnicalIndicator,
    TradingZone, RiskRewardRatio, IndicatorConfig
)
from src.domain.exceptions.stock_exceptions import (
    AnalysisException, InsufficientDataException, SymbolNotFoundException
)
from shared.utils.validation import validate_symbol, validate_days_back
from core.services.analysis_service import AnalysisService


class StockAnalysisUseCase:
    """
    Use case for performing technical analysis on stock data.

    This use case is responsible for:
    - Calculating technical indicators
    - Identifying trading zones (buy/sell/stop-loss)
    - Performing risk-reward analysis
    - Generating investment recommendations
    - Trend analysis and confidence scoring

    Input Contract:
    - symbol: Valid stock symbol (string)
    - price_data: List of PricePoint objects
    - analysis_config: AnalysisRequest with indicator settings
    - days_back: Number of days for analysis (integer)

    Output Contract:
    - StockAnalysis: Complete analysis result with indicators, zones, and recommendations
    - Dict[str, Any]: Analysis summary with key metrics
    """

    def __init__(self, analysis_service: AnalysisService):
        """
        Initialize the stock analysis use case.

        Args:
            analysis_service: Service for analysis operations
        """
        self._analysis_service = analysis_service
        self._logger = logging.getLogger(__name__)

    def analyze_stock(
        self,
        symbol: str,
        price_data: List[PricePoint],
        days_back: int = 365,
        indicators: Optional[List[IndicatorConfig]] = None,
        include_zones: bool = True,
        include_risk_analysis: bool = True
    ) -> StockAnalysis:
        """
        Perform comprehensive technical analysis on a stock.

        Args:
            symbol: Stock symbol
            price_data: Historical price data
            days_back: Number of days to analyze
            indicators: List of indicators to calculate
            include_zones: Whether to calculate trading zones
            include_risk_analysis: Whether to perform risk analysis

        Returns:
            Complete stock analysis result

        Raises:
            AnalysisException: If analysis fails
            InsufficientDataException: If not enough data for analysis
        """
        try:
            # Validate inputs
            symbol = validate_symbol(symbol)
            days_back = validate_days_back(days_back)

            if not price_data or len(price_data) < 20:
                raise InsufficientDataException(
                    f"Insufficient data for analysis: {len(price_data)} points"
                )

            self._logger.info(
                "Starting analysis for %s with %d data points",
                symbol, len(price_data)
            )

            # Create analysis request
            request = AnalysisRequest(
                symbol=symbol,
                days_back=days_back,
                indicators=indicators,
                include_zones=include_zones,
                include_risk_analysis=include_risk_analysis
            )

            # Delegate to analysis service
            analysis = self._analysis_service.analyze_stock(request, price_data)

            self._logger.info(
                "Analysis completed for %s: recommendation=%s, confidence=%.1f%%",
                symbol, analysis.recommendation, analysis.confidence_score
            )

            return analysis

        except Exception as e:
            if isinstance(e, (AnalysisException, InsufficientDataException)):
                raise

            error_msg = f"Failed to analyze stock {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def calculate_technical_indicators(
        self,
        price_data: List[PricePoint],
        indicators: List[IndicatorConfig]
    ) -> List[TechnicalIndicator]:
        """
        Calculate specific technical indicators.

        Args:
            price_data: Historical price data
            indicators: List of indicators to calculate

        Returns:
            List of calculated technical indicators
        """
        try:
            if not price_data:
                raise InsufficientDataException("No price data provided")

            self._logger.info(
                "Calculating %d indicators for %d data points",
                len(indicators), len(price_data)
            )

            # Delegate to analysis service
            calculated_indicators = self._analysis_service.calculate_indicators(
                price_data, indicators
            )

            self._logger.info(
                "Successfully calculated %d indicators",
                len(calculated_indicators)
            )

            return calculated_indicators

        except Exception as e:
            error_msg = f"Failed to calculate indicators: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def identify_trading_zones(
        self,
        symbol: str,
        price_data: List[PricePoint],
        indicators: Optional[List[TechnicalIndicator]] = None
    ) -> List[TradingZone]:
        """
        Identify buy, sell, and stop-loss zones.

        Args:
            symbol: Stock symbol
            price_data: Historical price data
            indicators: Pre-calculated indicators (optional)

        Returns:
            List of identified trading zones
        """
        try:
            symbol = validate_symbol(symbol)

            if not price_data:
                raise InsufficientDataException("No price data provided")

            self._logger.info(
                "Identifying trading zones for %s with %d data points",
                symbol, len(price_data)
            )

            # Delegate to analysis service
            zones = self._analysis_service.identify_trading_zones(
                symbol, price_data, indicators
            )

            self._logger.info(
                "Identified %d trading zones for %s",
                len(zones), symbol
            )

            return zones

        except Exception as e:
            error_msg = f"Failed to identify trading zones for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def perform_risk_analysis(
        self,
        symbol: str,
        current_price: float,
        trading_zones: List[TradingZone]
    ) -> List[RiskRewardRatio]:
        """
        Perform risk-reward analysis.

        Args:
            symbol: Stock symbol
            current_price: Current stock price
            trading_zones: Identified trading zones

        Returns:
            List of risk-reward ratios
        """
        try:
            symbol = validate_symbol(symbol)

            if current_price <= 0:
                raise AnalysisException("Invalid current price")

            self._logger.info(
                "Performing risk analysis for %s at price %.2f",
                symbol, current_price
            )

            # Separate zones by type
            buy_zones = [z for z in trading_zones if z.zone_type == "buy"]
            stop_loss_zones = [z for z in trading_zones if z.zone_type == "stop_loss"]
            take_profit_zones = [z for z in trading_zones if z.zone_type == "take_profit"]

            # Delegate to analysis service
            risk_reward_ratios = self._analysis_service._calculate_risk_reward_ratios(
                buy_zones, stop_loss_zones, take_profit_zones
            )

            self._logger.info(
                "Risk analysis completed for %s: %d ratios calculated",
                symbol, len(risk_reward_ratios)
            )

            return risk_reward_ratios

        except Exception as e:
            error_msg = f"Failed to perform risk analysis for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def generate_quick_recommendation(
        self,
        symbol: str,
        price_data: List[PricePoint]
    ) -> Dict[str, Any]:
        """
        Generate a quick investment recommendation.

        Args:
            symbol: Stock symbol
            price_data: Recent price data

        Returns:
            Dictionary with recommendation summary
        """
        try:
            symbol = validate_symbol(symbol)

            if not price_data or len(price_data) < 10:
                raise InsufficientDataException(
                    "Insufficient data for quick recommendation"
                )

            self._logger.info(
                "Generating quick recommendation for %s",
                symbol
            )

            # Perform basic analysis
            analysis = self.analyze_stock(
                symbol=symbol,
                price_data=price_data,
                days_back=min(len(price_data), 90),
                include_zones=True,
                include_risk_analysis=True
            )

            # Extract key metrics
            recommendation = {
                "symbol": symbol,
                "recommendation": analysis.recommendation,
                "confidence": analysis.confidence_score,
                "current_price": analysis.current_price,
                "trend": analysis.trend_analysis.direction.value if analysis.trend_analysis else "unknown",
                "risk_level": "medium",  # Default risk level
                "summary": f"{analysis.recommendation.upper()} with {analysis.confidence_score:.1f}% confidence"
            }

            self._logger.info(
                "Quick recommendation for %s: %s",
                symbol, recommendation["summary"]
            )

            return recommendation

        except Exception as e:
            error_msg = f"Failed to generate recommendation for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def compare_stocks(
        self,
        stock_analyses: List[StockAnalysis]
    ) -> Dict[str, Any]:
        """
        Compare multiple stock analyses.

        Args:
            stock_analyses: List of completed stock analyses

        Returns:
            Comparison results with rankings
        """
        try:
            if not stock_analyses:
                raise AnalysisException("No stock analyses provided for comparison")

            self._logger.info(
                "Comparing %d stock analyses",
                len(stock_analyses)
            )

            # Delegate to analysis service
            comparison = self._analysis_service.compare_stocks(stock_analyses)

            self._logger.info(
                "Stock comparison completed for %d stocks",
                len(stock_analyses)
            )

            return comparison

        except Exception as e:
            error_msg = f"Failed to compare stocks: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)
