"""
Performance monitoring service for StockPal clean architecture.

This service provides comprehensive performance monitoring and metrics collection
for data fetching and analysis operations across all data providers.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json
import os

from src.domain.exceptions.stock_exceptions import PerformanceException


@dataclass
class PerformanceMetric:
    """Performance metric data structure."""
    operation: str
    provider: str
    symbol: str
    start_time: datetime
    end_time: datetime
    duration_ms: float
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceStats:
    """Aggregated performance statistics."""
    operation: str
    provider: str
    total_calls: int
    success_rate: float
    avg_duration_ms: float
    min_duration_ms: float
    max_duration_ms: float
    p95_duration_ms: float
    last_24h_calls: int
    error_count: int


class PerformanceMonitor:
    """
    Performance monitoring service for StockPal operations.
    
    Provides real-time monitoring, metrics collection, and performance analysis
    for data fetching and analysis operations across all data providers.
    """

    def __init__(self, log_dir: str = "db/logs/performance"):
        """
        Initialize performance monitor.
        
        Args:
            log_dir: Directory for performance log files
        """
        self._logger = logging.getLogger(__name__)
        self.log_dir = log_dir
        self._ensure_log_directory()
        
        # In-memory metrics storage (last 24 hours)
        self._metrics: deque = deque(maxlen=10000)
        self._operation_stats: Dict[str, Dict[str, List[float]]] = defaultdict(
            lambda: defaultdict(list)
        )
        
        # Performance thresholds (in milliseconds)
        self.thresholds = {
            "data_fetch": 5000,  # 5 seconds
            "analysis": 3000,    # 3 seconds
            "cache_operation": 100,  # 100ms
            "database_operation": 1000  # 1 second
        }

    def _ensure_log_directory(self):
        """Ensure performance log directory exists."""
        os.makedirs(self.log_dir, exist_ok=True)

    def start_operation(self, operation: str, provider: str, symbol: str, 
                       metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Start monitoring an operation.
        
        Args:
            operation: Operation type (e.g., 'data_fetch', 'analysis')
            provider: Data provider name (e.g., 'SSI', 'VietStock', 'CafeF')
            symbol: Stock symbol
            metadata: Additional metadata
            
        Returns:
            Operation ID for tracking
        """
        operation_id = f"{operation}_{provider}_{symbol}_{int(time.time() * 1000)}"
        
        metric = PerformanceMetric(
            operation=operation,
            provider=provider,
            symbol=symbol,
            start_time=datetime.now(),
            end_time=datetime.now(),  # Will be updated on completion
            duration_ms=0.0,
            success=False,
            metadata=metadata or {}
        )
        
        # Store in temporary tracking
        setattr(self, f"_tracking_{operation_id}", metric)
        
        self._logger.debug(f"Started monitoring operation: {operation_id}")
        return operation_id

    def end_operation(self, operation_id: str, success: bool = True, 
                     error_message: Optional[str] = None):
        """
        End monitoring an operation and record metrics.
        
        Args:
            operation_id: Operation ID from start_operation
            success: Whether operation was successful
            error_message: Error message if operation failed
        """
        try:
            metric = getattr(self, f"_tracking_{operation_id}", None)
            if not metric:
                self._logger.warning(f"Operation {operation_id} not found in tracking")
                return

            # Update metric
            metric.end_time = datetime.now()
            metric.duration_ms = (metric.end_time - metric.start_time).total_seconds() * 1000
            metric.success = success
            metric.error_message = error_message

            # Store metric
            self._metrics.append(metric)
            
            # Update operation stats
            key = f"{metric.operation}_{metric.provider}"
            self._operation_stats[key]["durations"].append(metric.duration_ms)
            self._operation_stats[key]["successes"].append(1 if success else 0)

            # Log performance
            self._log_performance_metric(metric)
            
            # Check thresholds
            self._check_performance_threshold(metric)

            # Clean up tracking
            delattr(self, f"_tracking_{operation_id}")
            
            self._logger.debug(f"Completed monitoring operation: {operation_id}")

        except Exception as e:
            self._logger.error(f"Error ending operation {operation_id}: {str(e)}")

    def _log_performance_metric(self, metric: PerformanceMetric):
        """Log performance metric to file."""
        try:
            log_file = os.path.join(
                self.log_dir, 
                f"performance_{metric.operation}_{datetime.now().strftime('%Y%m%d')}.log"
            )
            
            log_entry = {
                "timestamp": metric.start_time.isoformat(),
                "operation": metric.operation,
                "provider": metric.provider,
                "symbol": metric.symbol,
                "duration_ms": metric.duration_ms,
                "success": metric.success,
                "error_message": metric.error_message,
                "metadata": metric.metadata
            }
            
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry) + "\n")
                
        except Exception as e:
            self._logger.error(f"Error logging performance metric: {str(e)}")

    def _check_performance_threshold(self, metric: PerformanceMetric):
        """Check if operation exceeded performance threshold."""
        threshold = self.thresholds.get(metric.operation, float('inf'))
        
        if metric.duration_ms > threshold:
            self._logger.warning(
                f"Performance threshold exceeded: {metric.operation} "
                f"({metric.provider}/{metric.symbol}) took {metric.duration_ms:.2f}ms "
                f"(threshold: {threshold}ms)"
            )

    def get_performance_stats(self, operation: Optional[str] = None, 
                            provider: Optional[str] = None,
                            hours: int = 24) -> List[PerformanceStats]:
        """
        Get performance statistics.
        
        Args:
            operation: Filter by operation type
            provider: Filter by provider
            hours: Time window in hours
            
        Returns:
            List of performance statistics
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter metrics
        filtered_metrics = [
            m for m in self._metrics 
            if m.start_time >= cutoff_time
            and (not operation or m.operation == operation)
            and (not provider or m.provider == provider)
        ]
        
        # Group by operation and provider
        grouped = defaultdict(list)
        for metric in filtered_metrics:
            key = f"{metric.operation}_{metric.provider}"
            grouped[key].append(metric)
        
        stats = []
        for key, metrics in grouped.items():
            operation_name, provider_name = key.split("_", 1)
            
            durations = [m.duration_ms for m in metrics]
            successes = [m.success for m in metrics]
            
            if durations:
                durations.sort()
                p95_index = int(len(durations) * 0.95)
                p95_duration = durations[p95_index] if p95_index < len(durations) else durations[-1]
                
                stats.append(PerformanceStats(
                    operation=operation_name,
                    provider=provider_name,
                    total_calls=len(metrics),
                    success_rate=sum(successes) / len(successes) * 100,
                    avg_duration_ms=sum(durations) / len(durations),
                    min_duration_ms=min(durations),
                    max_duration_ms=max(durations),
                    p95_duration_ms=p95_duration,
                    last_24h_calls=len(metrics),
                    error_count=len(metrics) - sum(successes)
                ))
        
        return stats

    def get_slow_operations(self, threshold_multiplier: float = 2.0, 
                          hours: int = 24) -> List[PerformanceMetric]:
        """
        Get operations that are slower than threshold.
        
        Args:
            threshold_multiplier: Multiplier for threshold
            hours: Time window in hours
            
        Returns:
            List of slow operations
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        slow_operations = []
        for metric in self._metrics:
            if metric.start_time >= cutoff_time:
                threshold = self.thresholds.get(metric.operation, float('inf'))
                if metric.duration_ms > threshold * threshold_multiplier:
                    slow_operations.append(metric)
        
        return sorted(slow_operations, key=lambda x: x.duration_ms, reverse=True)

    def cleanup_old_metrics(self, days: int = 7):
        """
        Clean up old performance metrics and log files.
        
        Args:
            days: Number of days to keep
        """
        cutoff_time = datetime.now() - timedelta(days=days)
        
        # Clean up in-memory metrics
        self._metrics = deque(
            [m for m in self._metrics if m.start_time >= cutoff_time],
            maxlen=self._metrics.maxlen
        )
        
        # Clean up log files
        try:
            for filename in os.listdir(self.log_dir):
                if filename.startswith("performance_") and filename.endswith(".log"):
                    file_path = os.path.join(self.log_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        self._logger.info(f"Removed old performance log: {filename}")
        except Exception as e:
            self._logger.error(f"Error cleaning up old log files: {str(e)}")


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
