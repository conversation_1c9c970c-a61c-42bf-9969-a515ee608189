"""
Data service for orchestrating data fetching and storage operations.

This service provides a high-level interface for managing stock data,
coordinating between external data sources and local storage.
"""

import logging
from typing import List, Optional
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

from src.domain.entities.stock_models import PricePoint, DataFetchRequest, DataProvider
from src.domain.exceptions.stock_exceptions import (
    DataFetchException,
    DatabaseException,
    SymbolNotFoundException,
    InsufficientDataException
)
from shared.utils.validation import validate_symbol, validate_days_back, validate_provider
from infrastructure.repositories.price_repository import PriceRepositoryInterface
from infrastructure.repositories.symbol_repository import SymbolRepositoryInterface
from infrastructure.external.data_fetcher import DataFetcherInterface


logger = logging.getLogger(__name__)


class DataService:
    """Service for managing stock data operations."""

    def __init__(
        self,
        price_repository: PriceRepositoryInterface,
        symbol_repository: SymbolRepositoryInterface,
        data_fetcher: DataFetcherInterface
    ):
        """
        Initialize the data service.

        Args:
            price_repository: Repository for price data
            symbol_repository: Repository for symbol data
            data_fetcher: External data fetcher
        """
        self._price_repository = price_repository
        self._symbol_repository = symbol_repository
        self._data_fetcher = data_fetcher
        self._logger = logging.getLogger(__name__)

    def get_daily_prices(
        self,
        symbol: str,
        days: int,
        end_date: Optional[datetime] = None,
        force_refresh: bool = False
    ) -> List[PricePoint]:
        """
        Get daily price data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to retrieve
            end_date: End date for the data range
            force_refresh: Whether to force refresh from external source

        Returns:
            List of price points

        Raises:
            SymbolNotFoundException: If symbol is not found
            InsufficientDataException: If not enough data is available
            DataFetchException: If data fetching fails
        """
        try:
            symbol = validate_symbol(symbol)
            days = validate_days_back(days)

            self._logger.info(f"Getting daily prices for {symbol}, {days} days")

            # Check if symbol exists
            if not self._symbol_repository.symbol_exists(symbol):
                raise SymbolNotFoundException(f"Symbol not found: {symbol}")

            # Try to get data from local storage first (unless force refresh)
            if not force_refresh:
                try:
                    prices = self._price_repository.get_daily_prices(symbol, days, end_date)
                    if len(prices) >= min(days, 30):  # Accept if we have at least 30 days or requested amount
                        self._logger.info(f"Retrieved {len(prices)} daily prices from local storage")
                        return prices
                except (DatabaseException, SymbolNotFoundException):
                    self._logger.info("Local data not available, fetching from external source")

            # Fetch from external source
            request = DataFetchRequest(symbol=symbol, provider=DataProvider.SSI, force_refresh=force_refresh)
            external_prices = self._data_fetcher.fetch_daily_prices(request)

            if not external_prices:
                raise InsufficientDataException(f"No price data available for {symbol}")

            # Save to local storage
            try:
                self._price_repository.save_daily_prices(symbol, external_prices)
                self._logger.info(f"Saved {len(external_prices)} daily prices to local storage")
            except DatabaseException as e:
                self._logger.warning(f"Failed to save prices to local storage: {str(e)}")

            # Filter by date range if specified
            if end_date:
                end_timestamp = int(end_date.timestamp())
                external_prices = [p for p in external_prices if p.timestamp <= end_timestamp]

            # Return the most recent 'days' prices
            external_prices.sort(key=lambda x: x.timestamp, reverse=True)
            return external_prices[:days]

        except Exception as e:
            if isinstance(e, (SymbolNotFoundException, InsufficientDataException, DataFetchException)):
                raise

            error_msg = f"Failed to get daily prices for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def get_minute_prices(
        self,
        symbol: str,
        days: int,
        end_date: Optional[datetime] = None,
        force_refresh: bool = False
    ) -> List[PricePoint]:
        """
        Get minute price data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to retrieve
            end_date: End date for the data range
            force_refresh: Whether to force refresh from external source

        Returns:
            List of price points

        Raises:
            SymbolNotFoundException: If symbol is not found
            InsufficientDataException: If not enough data is available
            DataFetchException: If data fetching fails
        """
        try:
            symbol = validate_symbol(symbol)
            days = validate_days_back(days)

            self._logger.info(f"Getting minute prices for {symbol}, {days} days")

            # Check if symbol exists
            if not self._symbol_repository.symbol_exists(symbol):
                raise SymbolNotFoundException(f"Symbol not found: {symbol}")

            # Try to get data from local storage first (unless force refresh)
            if not force_refresh:
                try:
                    prices = self._price_repository.get_minute_prices(symbol, days, end_date)
                    if len(prices) >= days * 100:  # Rough estimate of minutes per day
                        self._logger.info(f"Retrieved {len(prices)} minute prices from local storage")
                        return prices
                except (DatabaseException, SymbolNotFoundException):
                    self._logger.info("Local minute data not available, fetching from external source")

            # Fetch from external source
            request = DataFetchRequest(
                symbol=symbol,
                provider=DataProvider.SSI,
                timeframe_minutes=True,
                force_refresh=force_refresh
            )
            external_prices = self._data_fetcher.fetch_minute_prices(request)

            if not external_prices:
                raise InsufficientDataException(f"No minute price data available for {symbol}")

            # Save to local storage
            try:
                self._price_repository.save_minute_prices(symbol, external_prices)
                self._logger.info(f"Saved {len(external_prices)} minute prices to local storage")
            except DatabaseException as e:
                self._logger.warning(f"Failed to save minute prices to local storage: {str(e)}")

            # Filter by date range if specified
            if end_date:
                end_timestamp = int(end_date.timestamp())
                external_prices = [p for p in external_prices if p.timestamp <= end_timestamp]

            # Return the most recent data for 'days' days
            if days > 0:
                start_timestamp = int((datetime.now() - timedelta(days=days)).timestamp())
                external_prices = [p for p in external_prices if p.timestamp >= start_timestamp]

            external_prices.sort(key=lambda x: x.timestamp, reverse=True)
            return external_prices

        except Exception as e:
            if isinstance(e, (SymbolNotFoundException, InsufficientDataException, DataFetchException)):
                raise

            error_msg = f"Failed to get minute prices for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def refresh_all_symbols(self, provider: DataProvider = DataProvider.SSI, max_workers: int = 10) -> dict:
        """
        Refresh price data for all symbols.

        Args:
            provider: Data provider to use
            max_workers: Maximum number of concurrent workers

        Returns:
            Dictionary with success/failure counts and details
        """
        try:
            provider = validate_provider(provider)

            self._logger.info(f"Starting bulk refresh for all symbols using {provider.value}")

            # Get all symbols
            symbols = self._symbol_repository.get_all_symbols()

            if not symbols:
                return {"success": 0, "failed": 0, "details": [], "message": "No symbols found"}

            # Use ThreadPoolExecutor for parallel processing
            max_workers = min(max_workers, len(symbols))
            results = {"success": 0, "failed": 0, "details": []}

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                future_to_symbol = {
                    executor.submit(self._refresh_symbol_data, symbol, provider): symbol
                    for symbol in symbols
                }

                # Process completed tasks
                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        result = future.result()
                        if result["success"]:
                            results["success"] += 1
                            self._logger.info(f"Successfully refreshed data for {symbol}")
                        else:
                            results["failed"] += 1
                            self._logger.error(f"Failed to refresh data for {symbol}: {result['error']}")

                        results["details"].append({
                            "symbol": symbol,
                            "success": result["success"],
                            "error": result.get("error"),
                            "count": result.get("count", 0)
                        })

                    except Exception as e:
                        results["failed"] += 1
                        error_msg = f"Error refreshing data for {symbol}: {str(e)}"
                        self._logger.error(error_msg)
                        results["details"].append({
                            "symbol": symbol,
                            "success": False,
                            "error": error_msg,
                            "count": 0
                        })

            results["message"] = f"Completed: {results['success']} successful, {results['failed']} failed"
            self._logger.info(results["message"])
            return results

        except Exception as e:
            error_msg = f"Failed to refresh all symbols: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def _refresh_symbol_data(self, symbol: str, provider: DataProvider) -> dict:
        """
        Refresh data for a single symbol.

        Args:
            symbol: Stock symbol
            provider: Data provider

        Returns:
            Dictionary with refresh result
        """
        try:
            request = DataFetchRequest(symbol=symbol, provider=provider, force_refresh=True)
            prices = self._data_fetcher.fetch_daily_prices(request)

            # Filter out invalid prices
            valid_prices = [p for p in prices if p.close_price > 0]

            if valid_prices:
                self._price_repository.save_daily_prices(symbol, valid_prices)
                return {"success": True, "count": len(valid_prices)}
            else:
                return {"success": False, "error": "No valid prices found", "count": 0}

        except Exception as e:
            return {"success": False, "error": str(e), "count": 0}
