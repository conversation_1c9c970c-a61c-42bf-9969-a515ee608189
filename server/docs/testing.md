# StockPal Testing Documentation

## Overview

This document provides comprehensive information about the testing infrastructure and test suites for the StockPal application.

## Test Structure

The testing framework is organized into multiple layers following the clean architecture pattern:

```
server/tests/
├── unit/                    # Unit tests for individual components
│   ├── test_entities.py     # Domain entity tests ✅
│   ├── test_validation.py   # Validation utility tests ✅
│   ├── test_commands.py     # Command handler tests (WIP)
│   └── test_services.py     # Service layer tests (WIP)
├── integration/             # Integration tests
│   ├── test_data_provider_integration.py
│   ├── test_analytics_pipeline.py
│   ├── test_cache_integration.py
│   └── test_data_pipeline.py
├── performance/             # Performance benchmarks
│   └── test_performance_benchmarks.py
├── domain/                  # Domain-specific tests
│   └── entities/
└── conftest.py             # Pytest configuration and fixtures
```

## Working Test Suites

### ✅ Unit Tests - Entities (24 tests)

Tests for domain entities and value objects:

- **PricePoint**: Tests for stock price data validation and immutability
- **TechnicalIndicator**: Tests for technical indicator data structures
- **TradingZone**: Tests for trading zone definitions
- **RiskRewardRatio**: Tests for risk-reward analysis data
- **AnalysisRequest**: Tests for analysis request validation
- **Enums**: Tests for all enumeration types (SignalType, ConfidenceLevel, etc.)

**Run with**: `python -m pytest tests/unit/test_entities.py -v`

### ✅ Unit Tests - Validation (48 tests)

Comprehensive tests for validation utilities:

- **Symbol Validation**: Vietnamese stock symbol format validation
- **Data Provider Validation**: Enum validation for data providers (SSI, VietStock, CafeF)
- **Price/Volume Validation**: Numeric validation with business rules
- **Timestamp Validation**: Unix timestamp validation with reasonable ranges
- **Percentage/Confidence Score Validation**: Range validation for percentages
- **List Validation**: Symbol list validation with duplicate detection
- **Indicator Name Validation**: Technical indicator name format validation

**Run with**: `python -m pytest tests/unit/test_validation.py -v`

## Test Configuration

### Pytest Configuration (`pytest.ini`)

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    slow: Slow running tests
    external: Tests that require external services

addopts =
    --strict-markers
    --strict-config
    --tb=short
    --durations=10
    -ra
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
```

### Test Fixtures (`conftest.py`)

Common fixtures available across all tests:

- `test_data_dir`: Temporary directory for test data
- `mock_price_data`: Sample price point data
- `mock_stock_analysis`: Sample stock analysis results
- `mock_data_service`: Mocked data service
- `mock_analysis_service`: Mocked analysis service
- `sample_symbols`: List of test stock symbols
- `test_config`: Test configuration dictionary

## Test Runners

### Simple Test Runner (`run_tests_simple.py`)

A user-friendly test runner with the following options:

```bash
# Run all working tests
python run_tests_simple.py --all

# Run specific test types
python run_tests_simple.py --entities
python run_tests_simple.py --integration
python run_tests_simple.py --performance

# Run specific test file
python run_tests_simple.py --file tests/unit/test_entities.py

# Check test environment
python run_tests_simple.py --check
```

### Enhanced Test Runner (`tests/run_tests.py`)

A comprehensive test runner with advanced features:

```bash
# Run with coverage
python tests/run_tests.py --unit --verbose

# Run integration tests only
python tests/run_tests.py --integration

# Run performance benchmarks
python tests/run_tests.py --performance

# Fast mode (stop on first failure)
python tests/run_tests.py --fast
```

## Test Results Summary

### Current Status

| Test Suite | Status | Tests | Coverage |
|------------|--------|-------|----------|
| Entity Tests | ✅ PASSING | 24/24 | 100% |
| Validation Tests | ✅ PASSING | 48/48 | 100% |
| Basic Functionality Tests | ✅ PASSING | 17/17 | 100% |
| Command Tests | ⚠️ WIP | 0/12 | 0% |
| Service Tests | ⚠️ WIP | 0/15 | 0% |
| Integration Tests | ❌ FAILING | 0/9 | 0% |
| Performance Tests | ⚠️ PARTIAL | 1/6 | 17% |

**Total Working Tests**: 89/114 (78%)

### Recent Test Run

```bash
============================= test session starts ==============================
platform linux -- Python 3.12.3, pytest-8.3.5, pluggy-1.5.0
cachedir: .pytest_cache
rootdir: /home/<USER>/Workspaces/stockpal/server
configfile: pytest.ini
collecting ... collected 89 items

tests/unit/test_entities.py::TestPricePoint::test_price_point_creation PASSED [  1%]
tests/unit/test_validation.py::TestSymbolValidation::test_validate_symbol_success PASSED [ 28%]
tests/unit/test_basic_functionality.py::TestBasicStockDataFlow::test_create_stock_analysis PASSED [ 87%]
[... 86 more tests ...]

============================== 89 passed in 0.13s ==============================
```

### Test Coverage Highlights

- **Domain Entities**: Complete coverage of all stock data models
- **Validation Logic**: Comprehensive input validation for all data types
- **Basic Workflows**: End-to-end testing of core business logic flows
- **Enum Integration**: Full testing of all enumeration types and their usage
- **Data Structure Integration**: Testing of complex object creation and relationships

## Test Coverage

### Domain Layer Coverage

- ✅ **Entities**: 100% coverage for all domain entities
- ✅ **Value Objects**: Complete validation testing
- ✅ **Enums**: All enumeration types tested
- ⚠️ **Services**: Domain services need implementation
- ❌ **Repositories**: Interface testing pending

### Application Layer Coverage

- ⚠️ **Commands**: Command handlers need clean architecture migration
- ⚠️ **Services**: Application services need dependency fixes
- ❌ **Use Cases**: Use case testing not implemented

### Infrastructure Layer Coverage

- ❌ **Adapters**: Data provider adapters need testing
- ❌ **Repositories**: Repository implementations need testing
- ❌ **External Services**: External API integration testing pending

## Known Issues

### Import Dependencies

Several test files have import issues due to the ongoing migration to clean architecture:

1. **Command Tests**: Dependency on non-existent service imports
2. **Service Tests**: Legacy import paths need updating
3. **Integration Tests**: Missing shared.models module references
4. **Performance Tests**: Fixture configuration issues

### Architecture Migration

The codebase is transitioning from legacy structure to clean architecture, causing:

- Import path mismatches
- Missing interface implementations
- Incomplete dependency injection setup

## Next Steps

### Immediate Priorities

1. **Fix Import Issues**: Update all import paths to match clean architecture
2. **Complete Command Tests**: Implement working command handler tests
3. **Service Layer Tests**: Create comprehensive service tests with proper mocking
4. **Repository Tests**: Implement repository interface and implementation tests

### Medium-term Goals

1. **Integration Testing**: Fix integration test dependencies
2. **Performance Benchmarks**: Complete performance test suite
3. **End-to-End Testing**: Implement full workflow testing
4. **Test Data Management**: Create comprehensive test data fixtures

### Long-term Objectives

1. **Continuous Integration**: Set up automated testing pipeline
2. **Test Coverage Goals**: Achieve 90%+ test coverage
3. **Property-based Testing**: Implement property-based tests for complex logic
4. **Load Testing**: Add load testing for production readiness

## Best Practices

### Writing Tests

1. **Follow AAA Pattern**: Arrange, Act, Assert
2. **Use Descriptive Names**: Test names should describe the scenario
3. **Mock External Dependencies**: Use mocks for external services
4. **Test Edge Cases**: Include boundary conditions and error scenarios
5. **Keep Tests Independent**: Each test should be able to run in isolation

### Test Organization

1. **Group Related Tests**: Use test classes to group related functionality
2. **Use Fixtures**: Leverage pytest fixtures for common setup
3. **Mark Tests Appropriately**: Use pytest markers for test categorization
4. **Document Test Intent**: Include docstrings explaining test purpose

### Performance Considerations

1. **Fast Unit Tests**: Unit tests should run in milliseconds
2. **Isolated Integration Tests**: Integration tests should not depend on external services
3. **Benchmark Critical Paths**: Performance tests for key algorithms
4. **Resource Cleanup**: Ensure tests clean up resources properly

## Conclusion

The StockPal testing infrastructure provides a solid foundation with 72 working tests covering the core domain logic and validation utilities. While there are import issues to resolve in the service and integration layers, the current test suite ensures the reliability of the fundamental business logic.

The testing framework is designed to support the clean architecture migration and will continue to evolve as the codebase stabilizes.
