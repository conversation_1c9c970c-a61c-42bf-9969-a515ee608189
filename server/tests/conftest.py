"""
Pytest configuration and fixtures for StockPal tests.

This module provides common fixtures and configuration for all tests
using the new Clean Architecture structure.
"""

import pytest
import tempfile
import shutil
import logging
from datetime import datetime, timedelta
from typing import List
from pathlib import Path
from unittest.mock import Mock

# Configure test logging
logging.basicConfig(level=logging.DEBUG)

# Try to import from new architecture, fall back to mocks if not available
try:
    from src.domain.entities.stock_models import (
        PricePoint, DataProvider, StockAnalysis, TechnicalIndicator,
        SignalType, ConfidenceLevel, MarketCondition, TrendDirection
    )
except ImportError:
    # Create mock classes if imports fail
    class PricePoint:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    class DataProvider:
        SSI = "SSI"
        VIETSTOCK = "VIETSTOCK"
        CAFEF = "CAFEF"


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def cache_service(temp_dir):
    """Create a cache service for testing."""
    # Mock cache service if not available
    try:
        from src.infrastructure.adapters.cache_service import CacheService
        return CacheService(cache_dir=temp_dir, default_ttl=3600)
    except ImportError:
        from unittest.mock import Mock
        mock_cache = Mock()
        mock_cache.get.return_value = None
        mock_cache.set.return_value = None
        return mock_cache


@pytest.fixture
def cache_manager(cache_service):
    """Create a cache manager for testing."""
    # Mock cache manager if not available
    try:
        from src.infrastructure.adapters.cache_manager import CacheManager
        return CacheManager(cache_service)
    except ImportError:
        from unittest.mock import Mock
        return Mock()


@pytest.fixture
def sample_prices() -> List[PricePoint]:
    """Generate sample price data for testing."""
    prices = []
    base_price = 100.0
    base_timestamp = int(datetime.now().timestamp()) - (100 * 24 * 3600)  # 100 days ago

    for i in range(100):
        # Simulate price movement with some randomness
        price_change = (i % 10 - 5) * 0.01  # -5% to +5% change
        current_price = base_price * (1 + price_change)

        price_point = PricePoint(
            timestamp=base_timestamp + (i * 24 * 3600),
            open_price=current_price * 0.99,
            high_price=current_price * 1.02,
            low_price=current_price * 0.98,
            close_price=current_price,
            volume=1000000 + (i * 10000),
            change_price=current_price - base_price,
            change_percent=((current_price - base_price) / base_price) * 100,
            symbol="TEST"
        )
        prices.append(price_point)
        base_price = current_price

    return prices


@pytest.fixture
def sample_volatile_prices() -> List[PricePoint]:
    """Generate volatile price data for testing."""
    prices = []
    base_price = 50.0
    base_timestamp = int(datetime.now().timestamp()) - (50 * 24 * 3600)  # 50 days ago

    for i in range(50):
        # Simulate high volatility
        price_change = (i % 6 - 3) * 0.05  # -15% to +15% change
        current_price = base_price * (1 + price_change)

        price_point = PricePoint(
            timestamp=base_timestamp + (i * 24 * 3600),
            open_price=current_price * 0.95,
            high_price=current_price * 1.08,
            low_price=current_price * 0.92,
            close_price=current_price,
            volume=500000 + (i * 5000),
            change_price=current_price - base_price,
            change_percent=((current_price - base_price) / base_price) * 100,
            symbol="VOLATILE"
        )
        prices.append(price_point)
        base_price = current_price

    return prices


@pytest.fixture
def sample_trending_up_prices() -> List[PricePoint]:
    """Generate uptrending price data for testing."""
    prices = []
    base_price = 75.0
    base_timestamp = int(datetime.now().timestamp()) - (60 * 24 * 3600)  # 60 days ago

    for i in range(60):
        # Simulate uptrend with some noise
        trend_change = 0.01  # 1% daily trend
        noise = (i % 4 - 2) * 0.005  # Small random noise
        total_change = trend_change + noise
        current_price = base_price * (1 + total_change)

        price_point = PricePoint(
            timestamp=base_timestamp + (i * 24 * 3600),
            open_price=current_price * 0.995,
            high_price=current_price * 1.01,
            low_price=current_price * 0.99,
            close_price=current_price,
            volume=800000 + (i * 8000),
            change_price=current_price - base_price,
            change_percent=((current_price - base_price) / base_price) * 100,
            symbol="UPTREND"
        )
        prices.append(price_point)
        base_price = current_price

    return prices


@pytest.fixture
def price_repository(temp_dir):
    """Create a price repository for testing."""
    try:
        from src.infrastructure.repositories.price_repository import (
            SqlitePriceRepository
        )
        # Use a temporary database file
        db_path = Path(temp_dir) / "test_stock_data.db"
        return SqlitePriceRepository(db_path=str(db_path))
    except ImportError:
        # Return mock repository
        mock_repo = Mock()
        mock_repo.get_daily_prices.return_value = []
        mock_repo.save_daily_prices.return_value = None
        return mock_repo


@pytest.fixture
def symbol_repository(temp_dir):
    """Create a symbol repository for testing."""
    try:
        from src.infrastructure.repositories.symbol_repository import (
            SqliteSymbolRepository
        )
        db_path = Path(temp_dir) / "test_stock_data.db"
        return SqliteSymbolRepository(db_path=str(db_path))
    except ImportError:
        # Return mock repository
        mock_repo = Mock()
        mock_repo.get_all_symbols.return_value = ["VIC", "HPG", "TCB"]
        mock_repo.symbol_exists.return_value = True
        return mock_repo


@pytest.fixture
def data_fetcher():
    """Create a data fetcher for testing."""
    try:
        from src.infrastructure.adapters.external_data_fetcher import (
            ExternalDataFetcher
        )
        return ExternalDataFetcher()
    except ImportError:
        # Return mock data fetcher
        mock_fetcher = Mock()
        mock_fetcher.fetch_daily_prices.return_value = []
        return mock_fetcher


@pytest.fixture
def indicator_processor():
    """Create an indicator processor for testing."""
    try:
        from src.domain.services.technical_indicators import (
            TechnicalIndicatorService
        )
        return TechnicalIndicatorService()
    except ImportError:
        # Return mock processor
        mock_processor = Mock()
        mock_processor.calculate_all_indicators.return_value = []
        return mock_processor


@pytest.fixture
def data_service(price_repository, symbol_repository, data_fetcher):
    """Create a data service for testing."""
    try:
        from src.application.services.data_service import DataService
        return DataService(
            price_repository=price_repository,
            symbol_repository=symbol_repository,
            data_fetcher=data_fetcher
        )
    except ImportError:
        # Return mock service
        mock_service = Mock()
        mock_service.get_daily_prices.return_value = []
        return mock_service


@pytest.fixture
def analysis_service(data_service, indicator_processor):
    """Create an analysis service for testing."""
    try:
        from src.application.services.analysis_service import AnalysisService
        return AnalysisService(
            data_service=data_service,
            indicator_processor=indicator_processor
        )
    except ImportError:
        # Return mock service
        mock_service = Mock()
        mock_service.analyze_stock.return_value = Mock()
        return mock_service


@pytest.fixture
def ml_analytics_service():
    """Create an ML analytics service for testing."""
    try:
        from src.domain.services.ml_analytics_service import MLAnalyticsService
        return MLAnalyticsService()
    except ImportError:
        # Return mock service
        mock_service = Mock()
        mock_service.predict_trend.return_value = "UP"
        return mock_service


@pytest.fixture
def backtesting_service():
    """Create a backtesting service for testing."""
    try:
        from src.domain.services.backtesting_service import BacktestingService
        return BacktestingService(
            initial_capital=100000.0, commission_rate=0.0015
        )
    except ImportError:
        # Return mock service
        mock_service = Mock()
        mock_service.run_backtest.return_value = {"total_return": 0.15}
        return mock_service


@pytest.fixture
def test_symbols():
    """Provide test symbols for testing."""
    return ["VIC", "VNM", "HPG", "TCB", "FPT"]


@pytest.fixture
def test_providers():
    """Provide test data providers."""
    return [DataProvider.SSI, DataProvider.VIETSTOCK, DataProvider.CAFEF]


# Test data validation helpers
def validate_price_point(price_point: PricePoint) -> bool:
    """Validate a price point for testing."""
    return (
        price_point.timestamp > 0 and
        price_point.open_price > 0 and
        price_point.high_price >= price_point.open_price and
        price_point.low_price <= price_point.open_price and
        price_point.close_price > 0 and
        price_point.volume >= 0
    )


def validate_price_sequence(prices: List[PricePoint]) -> bool:
    """Validate a sequence of price points."""
    if not prices:
        return False

    for i, price in enumerate(prices):
        if not validate_price_point(price):
            return False

        # Check timestamp ordering
        if i > 0 and price.timestamp <= prices[i-1].timestamp:
            return False

    return True


# Test configuration
pytest_plugins = []
