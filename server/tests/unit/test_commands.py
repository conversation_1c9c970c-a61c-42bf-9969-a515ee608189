"""
Unit tests for command handlers.

This module tests the command handlers in the application layer,
ensuring they properly coordinate with services and handle errors.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch

# Mock the imports to avoid dependency issues
try:
    from src.application.commands.analyze_stock_command import AnalyzeStockCommand
    from src.application.commands.fetch_data_command import FetchData<PERSON>ommand
    from src.domain.entities.stock_models import (
        StockAnalysis, AnalysisRequest, PricePoint, DataProvider
    )
    from src.domain.exceptions.stock_exceptions import (
        AnalysisException, DataFetchException, SymbolNotFoundException
    )
except ImportError:
    # Create mock classes if imports fail
    AnalyzeStockCommand = Mock
    FetchDataCommand = Mock
    StockAnalysis = Mock
    AnalysisRequest = Mock
    PricePoint = Mock
    DataProvider = Mock
    AnalysisException = Exception
    DataFetchException = Exception
    SymbolNotFoundException = Exception


class TestAnalyzeStockCommand:
    """Test cases for AnalyzeStockCommand."""

    @pytest.fixture
    def mock_analysis_service(self):
        """Create a mock analysis service."""
        return Mock()

    @pytest.fixture
    def command(self, mock_analysis_service):
        """Create an AnalyzeStockCommand instance."""
        return AnalyzeStockCommand(mock_analysis_service)

    def test_analyze_single_stock_success(self, command, mock_analysis_service):
        """Test successful single stock analysis."""
        # Arrange
        symbol = "VIC"
        mock_analysis = Mock(spec=StockAnalysis)
        mock_analysis.symbol = symbol
        mock_analysis.current_price = 100.0
        mock_analysis.recommendation.value = "BUY"
        mock_analysis_service.analyze_stock.return_value = mock_analysis

        # Act
        result = command.analyze_single_stock(symbol)

        # Assert
        assert result == mock_analysis
        mock_analysis_service.analyze_stock.assert_called_once()
        call_args = mock_analysis_service.analyze_stock.call_args[0][0]
        assert isinstance(call_args, AnalysisRequest)
        assert call_args.symbol == symbol

    def test_analyze_single_stock_symbol_not_found(
        self, command, mock_analysis_service
    ):
        """Test analysis with symbol not found."""
        # Arrange
        symbol = "INVALID"
        mock_analysis_service.analyze_stock.side_effect = (
            SymbolNotFoundException(f"Symbol {symbol} not found")
        )

        # Act & Assert
        with pytest.raises(SymbolNotFoundException):
            command.analyze_single_stock(symbol)

    def test_analyze_single_stock_analysis_exception(
        self, command, mock_analysis_service
    ):
        """Test analysis with analysis exception."""
        # Arrange
        symbol = "VIC"
        mock_analysis_service.analyze_stock.side_effect = (
            AnalysisException("Analysis failed")
        )

        # Act & Assert
        with pytest.raises(AnalysisException):
            command.analyze_single_stock(symbol)

    def test_analyze_multiple_stocks_success(self, command, mock_analysis_service):
        """Test successful multiple stock analysis."""
        # Arrange
        symbols = ["VIC", "HPG", "TCB"]
        mock_analyses = []
        for symbol in symbols:
            mock_analysis = Mock(spec=StockAnalysis)
            mock_analysis.symbol = symbol
            mock_analyses.append(mock_analysis)

        mock_analysis_service.analyze_stock.side_effect = mock_analyses

        # Act
        result = command.analyze_multiple_stocks(symbols)

        # Assert
        assert len(result) == 3
        assert mock_analysis_service.analyze_stock.call_count == 3

    def test_analyze_multiple_stocks_empty_list(self, command):
        """Test multiple stock analysis with empty symbol list."""
        # Act & Assert
        with pytest.raises(AnalysisException, match="No symbols provided"):
            command.analyze_multiple_stocks([])

    def test_get_quick_recommendation_success(self, command, mock_analysis_service):
        """Test successful quick recommendation."""
        # Arrange
        symbol = "VIC"
        mock_analysis = Mock(spec=StockAnalysis)
        mock_analysis.symbol = symbol
        mock_analysis.current_price = 100.0
        mock_analysis.price_change_percent = 2.5
        mock_analysis.recommendation.value = "BUY"
        mock_analysis.trend_analysis.direction.value = "UP"
        mock_analysis.trend_analysis.strength = 0.8
        mock_analysis.confidence_score = 85.0
        mock_analysis.market_condition.value = "TRENDING"
        mock_analysis.technical_indicators = []

        mock_analysis_service.analyze_stock.return_value = mock_analysis

        # Act
        result = command.get_quick_recommendation(symbol)

        # Assert
        assert result["symbol"] == symbol
        assert result["current_price"] == 100.0
        assert result["recommendation"] == "BUY"
        assert result["trend_direction"] == "UP"
        assert result["confidence_score"] == 85.0

    @patch('src.application.commands.analyze_stock_command.validate_symbol')
    def test_analyze_single_stock_invalid_symbol(
        self, mock_validate, command
    ):
        """Test analysis with invalid symbol format."""
        # Arrange
        mock_validate.side_effect = ValueError("Invalid symbol format")

        # Act & Assert
        with pytest.raises(AnalysisException):
            command.analyze_single_stock("invalid_symbol")


class TestFetchDataCommand:
    """Test cases for FetchDataCommand."""

    @pytest.fixture
    def mock_data_service(self):
        """Create a mock data service."""
        return Mock()

    @pytest.fixture
    def command(self, mock_data_service):
        """Create a FetchDataCommand instance."""
        return FetchDataCommand(mock_data_service)

    def test_fetch_daily_prices_success(self, command, mock_data_service):
        """Test successful daily price fetching."""
        # Arrange
        symbol = "VIC"
        mock_prices = [
            Mock(spec=PricePoint),
            Mock(spec=PricePoint),
            Mock(spec=PricePoint)
        ]
        mock_data_service.get_daily_prices.return_value = mock_prices

        # Act
        result = command.fetch_daily_prices(symbol)

        # Assert
        assert result == mock_prices
        mock_data_service.get_daily_prices.assert_called_once_with(
            symbol=symbol,
            days=365,
            force_refresh=False
        )

    def test_fetch_daily_prices_symbol_not_found(
        self, command, mock_data_service
    ):
        """Test daily price fetching with symbol not found."""
        # Arrange
        symbol = "INVALID"
        mock_data_service.get_daily_prices.side_effect = (
            SymbolNotFoundException(f"Symbol {symbol} not found")
        )

        # Act & Assert
        with pytest.raises(SymbolNotFoundException):
            command.fetch_daily_prices(symbol)

    def test_fetch_daily_prices_data_fetch_exception(
        self, command, mock_data_service
    ):
        """Test daily price fetching with data fetch exception."""
        # Arrange
        symbol = "VIC"
        mock_data_service.get_daily_prices.side_effect = (
            DataFetchException("Data fetch failed")
        )

        # Act & Assert
        with pytest.raises(DataFetchException):
            command.fetch_daily_prices(symbol)

    def test_refresh_all_symbols_success(self, command, mock_data_service):
        """Test successful refresh of all symbols."""
        # Arrange
        mock_result = {
            "success": 10,
            "failed": 2,
            "message": "Refreshed 10 symbols successfully"
        }
        mock_data_service.refresh_all_symbols.return_value = mock_result

        # Act
        result = command.refresh_all_symbols()

        # Assert
        assert result == mock_result
        mock_data_service.refresh_all_symbols.assert_called_once_with(
            provider=DataProvider.SSI,
            max_workers=10
        )

    def test_validate_data_source_success(self, command, mock_data_service):
        """Test successful data source validation."""
        # Arrange
        provider = DataProvider.SSI
        mock_prices = [Mock(spec=PricePoint)]
        mock_data_service.get_daily_prices.return_value = mock_prices

        # Act
        result = command.validate_data_source(provider)

        # Assert
        assert result is True

    def test_validate_data_source_failure(self, command, mock_data_service):
        """Test data source validation failure."""
        # Arrange
        provider = DataProvider.SSI
        mock_data_service.get_daily_prices.side_effect = (
            DataFetchException("Connection failed")
        )

        # Act
        result = command.validate_data_source(provider)

        # Assert
        assert result is False
