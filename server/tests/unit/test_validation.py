"""
Unit tests for validation utilities.

This module tests the validation functions used throughout
the application to ensure data integrity.
"""

import pytest
from shared.utils.validation import (
    validate_symbol, validate_days_back, validate_provider,
    validate_price, validate_volume, validate_timestamp,
    validate_percentage, validate_confidence_score,
    validate_symbol_list, validate_indicator_name
)
from src.domain.entities.stock_models import DataProvider
from src.domain.exceptions.stock_exceptions import DataValidationException


class TestSymbolValidation:
    """Test cases for symbol validation."""

    def test_validate_symbol_success(self):
        """Test successful symbol validation."""
        # Test valid Vietnamese stock symbols (3-4 letters only)
        valid_symbols = ["VIC", "HPG", "TCB", "VNM", "FPT", "HOSE"]

        for symbol in valid_symbols:
            result = validate_symbol(symbol)
            assert result == symbol.upper()

    def test_validate_symbol_normalization(self):
        """Test symbol normalization (uppercase, trimmed)."""
        test_cases = [
            ("vic", "VIC"),
            ("  hpg  ", "HPG"),
            ("tcb", "TCB"),
            ("vnm ", "VNM"),
            (" fpt", "FPT")
        ]

        for input_symbol, expected in test_cases:
            result = validate_symbol(input_symbol)
            assert result == expected

    def test_validate_symbol_empty(self):
        """Test validation with empty symbol."""
        with pytest.raises(DataValidationException, match="Symbol cannot be empty"):
            validate_symbol("")

    def test_validate_symbol_none(self):
        """Test validation with None symbol."""
        with pytest.raises(DataValidationException, match="Symbol cannot be empty"):
            validate_symbol(None)

    def test_validate_symbol_invalid_format(self):
        """Test validation with invalid symbol format."""
        invalid_symbols = ["V", "VV", "TOOLONG", "123", "VIC@", "V-IC"]

        for symbol in invalid_symbols:
            with pytest.raises(DataValidationException, match="Invalid symbol format"):
                validate_symbol(symbol)


class TestDaysBackValidation:
    """Test cases for days_back validation."""

    def test_validate_days_back_success(self):
        """Test successful days_back validation."""
        valid_days = [1, 30, 90, 365, 1000, 3650]

        for days in valid_days:
            result = validate_days_back(days)
            assert result == days

    def test_validate_days_back_zero(self):
        """Test validation with zero days."""
        with pytest.raises(DataValidationException, match="Days must be positive"):
            validate_days_back(0)

    def test_validate_days_back_negative(self):
        """Test validation with negative days."""
        with pytest.raises(DataValidationException, match="Days must be positive"):
            validate_days_back(-10)

    def test_validate_days_back_too_large(self):
        """Test validation with too many days."""
        with pytest.raises(DataValidationException, match="Days cannot exceed 3650"):
            validate_days_back(5000)

    def test_validate_days_back_non_integer(self):
        """Test validation with non-integer input."""
        with pytest.raises(DataValidationException, match="Days must be an integer"):
            validate_days_back(30.5)


class TestProviderValidation:
    """Test cases for provider validation."""

    def test_validate_provider_enum(self):
        """Test validation with DataProvider enum."""
        for provider in DataProvider:
            result = validate_provider(provider)
            assert result == provider

    def test_validate_provider_string(self):
        """Test validation with string provider."""
        # Note: DataProvider enum values are lowercase
        test_cases = [
            ("ssi", DataProvider.SSI),
            ("SSI", DataProvider.SSI),
            ("vietstock", DataProvider.VIETSTOCK),
            ("VIETSTOCK", DataProvider.VIETSTOCK),
            ("cafef", DataProvider.CAFEF),
            ("CAFEF", DataProvider.CAFEF)
        ]

        for input_provider, expected in test_cases:
            result = validate_provider(input_provider)
            assert result == expected

    def test_validate_provider_invalid(self):
        """Test validation with invalid provider."""
        with pytest.raises(DataValidationException, match="Invalid provider"):
            validate_provider("invalid_provider")

    def test_validate_provider_wrong_type(self):
        """Test validation with wrong type."""
        with pytest.raises(DataValidationException, match="Provider must be string or DataProvider enum"):
            validate_provider(123)


class TestPriceValidation:
    """Test cases for price validation."""

    def test_validate_price_success(self):
        """Test successful price validation."""
        valid_prices = [0.01, 1.0, 100.0, 1000.0, 50000.0]

        for price in valid_prices:
            result = validate_price(price)
            assert result == float(price)

    def test_validate_price_zero(self):
        """Test validation with zero price."""
        with pytest.raises(DataValidationException, match="price must be positive"):
            validate_price(0)

    def test_validate_price_negative(self):
        """Test validation with negative price."""
        with pytest.raises(DataValidationException, match="price must be positive"):
            validate_price(-10.0)

    def test_validate_price_too_high(self):
        """Test validation with unreasonably high price."""
        with pytest.raises(DataValidationException, match="price seems unreasonably high"):
            validate_price(2000000.0)

    def test_validate_price_non_numeric(self):
        """Test validation with non-numeric input."""
        with pytest.raises(DataValidationException, match="price must be a number"):
            validate_price("not_a_number")


class TestVolumeValidation:
    """Test cases for volume validation."""

    def test_validate_volume_success(self):
        """Test successful volume validation."""
        valid_volumes = [0, 1, 1000, 1000000, 100000000]

        for volume in valid_volumes:
            result = validate_volume(volume)
            assert result == int(volume)

    def test_validate_volume_negative(self):
        """Test validation with negative volume."""
        with pytest.raises(DataValidationException, match="volume cannot be negative"):
            validate_volume(-1000)

    def test_validate_volume_float_conversion(self):
        """Test validation with float input (should convert to int)."""
        result = validate_volume(1000.0)
        assert result == 1000
        assert isinstance(result, int)

    def test_validate_volume_non_numeric(self):
        """Test validation with non-numeric input."""
        with pytest.raises(DataValidationException, match="volume must be a number"):
            validate_volume("not_a_number")


class TestTimestampValidation:
    """Test cases for timestamp validation."""

    def test_validate_timestamp_success(self):
        """Test successful timestamp validation."""
        # Valid timestamps (2000-2099)
        valid_timestamps = [946684800, 1640995200, 2147483647]

        for timestamp in valid_timestamps:
            result = validate_timestamp(timestamp)
            assert result == timestamp

    def test_validate_timestamp_negative(self):
        """Test validation with negative timestamp."""
        with pytest.raises(DataValidationException, match="timestamp must be positive"):
            validate_timestamp(-1)

    def test_validate_timestamp_too_old(self):
        """Test validation with timestamp before year 2000."""
        with pytest.raises(DataValidationException, match="timestamp is outside reasonable range"):
            validate_timestamp(946684799)  # 1999-12-31

    def test_validate_timestamp_too_future(self):
        """Test validation with timestamp after year 2100."""
        with pytest.raises(DataValidationException, match="timestamp is outside reasonable range"):
            validate_timestamp(4102444801)  # 2100-01-02

    def test_validate_timestamp_non_integer(self):
        """Test validation with non-integer input."""
        with pytest.raises(DataValidationException, match="timestamp must be an integer"):
            validate_timestamp(1640995200.5)


class TestPercentageValidation:
    """Test cases for percentage validation."""

    def test_validate_percentage_success(self):
        """Test successful percentage validation."""
        valid_percentages = [-100, -50.5, 0, 25.5, 100]

        for percentage in valid_percentages:
            result = validate_percentage(percentage)
            assert result == float(percentage)

    def test_validate_percentage_too_low(self):
        """Test validation with percentage below -100."""
        with pytest.raises(DataValidationException, match="percentage must be between -100 and 100"):
            validate_percentage(-150)

    def test_validate_percentage_too_high(self):
        """Test validation with percentage above 100."""
        with pytest.raises(DataValidationException, match="percentage must be between -100 and 100"):
            validate_percentage(150)

    def test_validate_percentage_non_numeric(self):
        """Test validation with non-numeric input."""
        with pytest.raises(DataValidationException, match="percentage must be a number"):
            validate_percentage("not_a_number")


class TestConfidenceScoreValidation:
    """Test cases for confidence score validation."""

    def test_validate_confidence_score_success(self):
        """Test successful confidence score validation."""
        valid_scores = [0, 25.5, 50, 75.5, 100]

        for score in valid_scores:
            result = validate_confidence_score(score)
            assert result == float(score)

    def test_validate_confidence_score_negative(self):
        """Test validation with negative confidence score."""
        with pytest.raises(DataValidationException, match="Confidence score must be between 0 and 100"):
            validate_confidence_score(-10)

    def test_validate_confidence_score_too_high(self):
        """Test validation with confidence score above 100."""
        with pytest.raises(DataValidationException, match="Confidence score must be between 0 and 100"):
            validate_confidence_score(150)

    def test_validate_confidence_score_non_numeric(self):
        """Test validation with non-numeric input."""
        with pytest.raises(DataValidationException, match="Confidence score must be a number"):
            validate_confidence_score("not_a_number")


class TestSymbolListValidation:
    """Test cases for symbol list validation."""

    def test_validate_symbol_list_success(self):
        """Test successful symbol list validation."""
        symbols = ["VIC", "HPG", "TCB"]
        result = validate_symbol_list(symbols)
        assert result == symbols

    def test_validate_symbol_list_normalization(self):
        """Test symbol list normalization."""
        symbols = ["vic", "  hpg  ", "TCB"]
        expected = ["VIC", "HPG", "TCB"]
        result = validate_symbol_list(symbols)
        assert result == expected

    def test_validate_symbol_list_empty(self):
        """Test validation with empty symbol list."""
        with pytest.raises(DataValidationException, match="Symbol list cannot be empty"):
            validate_symbol_list([])

    def test_validate_symbol_list_duplicates(self):
        """Test validation with duplicate symbols."""
        with pytest.raises(DataValidationException, match="Duplicate symbols found"):
            validate_symbol_list(["VIC", "HPG", "VIC"])

    def test_validate_symbol_list_too_many(self):
        """Test validation with too many symbols."""
        symbols = [f"SYM{i:02d}" for i in range(101)]  # 101 symbols
        with pytest.raises(DataValidationException, match="Too many symbols"):
            validate_symbol_list(symbols)

    def test_validate_symbol_list_invalid_symbol(self):
        """Test validation with invalid symbol in list."""
        with pytest.raises(DataValidationException, match="Invalid symbol at index"):
            validate_symbol_list(["VIC", "INVALID_SYMBOL", "HPG"])

    def test_validate_symbol_list_non_list(self):
        """Test validation with non-list input."""
        with pytest.raises(DataValidationException, match="Symbols must be a list"):
            validate_symbol_list("not_a_list")


class TestIndicatorNameValidation:
    """Test cases for indicator name validation."""

    def test_validate_indicator_name_success(self):
        """Test successful indicator name validation."""
        valid_names = ["RSI", "MACD", "SMA20", "EMA_50", "BOLLINGER_BANDS"]

        for name in valid_names:
            result = validate_indicator_name(name)
            assert result == name.upper()

    def test_validate_indicator_name_normalization(self):
        """Test indicator name normalization."""
        test_cases = [
            ("rsi", "RSI"),
            ("  macd  ", "MACD"),
            ("sma20", "SMA20"),
            ("ema_50 ", "EMA_50")
        ]

        for input_name, expected in test_cases:
            result = validate_indicator_name(input_name)
            assert result == expected

    def test_validate_indicator_name_empty(self):
        """Test validation with empty indicator name."""
        with pytest.raises(DataValidationException, match="Indicator name cannot be empty"):
            validate_indicator_name("")

    def test_validate_indicator_name_invalid_format(self):
        """Test validation with invalid indicator name format."""
        invalid_names = ["RSI-14", "MACD@Signal", "SMA 20", "EMA.50"]

        for name in invalid_names:
            with pytest.raises(DataValidationException, match="Invalid indicator name format"):
                validate_indicator_name(name)

    def test_validate_indicator_name_non_string(self):
        """Test validation with non-string input."""
        with pytest.raises(DataValidationException, match="Indicator name must be a string"):
            validate_indicator_name(123)
