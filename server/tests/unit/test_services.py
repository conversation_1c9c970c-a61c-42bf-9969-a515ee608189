"""
Unit tests for service layer components.

This module tests the core services that implement business logic,
ensuring they properly handle data and coordinate with repositories.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from src.application.services.data_service import DataService
from src.domain.entities.stock_models import (
    PricePoint, DataProvider, StockAnalysis, AnalysisRequest
)
from src.domain.exceptions.stock_exceptions import (
    DataFetchException, SymbolNotFoundException, InsufficientDataException
)


class TestDataService:
    """Test cases for DataService."""

    @pytest.fixture
    def mock_price_repository(self):
        """Create a mock price repository."""
        return Mock()

    @pytest.fixture
    def mock_symbol_repository(self):
        """Create a mock symbol repository."""
        return Mock()

    @pytest.fixture
    def mock_data_fetcher(self):
        """Create a mock data fetcher."""
        return Mock()

    @pytest.fixture
    def data_service(
        self, mock_price_repository, mock_symbol_repository, mock_data_fetcher
    ):
        """Create a DataService instance."""
        return DataService(
            price_repository=mock_price_repository,
            symbol_repository=mock_symbol_repository,
            data_fetcher=mock_data_fetcher
        )

    def test_get_daily_prices_from_cache(
        self, data_service, mock_price_repository
    ):
        """Test getting daily prices from local cache."""
        # Arrange
        symbol = "VIC"
        days = 30
        mock_prices = [
            Mock(spec=PricePoint, timestamp=1640995200),  # 2022-01-01
            Mock(spec=PricePoint, timestamp=1641081600),  # 2022-01-02
        ]
        mock_price_repository.get_daily_prices.return_value = mock_prices

        # Act
        result = data_service.get_daily_prices(symbol, days)

        # Assert
        assert result == mock_prices
        mock_price_repository.get_daily_prices.assert_called_once_with(
            symbol, days, None
        )

    def test_get_daily_prices_fetch_external(
        self, data_service, mock_price_repository, mock_data_fetcher
    ):
        """Test fetching daily prices from external source."""
        # Arrange
        symbol = "VIC"
        days = 30
        mock_price_repository.get_daily_prices.side_effect = (
            SymbolNotFoundException("Symbol not found in local storage")
        )
        mock_external_prices = [
            Mock(spec=PricePoint, timestamp=1640995200),
            Mock(spec=PricePoint, timestamp=1641081600),
        ]
        mock_data_fetcher.fetch_daily_prices.return_value = mock_external_prices

        # Act
        result = data_service.get_daily_prices(symbol, days)

        # Assert
        assert result == mock_external_prices
        mock_data_fetcher.fetch_daily_prices.assert_called_once()
        mock_price_repository.save_daily_prices.assert_called_once_with(
            symbol, mock_external_prices
        )

    def test_get_daily_prices_insufficient_data(
        self, data_service, mock_price_repository, mock_data_fetcher
    ):
        """Test handling insufficient data scenario."""
        # Arrange
        symbol = "VIC"
        days = 365
        mock_price_repository.get_daily_prices.return_value = []
        mock_data_fetcher.fetch_daily_prices.return_value = []

        # Act & Assert
        with pytest.raises(InsufficientDataException):
            data_service.get_daily_prices(symbol, days)

    def test_get_daily_prices_force_refresh(
        self, data_service, mock_price_repository, mock_data_fetcher
    ):
        """Test force refresh bypasses local cache."""
        # Arrange
        symbol = "VIC"
        days = 30
        force_refresh = True
        mock_external_prices = [Mock(spec=PricePoint)]
        mock_data_fetcher.fetch_daily_prices.return_value = mock_external_prices

        # Act
        result = data_service.get_daily_prices(
            symbol, days, force_refresh=force_refresh
        )

        # Assert
        assert result == mock_external_prices
        # Should not call repository get method when force refresh
        mock_price_repository.get_daily_prices.assert_not_called()
        mock_data_fetcher.fetch_daily_prices.assert_called_once()

    def test_get_minute_prices_success(
        self, data_service, mock_price_repository
    ):
        """Test getting minute prices successfully."""
        # Arrange
        symbol = "VIC"
        days = 5
        mock_prices = [Mock(spec=PricePoint) for _ in range(1000)]
        mock_price_repository.get_minute_prices.return_value = mock_prices

        # Act
        result = data_service.get_minute_prices(symbol, days)

        # Assert
        assert result == mock_prices
        mock_price_repository.get_minute_prices.assert_called_once_with(
            symbol, days, None
        )

    def test_refresh_all_symbols_success(
        self, data_service, mock_symbol_repository, mock_data_fetcher
    ):
        """Test successful refresh of all symbols."""
        # Arrange
        mock_symbols = ["VIC", "HPG", "TCB"]
        mock_symbol_repository.get_all_symbols.return_value = mock_symbols
        mock_data_fetcher.fetch_daily_prices.return_value = [Mock(spec=PricePoint)]

        # Act
        result = data_service.refresh_all_symbols()

        # Assert
        assert result["success"] == 3
        assert result["failed"] == 0
        assert "Successfully refreshed" in result["message"]

    def test_refresh_all_symbols_partial_failure(
        self, data_service, mock_symbol_repository, mock_data_fetcher
    ):
        """Test refresh with some symbols failing."""
        # Arrange
        mock_symbols = ["VIC", "INVALID", "HPG"]
        mock_symbol_repository.get_all_symbols.return_value = mock_symbols

        def mock_fetch_side_effect(request):
            if request.symbol == "INVALID":
                raise DataFetchException("Symbol not found")
            return [Mock(spec=PricePoint)]

        mock_data_fetcher.fetch_daily_prices.side_effect = mock_fetch_side_effect

        # Act
        result = data_service.refresh_all_symbols()

        # Assert
        assert result["success"] == 2
        assert result["failed"] == 1
        assert "2 symbols successfully" in result["message"]

    def test_get_symbol_info_success(
        self, data_service, mock_symbol_repository
    ):
        """Test getting symbol information successfully."""
        # Arrange
        symbol = "VIC"
        mock_info = {
            "symbol": symbol,
            "name": "Vingroup",
            "exchange": "HOSE",
            "sector": "Real Estate"
        }
        mock_symbol_repository.get_symbol_info.return_value = mock_info

        # Act
        result = data_service.get_symbol_info(symbol)

        # Assert
        assert result == mock_info
        mock_symbol_repository.get_symbol_info.assert_called_once_with(symbol)

    def test_get_symbol_info_not_found(
        self, data_service, mock_symbol_repository
    ):
        """Test getting symbol information when symbol not found."""
        # Arrange
        symbol = "INVALID"
        mock_symbol_repository.get_symbol_info.side_effect = (
            SymbolNotFoundException(f"Symbol {symbol} not found")
        )

        # Act & Assert
        with pytest.raises(SymbolNotFoundException):
            data_service.get_symbol_info(symbol)

    @patch('src.application.services.data_service.validate_symbol')
    def test_get_daily_prices_invalid_symbol(
        self, mock_validate, data_service
    ):
        """Test handling invalid symbol format."""
        # Arrange
        mock_validate.side_effect = ValueError("Invalid symbol format")

        # Act & Assert
        with pytest.raises(DataFetchException):
            data_service.get_daily_prices("invalid_symbol")

    def test_get_daily_prices_with_end_date(
        self, data_service, mock_price_repository
    ):
        """Test getting daily prices with end date filter."""
        # Arrange
        symbol = "VIC"
        days = 30
        end_date = datetime(2022, 6, 1)
        mock_prices = [
            Mock(spec=PricePoint, timestamp=1640995200),  # 2022-01-01
            Mock(spec=PricePoint, timestamp=1654041600),  # 2022-06-01
        ]
        mock_price_repository.get_daily_prices.return_value = mock_prices

        # Act
        result = data_service.get_daily_prices(symbol, days, end_date=end_date)

        # Assert
        assert result == mock_prices
        mock_price_repository.get_daily_prices.assert_called_once_with(
            symbol, days, end_date
        )

    def test_save_daily_prices_success(
        self, data_service, mock_price_repository
    ):
        """Test saving daily prices successfully."""
        # Arrange
        symbol = "VIC"
        prices = [Mock(spec=PricePoint), Mock(spec=PricePoint)]

        # Act
        data_service.save_daily_prices(symbol, prices)

        # Assert
        mock_price_repository.save_daily_prices.assert_called_once_with(
            symbol, prices
        )

    def test_get_available_symbols(
        self, data_service, mock_symbol_repository
    ):
        """Test getting list of available symbols."""
        # Arrange
        mock_symbols = ["VIC", "HPG", "TCB", "VNM"]
        mock_symbol_repository.get_all_symbols.return_value = mock_symbols

        # Act
        result = data_service.get_available_symbols()

        # Assert
        assert result == mock_symbols
        mock_symbol_repository.get_all_symbols.assert_called_once()

    def test_validate_symbol_exists(
        self, data_service, mock_symbol_repository
    ):
        """Test validating that a symbol exists."""
        # Arrange
        symbol = "VIC"
        mock_symbol_repository.symbol_exists.return_value = True

        # Act
        result = data_service.validate_symbol_exists(symbol)

        # Assert
        assert result is True
        mock_symbol_repository.symbol_exists.assert_called_once_with(symbol)

    def test_validate_symbol_not_exists(
        self, data_service, mock_symbol_repository
    ):
        """Test validating that a symbol does not exist."""
        # Arrange
        symbol = "INVALID"
        mock_symbol_repository.symbol_exists.return_value = False

        # Act
        result = data_service.validate_symbol_exists(symbol)

        # Assert
        assert result is False
        mock_symbol_repository.symbol_exists.assert_called_once_with(symbol)
