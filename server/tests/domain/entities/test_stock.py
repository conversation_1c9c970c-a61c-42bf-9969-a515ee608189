"""
Test Stock Entity

Unit tests for the Stock domain entity.
"""

import pytest
from datetime import datetime
from decimal import Decimal

import sys
from pathlib import Path
server_dir = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(server_dir))

from src.domain.entities.stock import Stock


class TestStock:
    """Test cases for Stock entity."""
    
    def test_stock_creation_valid(self):
        """Test creating a valid stock entity."""
        stock = Stock(
            symbol="VCB",
            name="Vietcombank",
            exchange="HOSE",
            sector="Banking",
            industry="Commercial Banks"
        )
        
        assert stock.symbol == "VCB"
        assert stock.name == "Vietcombank"
        assert stock.exchange == "HOSE"
        assert stock.sector == "Banking"
        assert stock.industry == "Commercial Banks"
        assert stock.is_valid_for_analysis()
    
    def test_stock_symbol_normalization(self):
        """Test that stock symbol is normalized to uppercase."""
        stock = Stock(
            symbol="vcb",
            name="Vietcombank",
            exchange="hose"
        )
        
        assert stock.symbol == "VCB"
        assert stock.exchange == "hose"  # Only symbol is normalized
    
    def test_stock_creation_invalid_empty_symbol(self):
        """Test that empty symbol raises ValueError."""
        with pytest.raises(ValueError, match="Stock symbol cannot be empty"):
            Stock(symbol="", name="Test", exchange="HOSE")
    
    def test_stock_creation_invalid_empty_name(self):
        """Test that empty name raises ValueError."""
        with pytest.raises(ValueError, match="Stock name cannot be empty"):
            Stock(symbol="TEST", name="", exchange="HOSE")
    
    def test_stock_creation_invalid_empty_exchange(self):
        """Test that empty exchange raises ValueError."""
        with pytest.raises(ValueError, match="Stock exchange cannot be empty"):
            Stock(symbol="TEST", name="Test", exchange="")
    
    def test_stock_display_name(self):
        """Test stock display name formatting."""
        stock = Stock(symbol="VCB", name="Vietcombank", exchange="HOSE")
        assert stock.get_display_name() == "VCB - Vietcombank"
    
    def test_stock_equality(self):
        """Test stock equality based on symbol and exchange."""
        stock1 = Stock(symbol="VCB", name="Vietcombank", exchange="HOSE")
        stock2 = Stock(symbol="VCB", name="Different Name", exchange="HOSE")
        stock3 = Stock(symbol="VCB", name="Vietcombank", exchange="HNX")
        
        assert stock1 == stock2  # Same symbol and exchange
        assert stock1 != stock3  # Different exchange
    
    def test_stock_hash(self):
        """Test stock hashing for use in sets and dicts."""
        stock1 = Stock(symbol="VCB", name="Vietcombank", exchange="HOSE")
        stock2 = Stock(symbol="VCB", name="Different Name", exchange="HOSE")
        
        # Should have same hash since symbol and exchange are same
        assert hash(stock1) == hash(stock2)
        
        # Should be usable in sets
        stock_set = {stock1, stock2}
        assert len(stock_set) == 1  # Only one unique stock
    
    def test_stock_string_representation(self):
        """Test stock string representation."""
        stock = Stock(symbol="VCB", name="Vietcombank", exchange="HOSE")
        expected = "Stock(VCB, Vietcombank, HOSE)"
        assert str(stock) == expected
    
    def test_stock_validation_for_analysis(self):
        """Test stock validation for analysis."""
        # Valid stock
        valid_stock = Stock(symbol="VCB", name="Vietcombank", exchange="HOSE")
        assert valid_stock.is_valid_for_analysis()
        
        # Test with minimal required fields
        minimal_stock = Stock(symbol="TEST", name="Test", exchange="HOSE")
        assert minimal_stock.is_valid_for_analysis()


if __name__ == "__main__":
    pytest.main([__file__])
