"""
Integration tests for data providers in StockPal.

Tests the current working data provider architecture.
"""

import unittest
from datetime import datetime

# Import from existing working components
from stockpal.data.data_scraper import DataScraper
from stockpal.core.stock import PriceData


class TestDataProviderIntegration(unittest.TestCase):
    """Integration tests for data provider architecture."""

    def setUp(self):
        """Set up test environment."""
        # Test symbols
        self.test_symbols = ["VIC", "VHM", "HPG", "TCB", "BID"]

    def test_data_scraper_initialization(self):
        """Test that DataScraper can be initialized."""
        scraper = DataScraper("VIC", provider="ssi")
        self.assertIsNotNone(scraper)
        self.assertEqual(scraper._symbol, "VIC")

    def test_price_data_creation(self):
        """Test PriceData model creation."""
        price = PriceData(
            symbol="VIC",
            timestamp=int(datetime.now().timestamp()),
            open_price=100.0,
            highest_price=105.0,
            lowest_price=95.0,
            close_price=102.0,
            match_volume=1000000,
            change_price=2.0,
            change_price_percent=2.0
        )
        self.assertIsNotNone(price)
        self.assertEqual(price.close_price, 102.0)

    def test_multiple_providers(self):
        """Test that multiple providers can be used."""
        # Test SSI provider
        scraper_ssi = DataScraper("VIC", provider="ssi")
        self.assertIsNotNone(scraper_ssi)
        self.assertEqual(scraper_ssi._provider, "ssi")

        # Test VietStock provider
        scraper_vietstock = DataScraper("VIC", provider="vietstock")
        self.assertIsNotNone(scraper_vietstock)
        self.assertEqual(scraper_vietstock._provider, "vietstock")

        # Test CafeF provider
        scraper_cafef = DataScraper("VIC", provider="cafef")
        self.assertIsNotNone(scraper_cafef)
        self.assertEqual(scraper_cafef._provider, "cafef")


if __name__ == "__main__":
    unittest.main()
