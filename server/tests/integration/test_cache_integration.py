"""
Integration tests for caching functionality.

These tests validate the caching layer integration with the rest of the system.
"""

import pytest
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from shared.models.stock_models import PricePoint, TechnicalIndicator, StockAnalysis
from infrastructure.cache.cache_service import CacheService
from infrastructure.cache.cache_manager import CacheManager


class TestCacheIntegration:
    """Test cache integration with the application."""

    def test_cache_service_basic_operations(self, cache_service):
        """Test basic cache service operations."""
        # Test set and get
        test_data = {"test": "value", "number": 42}
        assert cache_service.set("test_key", test_data, "general")

        retrieved_data = cache_service.get("test_key", "general")
        assert retrieved_data == test_data

        # Test delete
        assert cache_service.delete("test_key", "general")
        assert cache_service.get("test_key", "general") is None

    def test_cache_expiration(self, temp_dir):
        """Test cache expiration functionality."""
        # Create cache with short TTL
        cache_service = CacheService(cache_dir=temp_dir, default_ttl=1)

        # Set data with short TTL
        test_data = {"expires": "soon"}
        assert cache_service.set("expire_test", test_data, "general", ttl=1)

        # Should be available immediately
        assert cache_service.get("expire_test", "general") == test_data

        # Wait for expiration
        time.sleep(2)

        # Should be expired now
        assert cache_service.get("expire_test", "general") is None

    def test_cache_manager_price_caching(self, cache_manager, sample_prices):
        """Test price data caching through cache manager."""
        symbol = "TEST"
        days = 30
        provider = "ssi"

        # Cache price data
        assert cache_manager.set_daily_prices(symbol, days, provider, sample_prices)

        # Retrieve cached data
        cached_prices = cache_manager.get_daily_prices(symbol, days, provider)
        assert cached_prices is not None
        assert len(cached_prices) == len(sample_prices)

        # Verify data integrity
        for original, cached in zip(sample_prices, cached_prices):
            assert original.timestamp == cached.timestamp
            assert original.close_price == cached.close_price
            assert original.volume == cached.volume

    def test_cache_manager_indicator_caching(self, cache_manager):
        """Test technical indicator caching."""
        symbol = "TEST"
        indicator_name = "RSI"
        parameters = {"period": 14}
        values = [30.5, 45.2, 67.8, 72.1, 55.9]

        # Cache indicator data
        assert cache_manager.set_indicator(symbol, indicator_name, parameters, values)

        # Retrieve cached data
        cached_values = cache_manager.get_indicator(symbol, indicator_name, parameters)
        assert cached_values == values

        # Test with different parameters
        different_params = {"period": 21}
        cached_different = cache_manager.get_indicator(symbol, indicator_name, different_params)
        assert cached_different is None  # Should not exist

    def test_cache_manager_analysis_caching(self, cache_manager):
        """Test analysis result caching."""
        from shared.models.stock_models import (
            StockAnalysis, TrendAnalysis, TrendDirection,
            SignalType, MarketCondition, ConfidenceLevel
        )

        symbol = "TEST"
        analysis_params = {"days_back": 60, "indicators": ["RSI", "MACD"]}

        # Create mock analysis
        analysis = StockAnalysis(
            symbol=symbol,
            current_price=100.0,
            analysis_date=datetime.now(),
            last_trading_date=datetime.now() - timedelta(days=1),
            trend_analysis=TrendAnalysis(
                direction=TrendDirection.BULLISH,
                strength=0.7,
                confidence=ConfidenceLevel.HIGH
            ),
            price_change=2.5,
            price_change_percent=2.5,
            technical_indicators=[],
            buy_zones=[],
            stop_loss_zones=[],
            take_profit_zones=[],
            risk_reward_ratios=[],
            recommendation=SignalType.BUY,
            market_condition=MarketCondition(
                condition="trending",
                volatility=0.15,
                volume_trend="stable"
            ),
            technical_summary="Bullish trend",
            confidence_score=0.75
        )

        # Cache analysis
        assert cache_manager.set_analysis(symbol, analysis_params, analysis)

        # Retrieve cached analysis
        cached_analysis = cache_manager.get_analysis(symbol, analysis_params)
        assert cached_analysis is not None
        assert cached_analysis.symbol == symbol
        assert cached_analysis.current_price == 100.0
        assert cached_analysis.recommendation == SignalType.BUY

    def test_cache_invalidation(self, cache_manager, sample_prices):
        """Test cache invalidation functionality."""
        symbol = "INVL"

        # Cache multiple types of data
        cache_manager.set_daily_prices(symbol, 30, "ssi", sample_prices)
        cache_manager.set_indicator(symbol, "RSI", {"period": 14}, [50.0, 60.0])

        # Verify data is cached
        assert cache_manager.get_daily_prices(symbol, 30, "ssi") is not None
        assert cache_manager.get_indicator(symbol, "RSI", {"period": 14}) is not None

        # Invalidate all data for symbol
        invalidated_count = cache_manager.invalidate_symbol_data(symbol)
        assert invalidated_count > 0

        # Verify data is invalidated
        assert cache_manager.get_daily_prices(symbol, 30, "ssi") is None
        assert cache_manager.get_indicator(symbol, "RSI", {"period": 14}) is None

    def test_cache_statistics(self, cache_manager, sample_prices):
        """Test cache statistics functionality."""
        # Add some data to cache
        cache_manager.set_daily_prices("STATS1", 30, "ssi", sample_prices)
        cache_manager.set_daily_prices("STATS2", 60, "vietstock", sample_prices)
        cache_manager.set_indicator("STATS1", "MACD", {"fast": 12, "slow": 26}, [1.2, 1.5])

        # Get cache statistics
        stats = cache_manager.get_cache_stats()

        # Validate statistics structure
        assert "hits" in stats
        assert "misses" in stats
        assert "hit_rate_percent" in stats
        assert "total_files" in stats
        assert "total_size_bytes" in stats
        assert "categories" in stats

        # Validate category statistics
        categories = stats["categories"]
        assert "prices" in categories
        assert "indicators" in categories
        assert "analysis" in categories
        assert "symbols" in categories

        # Should have some files in prices and indicators
        assert categories["prices"]["file_count"] > 0
        assert categories["indicators"]["file_count"] > 0

    def test_cache_cleanup(self, temp_dir):
        """Test cache cleanup functionality."""
        # Create cache with very short TTL
        cache_service = CacheService(cache_dir=temp_dir, default_ttl=1)
        cache_manager = CacheManager(cache_service)

        # Add data that will expire quickly
        test_data = [{"expired": True}]
        cache_manager.cache_service.set("expire1", test_data, "general", ttl=1)
        cache_manager.cache_service.set("expire2", test_data, "general", ttl=1)
        cache_manager.cache_service.set("keep", test_data, "general", ttl=3600)  # Long TTL

        # Wait for some to expire
        time.sleep(2)

        # Run cleanup
        cleaned_count = cache_manager.cleanup_expired()

        # Should have cleaned up expired entries
        assert cleaned_count >= 2

        # Long TTL entry should still exist
        assert cache_manager.cache_service.get("keep", "general") is not None

    def test_cache_with_data_service_integration(self, data_service, cache_manager, sample_prices):
        """Test cache integration with data service."""
        symbol = "TEST"

        # Inject cache manager into data service
        data_service._cache_manager = cache_manager

        with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch, \
             patch.object(data_service._symbol_repository, 'symbol_exists') as mock_symbol_exists:

            mock_fetch.return_value = sample_prices
            mock_symbol_exists.return_value = True

            # First call - should fetch from external source and cache
            prices1 = data_service.get_daily_prices(symbol=symbol, days=30, force_refresh=True)
            assert mock_fetch.call_count == 1

            # Cache the result
            cache_manager.set_daily_prices(symbol, 30, "ssi", prices1)

            # Second call - should use cache if available
            cached_prices = cache_manager.get_daily_prices(symbol, 30, "ssi")
            assert cached_prices is not None
            assert len(cached_prices) == len(prices1)

    def test_cache_market_hours_awareness(self, cache_manager):
        """Test cache TTL adjustment based on market hours."""
        # This test would need to be enhanced with actual market hours logic
        # For now, we test that the cache manager has the capability

        symbol = "MRKT"
        prices = []  # Empty for this test

        # During market hours, TTL should be shorter
        # Outside market hours, TTL should be longer
        # This is a simplified test of the concept

        assert cache_manager.set_daily_prices(symbol, 30, "ssi", prices)

        # Verify the cache manager has market hours awareness methods
        assert hasattr(cache_manager, '_is_market_hours')
        assert hasattr(cache_manager, '_get_price_ttl')

    def test_cache_concurrent_access(self, cache_manager, sample_prices):
        """Test cache behavior under concurrent access."""
        import concurrent.futures
        import threading

        symbol = "CONC"
        results = []
        errors = []

        def cache_operation(operation_id):
            try:
                # Each thread performs cache operations
                cache_manager.set_daily_prices(f"{symbol}_{operation_id}", 30, "ssi", sample_prices)
                cached_data = cache_manager.get_daily_prices(f"{symbol}_{operation_id}", 30, "ssi")
                results.append(len(cached_data) if cached_data else 0)
            except Exception as e:
                errors.append(str(e))

        # Run concurrent operations
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(cache_operation, i) for i in range(10)]
            concurrent.futures.wait(futures)

        # Validate results
        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == 10
        assert all(result == len(sample_prices) for result in results)

    def test_cache_large_data_handling(self, cache_manager):
        """Test cache handling of large datasets."""
        symbol = "LARG"

        # Create large price dataset
        large_prices = []
        base_timestamp = int(datetime.now().timestamp())

        for i in range(1000):  # 1000 price points
            price_point = PricePoint(
                timestamp=base_timestamp + (i * 3600),
                open_price=100.0 + i * 0.1,
                high_price=101.0 + i * 0.1,
                low_price=99.0 + i * 0.1,
                close_price=100.5 + i * 0.1,
                volume=1000000,
                symbol=symbol
            )
            large_prices.append(price_point)

        # Cache large dataset
        start_time = time.time()
        success = cache_manager.set_daily_prices(symbol, 1000, "ssi", large_prices)
        cache_time = time.time() - start_time

        assert success
        assert cache_time < 10.0  # Should complete within 10 seconds

        # Retrieve large dataset
        start_time = time.time()
        cached_prices = cache_manager.get_daily_prices(symbol, 1000, "ssi")
        retrieve_time = time.time() - start_time

        assert cached_prices is not None
        assert len(cached_prices) == 1000
        assert retrieve_time < 5.0  # Should retrieve within 5 seconds

    def test_cache_error_recovery(self, temp_dir):
        """Test cache error recovery and resilience."""
        cache_service = CacheService(cache_dir=temp_dir)
        cache_manager = CacheManager(cache_service)

        # Test with corrupted cache file
        corrupted_file = cache_service.cache_dir / "general" / "corrupted.json"
        corrupted_file.parent.mkdir(parents=True, exist_ok=True)

        # Write invalid JSON
        with open(corrupted_file, 'w') as f:
            f.write("invalid json content {")

        # Cache service should handle corrupted files gracefully
        result = cache_service.get("corrupted", "general")
        assert result is None  # Should return None for corrupted data

        # Cleanup should remove corrupted files
        cleaned = cache_service.cleanup_expired()
        assert cleaned >= 1  # Should clean up the corrupted file
