"""
Integration tests for data provider functionality.

This module tests the integration between data providers (SSI, VietStock, CafeF)
and the application services, ensuring data flows correctly through all layers.
"""

import pytest
import logging
from datetime import datetime, timedelta
from typing import List

from src.domain.entities.stock_models import (
    PricePoint, DataProvider, DataFetchRequest
)
from src.domain.exceptions.stock_exceptions import (
    DataFetchException, SymbolNotFoundException, NetworkException
)


# Configure test logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestDataProviderIntegration:
    """Integration tests for data provider functionality."""

    @pytest.fixture
    def test_symbols(self) -> List[str]:
        """Provide a list of test symbols."""
        return ["VIC", "HPG", "TCB", "VNM"]

    @pytest.fixture
    def invalid_symbols(self) -> List[str]:
        """Provide a list of invalid symbols for testing error handling."""
        return ["INVALID", "NOTFOUND", "BADSTOCK"]

    @pytest.mark.integration
    def test_ssi_data_provider_daily_prices(self, test_symbols):
        """Test SSI data provider for daily price fetching."""
        try:
            from src.infrastructure.adapters.ssi_adapter import SSIAdapter
            
            adapter = SSIAdapter()
            
            for symbol in test_symbols[:2]:  # Test first 2 symbols
                logger.info(f"Testing SSI daily prices for {symbol}")
                
                # Test basic daily price fetching
                prices = adapter.get_daily_prices(symbol, days=30)
                
                # Assertions
                assert isinstance(prices, list)
                assert len(prices) > 0, f"No prices returned for {symbol}"
                
                # Validate price data structure
                for price in prices[:5]:  # Check first 5 prices
                    assert isinstance(price, PricePoint)
                    assert price.timestamp > 0
                    assert price.open_price > 0
                    assert price.high_price > 0
                    assert price.low_price > 0
                    assert price.close_price > 0
                    assert price.volume >= 0
                    
                    # Validate price relationships
                    assert price.high_price >= price.low_price
                    assert price.low_price <= price.close_price <= price.high_price
                
                logger.info(f"✅ SSI daily prices test passed for {symbol}")
                
        except ImportError:
            pytest.skip("SSI adapter not available")
        except Exception as e:
            pytest.fail(f"SSI integration test failed: {str(e)}")

    @pytest.mark.integration
    def test_vietstock_data_provider_daily_prices(self, test_symbols):
        """Test VietStock data provider for daily price fetching."""
        try:
            from src.infrastructure.adapters.vietstock_adapter import VietStockAdapter
            
            adapter = VietStockAdapter()
            
            for symbol in test_symbols[:2]:  # Test first 2 symbols
                logger.info(f"Testing VietStock daily prices for {symbol}")
                
                try:
                    prices = adapter.get_daily_prices(symbol, days=30)
                    
                    # Assertions
                    assert isinstance(prices, list)
                    # Note: VietStock adapter might return empty list if not implemented
                    if len(prices) > 0:
                        for price in prices[:5]:
                            assert isinstance(price, PricePoint)
                            assert price.timestamp > 0
                            assert price.close_price > 0
                    
                    logger.info(f"✅ VietStock daily prices test passed for {symbol}")
                    
                except DataFetchException as e:
                    # Expected if VietStock is not fully implemented
                    logger.warning(f"VietStock not fully implemented: {e}")
                    
        except ImportError:
            pytest.skip("VietStock adapter not available")

    @pytest.mark.integration
    def test_data_provider_error_handling(self, invalid_symbols):
        """Test error handling for invalid symbols across providers."""
        try:
            from src.infrastructure.adapters.ssi_adapter import SSIAdapter
            
            adapter = SSIAdapter()
            
            for symbol in invalid_symbols[:2]:  # Test first 2 invalid symbols
                logger.info(f"Testing error handling for invalid symbol: {symbol}")
                
                with pytest.raises((SymbolNotFoundException, DataFetchException)):
                    adapter.get_daily_prices(symbol, days=30)
                
                logger.info(f"✅ Error handling test passed for {symbol}")
                
        except ImportError:
            pytest.skip("SSI adapter not available")

    @pytest.mark.integration
    def test_data_service_integration(self, test_symbols):
        """Test integration between data service and providers."""
        try:
            from src.application.services.data_service import DataService
            from src.infrastructure.repositories.price_repository import SqlitePriceRepository
            from src.infrastructure.repositories.symbol_repository import SqliteSymbolRepository
            from src.infrastructure.adapters.external_data_fetcher import ExternalDataFetcher
            
            # Setup dependencies
            price_repo = SqlitePriceRepository()
            symbol_repo = SqliteSymbolRepository()
            data_fetcher = ExternalDataFetcher()
            
            data_service = DataService(
                price_repository=price_repo,
                symbol_repository=symbol_repo,
                data_fetcher=data_fetcher
            )
            
            for symbol in test_symbols[:1]:  # Test one symbol
                logger.info(f"Testing data service integration for {symbol}")
                
                # Test daily price fetching through service
                prices = data_service.get_daily_prices(symbol, days=30)
                
                # Assertions
                assert isinstance(prices, list)
                assert len(prices) > 0, f"No prices returned for {symbol}"
                
                # Test that data is properly cached/stored
                cached_prices = data_service.get_daily_prices(symbol, days=30)
                assert len(cached_prices) == len(prices)
                
                logger.info(f"✅ Data service integration test passed for {symbol}")
                
        except ImportError as e:
            pytest.skip(f"Required components not available: {e}")

    @pytest.mark.integration
    def test_data_provider_performance(self, test_symbols):
        """Test data provider performance and response times."""
        try:
            from src.infrastructure.adapters.ssi_adapter import SSIAdapter
            import time
            
            adapter = SSIAdapter()
            symbol = test_symbols[0]  # Test with first symbol
            
            logger.info(f"Testing performance for {symbol}")
            
            # Test response time for daily prices
            start_time = time.time()
            prices = adapter.get_daily_prices(symbol, days=90)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # Assertions
            assert len(prices) > 0
            assert response_time < 30.0, f"Response time too slow: {response_time}s"
            
            logger.info(f"✅ Performance test passed: {response_time:.2f}s for {len(prices)} prices")
            
        except ImportError:
            pytest.skip("SSI adapter not available")

    @pytest.mark.integration
    def test_data_consistency_across_providers(self, test_symbols):
        """Test data consistency when available from multiple providers."""
        try:
            from src.infrastructure.adapters.ssi_adapter import SSIAdapter
            from src.infrastructure.adapters.vietstock_adapter import VietStockAdapter
            
            ssi_adapter = SSIAdapter()
            vietstock_adapter = VietStockAdapter()
            
            symbol = test_symbols[0]  # Test with first symbol
            days = 30
            
            logger.info(f"Testing data consistency for {symbol}")
            
            # Get data from both providers
            ssi_prices = ssi_adapter.get_daily_prices(symbol, days)
            
            try:
                vietstock_prices = vietstock_adapter.get_daily_prices(symbol, days)
                
                # If both providers return data, check consistency
                if len(ssi_prices) > 0 and len(vietstock_prices) > 0:
                    # Check that both have reasonable amount of data
                    assert len(ssi_prices) > days * 0.5  # At least 50% of requested days
                    assert len(vietstock_prices) > days * 0.5
                    
                    # Check that latest prices are reasonably close (within 10%)
                    if len(ssi_prices) > 0 and len(vietstock_prices) > 0:
                        ssi_latest = ssi_prices[-1].close_price
                        vietstock_latest = vietstock_prices[-1].close_price
                        
                        price_diff = abs(ssi_latest - vietstock_latest) / ssi_latest
                        assert price_diff < 0.1, f"Price difference too large: {price_diff:.2%}"
                
                logger.info(f"✅ Data consistency test passed for {symbol}")
                
            except DataFetchException:
                # VietStock might not be fully implemented
                logger.warning("VietStock data not available for consistency test")
                
        except ImportError:
            pytest.skip("Required adapters not available")

    @pytest.mark.integration
    def test_data_provider_minute_prices(self, test_symbols):
        """Test minute price data fetching if supported."""
        try:
            from src.infrastructure.adapters.ssi_adapter import SSIAdapter
            
            adapter = SSIAdapter()
            symbol = test_symbols[0]  # Test with first symbol
            
            logger.info(f"Testing minute prices for {symbol}")
            
            try:
                # Test minute price fetching
                prices = adapter.get_minute_prices(symbol, days=5)
                
                if len(prices) > 0:
                    # Validate minute price data
                    for price in prices[:10]:  # Check first 10 prices
                        assert isinstance(price, PricePoint)
                        assert price.timestamp > 0
                        assert price.close_price > 0
                    
                    # Check that we have more granular data than daily
                    # (should have more data points for minute prices)
                    daily_prices = adapter.get_daily_prices(symbol, days=5)
                    if len(daily_prices) > 0:
                        assert len(prices) > len(daily_prices)
                
                logger.info(f"✅ Minute prices test passed for {symbol}")
                
            except (DataFetchException, AttributeError):
                # Minute prices might not be supported
                logger.warning("Minute prices not supported or available")
                
        except ImportError:
            pytest.skip("SSI adapter not available")

    @pytest.mark.integration
    def test_data_provider_symbol_validation(self, test_symbols, invalid_symbols):
        """Test symbol validation across providers."""
        try:
            from src.infrastructure.adapters.ssi_adapter import SSIAdapter
            
            adapter = SSIAdapter()
            
            # Test valid symbols
            for symbol in test_symbols[:2]:
                logger.info(f"Testing symbol validation for valid symbol: {symbol}")
                
                try:
                    symbol_info = adapter.get_symbol_info(symbol)
                    assert symbol_info is not None
                    assert symbol_info.get('symbol') == symbol
                    
                except (AttributeError, DataFetchException):
                    # Symbol info might not be implemented
                    logger.warning(f"Symbol info not available for {symbol}")
            
            # Test invalid symbols
            for symbol in invalid_symbols[:1]:
                logger.info(f"Testing symbol validation for invalid symbol: {symbol}")
                
                try:
                    symbol_info = adapter.get_symbol_info(symbol)
                    # Should either raise exception or return None/empty
                    if symbol_info is not None:
                        assert symbol_info.get('symbol') != symbol or symbol_info == {}
                        
                except (SymbolNotFoundException, DataFetchException):
                    # Expected behavior for invalid symbols
                    pass
                except AttributeError:
                    # Symbol info might not be implemented
                    logger.warning("Symbol info validation not implemented")
            
            logger.info("✅ Symbol validation tests completed")
            
        except ImportError:
            pytest.skip("SSI adapter not available")

    @pytest.mark.integration
    def test_data_provider_date_range_handling(self, test_symbols):
        """Test handling of different date ranges."""
        try:
            from src.infrastructure.adapters.ssi_adapter import SSIAdapter
            
            adapter = SSIAdapter()
            symbol = test_symbols[0]  # Test with first symbol
            
            logger.info(f"Testing date range handling for {symbol}")
            
            # Test different date ranges
            test_ranges = [7, 30, 90, 365]
            
            for days in test_ranges:
                logger.info(f"Testing {days} days range")
                
                prices = adapter.get_daily_prices(symbol, days)
                
                # Assertions
                assert isinstance(prices, list)
                if len(prices) > 0:
                    # Check that we don't get more data than requested timeframe
                    # (allowing some flexibility for weekends/holidays)
                    assert len(prices) <= days * 1.5
                    
                    # Check date ordering (most recent first or last)
                    if len(prices) > 1:
                        timestamps = [p.timestamp for p in prices]
                        # Should be either ascending or descending
                        is_ascending = all(timestamps[i] <= timestamps[i+1] for i in range(len(timestamps)-1))
                        is_descending = all(timestamps[i] >= timestamps[i+1] for i in range(len(timestamps)-1))
                        assert is_ascending or is_descending, "Timestamps not properly ordered"
            
            logger.info(f"✅ Date range handling test passed for {symbol}")
            
        except ImportError:
            pytest.skip("SSI adapter not available")
