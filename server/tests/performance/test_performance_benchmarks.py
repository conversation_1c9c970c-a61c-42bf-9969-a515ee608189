"""
Performance benchmarks for StockPal components.

This module provides performance tests to ensure the system meets
performance requirements and to identify bottlenecks.
"""

import pytest
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
from unittest.mock import Mock

# Configure performance test logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """Helper class to collect and analyze performance metrics."""
    
    def __init__(self):
        self.metrics = {}
    
    def record_timing(self, operation: str, duration: float):
        """Record timing for an operation."""
        if operation not in self.metrics:
            self.metrics[operation] = []
        self.metrics[operation].append(duration)
    
    def get_stats(self, operation: str) -> Dict[str, float]:
        """Get statistics for an operation."""
        if operation not in self.metrics:
            return {}
        
        timings = self.metrics[operation]
        return {
            'count': len(timings),
            'min': min(timings),
            'max': max(timings),
            'avg': sum(timings) / len(timings),
            'total': sum(timings)
        }
    
    def print_summary(self):
        """Print performance summary."""
        logger.info("=== PERFORMANCE SUMMARY ===")
        for operation, timings in self.metrics.items():
            stats = self.get_stats(operation)
            logger.info(f"{operation}:")
            logger.info(f"  Count: {stats['count']}")
            logger.info(f"  Min: {stats['min']:.3f}s")
            logger.info(f"  Max: {stats['max']:.3f}s")
            logger.info(f"  Avg: {stats['avg']:.3f}s")
            logger.info(f"  Total: {stats['total']:.3f}s")


class TestDataFetchingPerformance:
    """Performance tests for data fetching operations."""
    
    @pytest.fixture
    def performance_metrics(self):
        """Create performance metrics collector."""
        return PerformanceMetrics()
    
    @pytest.fixture
    def test_symbols(self) -> List[str]:
        """Provide test symbols for performance testing."""
        return ["VIC", "HPG", "TCB", "VNM", "FPT"]
    
    @pytest.mark.performance
    def test_single_symbol_fetch_performance(self, test_symbols, performance_metrics):
        """Test performance of fetching data for a single symbol."""
        try:
            from src.application.services.data_service import DataService
            from unittest.mock import Mock
            
            # Create mock dependencies for isolated testing
            mock_price_repo = Mock()
            mock_symbol_repo = Mock()
            mock_data_fetcher = Mock()
            
            # Setup mock responses
            mock_prices = [Mock() for _ in range(100)]  # 100 price points
            mock_data_fetcher.fetch_daily_prices.return_value = mock_prices
            mock_price_repo.get_daily_prices.side_effect = Exception("Not in cache")
            
            data_service = DataService(
                price_repository=mock_price_repo,
                symbol_repository=mock_symbol_repo,
                data_fetcher=mock_data_fetcher
            )
            
            symbol = test_symbols[0]
            
            # Benchmark single fetch
            start_time = time.time()
            prices = data_service.get_daily_prices(symbol, days=90)
            end_time = time.time()
            
            duration = end_time - start_time
            performance_metrics.record_timing('single_symbol_fetch', duration)
            
            # Assertions
            assert len(prices) == 100
            assert duration < 1.0, f"Single symbol fetch too slow: {duration:.3f}s"
            
            logger.info(f"✅ Single symbol fetch: {duration:.3f}s")
            
        except ImportError:
            pytest.skip("Required components not available")
    
    @pytest.mark.performance
    def test_batch_symbol_fetch_performance(self, test_symbols, performance_metrics):
        """Test performance of fetching data for multiple symbols."""
        try:
            from src.application.services.data_service import DataService
            from unittest.mock import Mock
            
            # Create mock dependencies
            mock_price_repo = Mock()
            mock_symbol_repo = Mock()
            mock_data_fetcher = Mock()
            
            # Setup mock responses
            mock_prices = [Mock() for _ in range(100)]
            mock_data_fetcher.fetch_daily_prices.return_value = mock_prices
            mock_price_repo.get_daily_prices.side_effect = Exception("Not in cache")
            
            data_service = DataService(
                price_repository=mock_price_repo,
                symbol_repository=mock_symbol_repo,
                data_fetcher=mock_data_fetcher
            )
            
            # Benchmark batch fetch (sequential)
            start_time = time.time()
            for symbol in test_symbols:
                prices = data_service.get_daily_prices(symbol, days=90)
                performance_metrics.record_timing('batch_symbol_fetch_sequential', 
                                                time.time() - start_time)
            end_time = time.time()
            
            sequential_duration = end_time - start_time
            performance_metrics.record_timing('batch_total_sequential', sequential_duration)
            
            # Benchmark batch fetch (parallel)
            start_time = time.time()
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = {
                    executor.submit(data_service.get_daily_prices, symbol, 90): symbol
                    for symbol in test_symbols
                }
                
                for future in as_completed(futures):
                    symbol = futures[future]
                    prices = future.result()
                    assert len(prices) == 100
            
            end_time = time.time()
            parallel_duration = end_time - start_time
            performance_metrics.record_timing('batch_total_parallel', parallel_duration)
            
            # Assertions
            assert parallel_duration < sequential_duration, "Parallel should be faster"
            assert parallel_duration < 5.0, f"Batch fetch too slow: {parallel_duration:.3f}s"
            
            speedup = sequential_duration / parallel_duration
            logger.info(f"✅ Batch fetch speedup: {speedup:.2f}x")
            
        except ImportError:
            pytest.skip("Required components not available")
    
    @pytest.mark.performance
    def test_cache_performance(self, test_symbols, performance_metrics):
        """Test cache performance and hit rates."""
        try:
            from src.infrastructure.adapters.cache_service import CacheService
            import tempfile
            import os
            
            # Create temporary cache directory
            with tempfile.TemporaryDirectory() as temp_dir:
                cache_service = CacheService(cache_dir=temp_dir, default_ttl=3600)
                
                # Test cache write performance
                test_data = {"prices": [{"close": 100.0} for _ in range(1000)]}
                
                start_time = time.time()
                for i, symbol in enumerate(test_symbols):
                    cache_key = f"prices_{symbol}_90d"
                    cache_service.set(cache_key, test_data)
                end_time = time.time()
                
                write_duration = end_time - start_time
                performance_metrics.record_timing('cache_write_batch', write_duration)
                
                # Test cache read performance
                start_time = time.time()
                for symbol in test_symbols:
                    cache_key = f"prices_{symbol}_90d"
                    cached_data = cache_service.get(cache_key)
                    assert cached_data is not None
                end_time = time.time()
                
                read_duration = end_time - start_time
                performance_metrics.record_timing('cache_read_batch', read_duration)
                
                # Assertions
                assert write_duration < 1.0, f"Cache writes too slow: {write_duration:.3f}s"
                assert read_duration < 0.1, f"Cache reads too slow: {read_duration:.3f}s"
                
                logger.info(f"✅ Cache write: {write_duration:.3f}s, read: {read_duration:.3f}s")
                
        except ImportError:
            pytest.skip("Cache service not available")


class TestAnalysisPerformance:
    """Performance tests for stock analysis operations."""
    
    @pytest.fixture
    def performance_metrics(self):
        """Create performance metrics collector."""
        return PerformanceMetrics()
    
    @pytest.fixture
    def mock_price_data(self):
        """Create mock price data for testing."""
        from src.domain.entities.stock_models import PricePoint
        import random
        
        prices = []
        base_price = 100.0
        timestamp = 1640995200  # 2022-01-01
        
        for i in range(365):  # One year of data
            # Simulate price movement
            change = random.uniform(-0.05, 0.05)  # ±5% daily change
            base_price *= (1 + change)
            
            prices.append(PricePoint(
                timestamp=timestamp + (i * 86400),  # Daily increment
                open_price=base_price * random.uniform(0.98, 1.02),
                high_price=base_price * random.uniform(1.00, 1.05),
                low_price=base_price * random.uniform(0.95, 1.00),
                close_price=base_price,
                volume=random.randint(100000, 1000000)
            ))
        
        return prices
    
    @pytest.mark.performance
    def test_technical_indicator_calculation_performance(
        self, mock_price_data, performance_metrics
    ):
        """Test performance of technical indicator calculations."""
        try:
            from src.domain.services.technical_indicators import TechnicalIndicatorService
            
            indicator_service = TechnicalIndicatorService()
            symbol = "TEST"
            
            # Test individual indicator performance
            indicators_to_test = [
                ('RSI', lambda: indicator_service.calculate_rsi(symbol, mock_price_data)),
                ('MACD', lambda: indicator_service.calculate_macd(symbol, mock_price_data)),
                ('SMA', lambda: indicator_service.calculate_sma(symbol, mock_price_data, 20)),
                ('EMA', lambda: indicator_service.calculate_ema(symbol, mock_price_data, 20)),
                ('Bollinger', lambda: indicator_service.calculate_bollinger_bands(symbol, mock_price_data))
            ]
            
            for indicator_name, calc_func in indicators_to_test:
                start_time = time.time()
                result = calc_func()
                end_time = time.time()
                
                duration = end_time - start_time
                performance_metrics.record_timing(f'indicator_{indicator_name.lower()}', duration)
                
                # Assertions
                assert result is not None
                assert duration < 0.5, f"{indicator_name} calculation too slow: {duration:.3f}s"
                
                logger.info(f"✅ {indicator_name} calculation: {duration:.3f}s")
            
            # Test batch indicator calculation
            start_time = time.time()
            all_indicators = indicator_service.calculate_all_indicators(symbol, mock_price_data)
            end_time = time.time()
            
            batch_duration = end_time - start_time
            performance_metrics.record_timing('indicator_batch_all', batch_duration)
            
            # Assertions
            assert len(all_indicators) > 0
            assert batch_duration < 2.0, f"Batch indicators too slow: {batch_duration:.3f}s"
            
            logger.info(f"✅ All indicators calculation: {batch_duration:.3f}s")
            
        except ImportError:
            pytest.skip("Technical indicator service not available")
    
    @pytest.mark.performance
    def test_stock_analysis_end_to_end_performance(
        self, mock_price_data, performance_metrics
    ):
        """Test end-to-end stock analysis performance."""
        try:
            from src.application.services.analysis_service import AnalysisService
            from src.domain.entities.stock_models import AnalysisRequest
            from unittest.mock import Mock
            
            # Create mock dependencies
            mock_data_service = Mock()
            mock_indicator_processor = Mock()
            
            # Setup mock responses
            mock_data_service.get_daily_prices.return_value = mock_price_data
            mock_indicator_processor.calculate_all_indicators.return_value = {
                'rsi': [65.0], 'macd': [1.5], 'sma20': [100.0]
            }
            
            analysis_service = AnalysisService(
                data_service=mock_data_service,
                indicator_processor=mock_indicator_processor
            )
            
            # Test single analysis performance
            request = AnalysisRequest(
                symbol="TEST",
                days_back=365,
                include_zones=True,
                include_risk_analysis=True
            )
            
            start_time = time.time()
            analysis = analysis_service.analyze_stock(request)
            end_time = time.time()
            
            duration = end_time - start_time
            performance_metrics.record_timing('analysis_end_to_end', duration)
            
            # Assertions
            assert analysis is not None
            assert duration < 3.0, f"End-to-end analysis too slow: {duration:.3f}s"
            
            logger.info(f"✅ End-to-end analysis: {duration:.3f}s")
            
        except ImportError:
            pytest.skip("Analysis service not available")
    
    @pytest.mark.performance
    def test_memory_usage_analysis(self, mock_price_data, performance_metrics):
        """Test memory usage during analysis operations."""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            
            # Measure initial memory
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Simulate heavy analysis workload
            from src.domain.services.technical_indicators import TechnicalIndicatorService
            
            indicator_service = TechnicalIndicatorService()
            
            # Process multiple symbols worth of data
            for i in range(10):  # Simulate 10 symbols
                symbol = f"TEST{i}"
                
                # Calculate all indicators
                indicators = indicator_service.calculate_all_indicators(symbol, mock_price_data)
                
                # Measure memory after each symbol
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory
                
                performance_metrics.record_timing('memory_usage_mb', memory_increase)
            
            # Final memory measurement
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            total_memory_increase = final_memory - initial_memory
            
            # Assertions
            assert total_memory_increase < 100, f"Memory usage too high: {total_memory_increase:.1f}MB"
            
            logger.info(f"✅ Memory usage increase: {total_memory_increase:.1f}MB")
            
        except ImportError:
            pytest.skip("psutil not available for memory testing")
    
    def teardown_method(self, method):
        """Clean up after each test method."""
        # Print performance summary if metrics were collected
        if hasattr(self, 'performance_metrics'):
            self.performance_metrics.print_summary()
