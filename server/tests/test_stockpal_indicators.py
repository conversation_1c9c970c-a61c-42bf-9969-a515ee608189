"""
Unit tests for StockPal indicators with clean architecture.

Tests all technical indicators including RSI, Bollinger Bands, Ichimoku,
Moving Averages, and the 52-week RS indicator.
"""

import unittest
from datetime import datetime, timedelta
from typing import List

from stockpal.core.stock import PriceData
from stockpal.indicator.rsi import RelativeStrengthIndex
from stockpal.indicator.bb import BollingerBands
from stockpal.indicator.ichimoku import Ichimoku
from stockpal.indicator.ma import MovingAverage
from stockpal.indicator.rs import RelativeStrength


class TestStockPalIndicators(unittest.TestCase):
    """Test suite for StockPal technical indicators."""

    def setUp(self):
        """Set up test data."""
        # Create sample price data for 400 days (more than 52 weeks)
        self.prices = []
        self.benchmark_prices = []
        base_date = datetime.now() - timedelta(days=400)

        for i in range(400):
            date = base_date + timedelta(days=i)
            # Stock price with some volatility
            base_price = 100 + (i * 0.1) + (i % 10 - 5) * 2

            price = PriceData(
                symbol="TEST",
                timestamp=int(date.timestamp()),
                open_price=base_price - 1,
                highest_price=base_price + 2,
                lowest_price=base_price - 2,
                close_price=base_price,
                match_volume=1000000 + (i * 1000),
                change_price=0.1 if i > 0 else 0.0,
                change_price_percent=0.1 if i > 0 else 0.0
            )
            self.prices.append(price)

            # Benchmark price (less volatile)
            benchmark_price = 1000 + (i * 0.05) + (i % 5 - 2) * 1
            benchmark = PriceData(
                symbol="BENCHMARK",
                timestamp=int(date.timestamp()),
                open_price=benchmark_price - 0.5,
                highest_price=benchmark_price + 1,
                lowest_price=benchmark_price - 1,
                close_price=benchmark_price,
                match_volume=10000000,
                change_price=0.05 if i > 0 else 0.0,
                change_price_percent=0.05 if i > 0 else 0.0
            )
            self.benchmark_prices.append(benchmark)

    def test_rsi_calculation(self):
        """Test RSI indicator calculation."""
        rsi = RelativeStrengthIndex("TEST", self.prices, period=14)
        values = rsi.calculate()

        self.assertIsInstance(values, list)
        self.assertEqual(len(values), len(self.prices))

        # Check that RSI values are in valid range (0-100)
        valid_values = [v for v in values if v is not None]
        self.assertTrue(all(0 <= v <= 100 for v in valid_values))

        # Check that we have enough valid values
        self.assertGreater(len(valid_values), 300)

    def test_bollinger_bands_calculation(self):
        """Test Bollinger Bands indicator calculation."""
        bb = BollingerBands("TEST", self.prices, period=20, std_dev=2)
        result = bb.calculate()

        self.assertIsInstance(result, dict)
        self.assertIn("upper", result)
        self.assertIn("middle", result)
        self.assertIn("lower", result)

        upper = result["upper"]
        middle = result["middle"]
        lower = result["lower"]

        # Check lengths
        self.assertEqual(len(upper), len(self.prices))
        self.assertEqual(len(middle), len(self.prices))
        self.assertEqual(len(lower), len(self.prices))

        # Check that upper > middle > lower for valid values
        for i in range(len(upper)):
            if all(v is not None for v in [upper[i], middle[i], lower[i]]):
                self.assertGreater(upper[i], middle[i])
                self.assertGreater(middle[i], lower[i])

    def test_ichimoku_calculation(self):
        """Test Ichimoku indicator calculation."""
        ichimoku = Ichimoku("TEST", self.prices)
        result = ichimoku.calculate()

        self.assertIsInstance(result, dict)
        expected_keys = ["tenkan_sen", "kijun_sen", "senkou_span_a",
                        "senkou_span_b", "chikou_span"]

        for key in expected_keys:
            self.assertIn(key, result)
            self.assertEqual(len(result[key]), len(self.prices))

    def test_moving_average_calculation(self):
        """Test Moving Average indicator calculation."""
        ma = MovingAverage("TEST", self.prices, period=20, ma_type="SMA")
        values = ma.calculate()

        self.assertIsInstance(values, list)
        self.assertEqual(len(values), len(self.prices))

        # Check that we have valid moving average values
        valid_values = [v for v in values if v is not None]
        self.assertGreater(len(valid_values), 350)

        # Test EMA as well
        ema = MovingAverage("TEST", self.prices, period=20, ma_type="EMA")
        ema_values = ema.calculate()

        self.assertIsInstance(ema_values, list)
        self.assertEqual(len(ema_values), len(self.prices))

    def test_rs_52_week_calculation(self):
        """Test 52-week Relative Strength indicator calculation."""
        rs = RelativeStrength("TEST", self.prices, self.benchmark_prices, period=52)
        values = rs.calculate()

        self.assertIsInstance(values, list)
        self.assertEqual(len(values), len(self.prices))

        # Check that RS values are reasonable (typically between 0.5 and 2.0)
        valid_values = [v for v in values if v is not None]
        self.assertGreater(len(valid_values), 50)  # Should have some valid values

        # Most RS values should be positive
        self.assertTrue(all(v > 0 for v in valid_values))

        # Verify 52-week period is being used (365 trading days)
        self.assertEqual(rs.trading_days_period, 365)

    def test_rs_trend_prediction(self):
        """Test RS indicator trend prediction."""
        rs = RelativeStrength("TEST", self.prices, self.benchmark_prices, period=52)
        trend = rs.predict_trend()

        self.assertIsInstance(trend, dict)
        self.assertIn("trend", trend)
        self.assertIn("confidence", trend)

        # Trend should be one of the expected values
        self.assertIn(trend["trend"], ["outperforming", "underperforming", "neutral"])

        # Confidence should be between 0 and 1
        self.assertGreaterEqual(trend["confidence"], 0.0)
        self.assertLessEqual(trend["confidence"], 1.0)

    def test_rs_recommendation(self):
        """Test RS indicator recommendation."""
        rs = RelativeStrength("TEST", self.prices, self.benchmark_prices, period=52)
        recommendation = rs.get_recommendation()

        self.assertIsInstance(recommendation, str)
        self.assertIn(recommendation, ["Mua mạnh", "Mua", "Giữ", "Bán", "Bán mạnh"])

    def test_insufficient_data_handling(self):
        """Test indicator behavior with insufficient data."""
        # Test with only 10 days of data
        short_prices = self.prices[:10]
        short_benchmark = self.benchmark_prices[:10]

        # RSI with insufficient data
        rsi = RelativeStrengthIndex("TEST", short_prices, period=14)
        rsi_values = rsi.calculate()
        self.assertEqual(len(rsi_values), 10)
        # RSI returns default values (50.0) when insufficient data
        self.assertTrue(all(abs(v - 50.0) < 0.01 for v in rsi_values))

        # RS with insufficient data
        rs = RelativeStrength("TEST", short_prices, short_benchmark, period=52)
        rs_values = rs.calculate()
        self.assertEqual(len(rs_values), 0)  # Should return empty list

    def test_indicator_edge_cases(self):
        """Test indicators with edge cases."""
        # Test with constant prices
        constant_prices = []
        base_date = datetime.now() - timedelta(days=100)

        for i in range(100):
            date = base_date + timedelta(days=i)
            price = PriceData(
                symbol="TEST",
                timestamp=int(date.timestamp()),
                open_price=100.0,
                highest_price=100.0,
                lowest_price=100.0,
                close_price=100.0,
                match_volume=1000000,
                change_price=0.0,
                change_price_percent=0.0
            )
            constant_prices.append(price)

        # RSI with constant prices should be around 50
        rsi = RelativeStrengthIndex("TEST", constant_prices, period=14)
        rsi_values = rsi.calculate()
        valid_rsi = [v for v in rsi_values if v is not None]

        if valid_rsi:
            # With constant prices, RSI should be around 50
            self.assertTrue(all(45 <= v <= 55 for v in valid_rsi))

    def test_performance_with_large_dataset(self):
        """Test indicator performance with large dataset."""
        # Create larger dataset (2 years of data)
        large_prices = []
        base_date = datetime.now() - timedelta(days=730)

        for i in range(730):
            date = base_date + timedelta(days=i)
            base_price = 100 + (i * 0.1) + (i % 20 - 10) * 3

            price = PriceData(
                symbol="TEST",
                timestamp=int(date.timestamp()),
                open_price=base_price - 1,
                highest_price=base_price + 2,
                lowest_price=base_price - 2,
                close_price=base_price,
                match_volume=1000000,
                change_price=0.1,
                change_price_percent=0.1
            )
            large_prices.append(price)

        # Test RSI performance
        import time
        start_time = time.time()
        rsi = RelativeStrengthIndex("TEST", large_prices, period=14)
        rsi_values = rsi.calculate()
        rsi_time = time.time() - start_time

        self.assertLess(rsi_time, 1.0)  # Should complete within 1 second
        self.assertEqual(len(rsi_values), 730)

        # Test Bollinger Bands performance
        start_time = time.time()
        bb = BollingerBands("TEST", large_prices, period=20)
        bb_result = bb.calculate()
        bb_time = time.time() - start_time

        self.assertLess(bb_time, 1.0)  # Should complete within 1 second
        self.assertEqual(len(bb_result["upper"]), 730)


if __name__ == "__main__":
    unittest.main()
