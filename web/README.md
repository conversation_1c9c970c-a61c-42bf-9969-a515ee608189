# StockPal Web (Frontend)

This directory contains the frontend (web) code for the StockPal project.

## Structure

- `index.html` - Main HTML file
- `script.js` - Main JavaScript file
- `styles.css` - Main CSS file
- `assets/` - Images, logos, and other static assets
- Other HTML/JS/CSS files as needed

## Setup & Usage

1. Open `index.html` in your web browser to use the frontend interface.
2. Ensure the backend server is running (see `../server/README.md`) if the frontend requires API access.

## Development

- Edit HTML, JS, and CSS files as needed.
- Place new images or static files in the `assets/` directory.

## Notes

- All frontend code and assets should reside in this sub-project.
- See the root `README.md` for overall project context. 