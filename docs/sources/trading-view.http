###

# Get list symbols

POST https://scanner.tradingview.com/vietnam/scan?label-product=screener-stock
accept: application/json
accept-language: en-US,en;q=0.9,vi;q=0.8
content-type: text/plain;charset=UTF-8
origin: https://vn.tradingview.com
priority: u=1, i
referer: https://vn.tradingview.com/
user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
  
{"columns":["name","description","logoid","industry.tr","industry","sector.tr","sector","exchange.tr","exchange","indexes.tr","indexes","market","recommendation_mark","price_target_1y","price_target_1y_delta"],"filter":[{"left":"is_primary","operation":"equal","right":true}],"ignore_unknown_fields":false,"options":{"lang":"vi"},"range":[0,2000],"sort":{"sortBy":"market_cap_basic","sortOrder":"desc"},"symbols":{},"markets":["vietnam"],"filter2":{"operator":"and","operands":[{"operation":{"operator":"or","operands":[{"operation":{"operator":"and","operands":[{"expression":{"left":"type","operation":"equal","right":"stock"}},{"expression":{"left":"typespecs","operation":"has","right":["common"]}}]}},{"operation":{"operator":"and","operands":[{"expression":{"left":"type","operation":"equal","right":"stock"}},{"expression":{"left":"typespecs","operation":"has","right":["preferred"]}}]}},{"operation":{"operator":"and","operands":[{"expression":{"left":"type","operation":"equal","right":"dr"}}]}},{"operation":{"operator":"and","operands":[{"expression":{"left":"type","operation":"equal","right":"fund"}},{"expression":{"left":"typespecs","operation":"has_none_of","right":["etf"]}}]}}]}}]}}

###

# Company logo
GET https://s3-symbol-logo.tradingview.com/joint-stock-commercial-bank-for-foreign-trade-of-vietnam--big.svg
