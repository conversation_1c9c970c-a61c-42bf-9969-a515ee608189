{"columns": ["name", "description", "logoid", "update_mode", "type", "typespecs", "close", "pricescale", "<PERSON><PERSON>v", "fractional", "minmove2", "currency", "change", "volume", "relative_volume_10d_calc", "market_cap_basic", "fundamental_currency_code", "price_earnings_ttm", "earnings_per_share_diluted_ttm", "earnings_per_share_diluted_yoy_growth_ttm", "dividends_yield_current", "sector.tr", "market", "sector", "recommendation_mark", "industry.tr", "indexes.tr", "exchange.tr", "source-logoid", "price_target_1y", "price_target_1y_delta", "exchange"], "filter": [{"left": "is_primary", "operation": "equal", "right": true}], "ignore_unknown_fields": false, "options": {"lang": "vi"}, "range": [0, 300], "sort": {"sortBy": "market_cap_basic", "sortOrder": "desc"}, "symbols": {}, "markets": ["vietnam"], "filter2": {"operator": "and", "operands": [{"operation": {"operator": "or", "operands": [{"operation": {"operator": "and", "operands": [{"expression": {"left": "type", "operation": "equal", "right": "stock"}}, {"expression": {"left": "typespecs", "operation": "has", "right": ["common"]}}]}}, {"operation": {"operator": "and", "operands": [{"expression": {"left": "type", "operation": "equal", "right": "stock"}}, {"expression": {"left": "typespecs", "operation": "has", "right": ["preferred"]}}]}}, {"operation": {"operator": "and", "operands": [{"expression": {"left": "type", "operation": "equal", "right": "dr"}}]}}, {"operation": {"operator": "and", "operands": [{"expression": {"left": "type", "operation": "equal", "right": "fund"}}, {"expression": {"left": "typespecs", "operation": "has_none_of", "right": ["etf"]}}]}}]}}]}}