###

# Variables
@cookie_request_verification_token = _bCVTmE4h8mpaoaAiYX0sliQC_hokcjoZJ09b4cjdOjwsYTlEy2dAlv0EYF1Cdm_HhZKwT86JjNAqMr1FLQGBpwTME_wne7L2IwiJ69ukHY1
@payload_request_verification_token = iTXp7SrEo4vdtEPecM_Ur9MSRMwnKqni3GNHhZhwVq-NKLzbsBpgH6WcBS0M0rUpGjMXwzAIuiotPch1rB0CQAA6L5S-lhN32HSgMYRT9so1
@login_payload_request_verification_token = 7z9CfyU6fl4macYAmmsSKcFA2bifyCO_6deTrcilhneH0-Y1tz0dcNZPVV7MAbZgMXMzo95IbFvZWfhhyf8wOtkWfi2aQ5C9-hXYEhE_y0VtIVrNdnEZ7Zl3vHNU99LjxdwqpkUUQ4Jsq9lj8C_JHg2

###

# Get prices by chart
GET https://api.vietstock.vn/tvnew/history
    ?symbol=HPG
    &resolution=1D
    &from=1713916800
    &to=1745452800
Accept: */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: en-US,en;q=0.9,vi;q=0.8
Host: api.vietstock.vn
Origin: https://stockchart.vietstock.vn
Referer: https://stockchart.vietstock.vn/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

###

# Get trading result
POST https://finance.vietstock.vn/data/gettradingresult
Accept: */*
Accept-Encoding: gzip
Accept-Language: en-US,en;q=0.9,vi;q=0.8
Connection: keep-alive
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
Host: finance.vietstock.vn
Origin: https://finance.vietstock.vn
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
Cookie: __RequestVerificationToken={{cookie_request_verification_token}}


Code=HPG
&OrderBy=
&OrderDirection=desc
&PageIndex=1
&PageSize=10
&FromDate=2025-01-27
&ToDate=2025-05-03
&ExportType=default
&Cols=
TKLGD,TGTGD,VHTT,TGG,DC,TGPTG,KLGDKL,GTGDKL,Room,RoomCL,RoomCLPT,KL_M_GDKL,GT_M_GDKL,KL_B_GDKL,GT_B_GDKL,KL_M_GDTT,GT_M_GDTT,KL_B_GDTT,GT_B_GDTT,KL_M_TKL,GT_M_TGT,KL_B_TKL,GT_B_TGT
&ExchangeID=1
&__RequestVerificationToken={{payload_request_verification_token}}

###

# Get org list
POST https://finance.vietstock.vn/data/corporateaz
Accept: */*
Accept-Encoding: gzip
Accept-Language: en-US,en;q=0.9,vi;q=0.8
Connection: keep-alive
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
Host: finance.vietstock.vn
Origin: https://finance.vietstock.vn
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
Cookie: Theme=Light; AnonymousNotification=; language=vi-VN; ASP.NET_SessionId=5g1jxkyc3y1vsbudsamzmxyw; __RequestVerificationToken=_bCVTmE4h8mpaoaAiYX0sliQC_hokcjoZJ09b4cjdOjwsYTlEy2dAlv0EYF1Cdm_HhZKwT86JjNAqMr1FLQGBpwTME_wne7L2IwiJ69ukHY1; CookieLogin=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fxrf2fuziVFMqOqhl_Gmj0bLjK6Ga3TpZOKQCCuuIu0; vst_usr_lg_token=Ayf1357YIEuS/53CXnJlLA==

catID=0
&industryID=0
&page=2
&pageSize=50
&type=0
&code=
&businessTypeID=0
&orderBy=Code
&orderDir=ASC
&__RequestVerificationToken={{login_payload_request_verification_token}}


###

# Get org list (page 2)
POST https://finance.vietstock.vn/data/corporateaz
Accept: */*
Accept-Language: en-US,en;q=0.9,vi;q=0.8
Connection: keep-alive
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
DNT: 1
Origin: https://finance.vietstock.vn
Referer: https://finance.vietstock.vn/doanh-nghiep-a-z?page=1
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Cookie: Theme=Light; AnonymousNotification=; language=vi-VN; ASP.NET_SessionId=5g1jxkyc3y1vsbudsamzmxyw; __RequestVerificationToken=_bCVTmE4h8mpaoaAiYX0sliQC_hokcjoZJ09b4cjdOjwsYTlEy2dAlv0EYF1Cdm_HhZKwT86JjNAqMr1FLQGBpwTME_wne7L2IwiJ69ukHY1; CookieLogin=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fxrf2fuziVFMqOqhl_Gmj0bLjK6Ga3TpZOKQCCuuIu0; vst_usr_lg_token=Ayf1357YIEuS/53CXnJlLA==

catID=0
&industryID=0
&page=1
&pageSize=20
&type=0
&code=
&businessTypeID=0
&orderBy=Code
&orderDir=ASC
&__RequestVerificationToken=7z9CfyU6fl4macYAmmsSKcFA2bifyCO_6deTrcilhneH0-Y1tz0dcNZPVV7MAbZgMXMzo95IbFvZWfhhyf8wOtkWfi2aQ5C9-hXYEhE_y0VtIVrNdnEZ7Zl3vHNU99LjxdwqpkUUQ4Jsq9lj8C_JHg2
