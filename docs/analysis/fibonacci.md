# Phân Tích Fibonacci Retracement

## Tổng Quan
Fi<PERSON>acci retracement là một phương pháp phân tích kỹ thuật sử dụng các đường ngang để xác định các mức hỗ trợ hoặc kháng cự tiềm năng, nơi giá có thể đảo chiều. StockPal sử dụng khung thời gian 30 chu kỳ để tính toán các mức này.

## C<PERSON><PERSON> Mức Fibonacci Quan Trọng

### Tỷ <PERSON><PERSON>ẩn
- 0.236 (23.6%): Mức điều chỉnh yếu
  - Thường được sử dụng trong xu hướng mạnh
  - <PERSON><PERSON> hợp cho giao dịch theo xu hướng
  - Điểm vào lệnh tốt khi có xác nhận từ các chỉ báo khác

- 0.382 (38.2%): <PERSON><PERSON><PERSON> điều chỉnh trung bình
  - <PERSON><PERSON><PERSON> quan trọng cho các nhà giao dịch ngắn hạn
  - Thường có phản ứng giá mạnh
  - <PERSON><PERSON><PERSON> hợp tốt với các mức hỗ trợ/kháng cự

- 0.500 (50.0%): Mức điều chỉnh giữa
  - Mức tâm lý quan trọng
  - Thường có khối lượng giao dịch lớn
  - Điểm đảo chiều tiềm năng mạnh

- 0.618 (61.8%): Mức điều chỉnh mạnh
  - Mức Fibonacci quan trọng nhất
  - Thường là điểm đảo chiều chính
  - Tỷ lệ risk/reward tốt nhất cho giao dịch

## Ứng Dụng

### Hỗ Trợ/Kháng Cự
- Giá thường phản ứng tại các mức Fibonacci
  - Quan sát phản ứng giá và khối lượng
  - Chờ đợi nến xác nhận trước khi vào lệnh
- Kết hợp nhiều khung thời gian
  - Xác nhận mức hỗ trợ/kháng cự trên nhiều timeframe
  - Tăng độ tin cậy của tín hiệu
- Kết hợp với các mức kỹ thuật khác
  - Đường trung bình động
  - Các mức Pivot
  - Các mẫu hình nến
- Vùng giá động
  - Sử dụng dải giá thay vì mức giá cụ thể
  - Tăng tỷ lệ thành công của giao dịch

### Vùng Đảo Chiều Xu Hướng
- Xác định điểm đảo chiều quan trọng
  - Kết hợp với các mẫu hình đảo chiều
  - Quan sát khối lượng tại các mức
- Xác nhận xu hướng tiếp diễn
  - Phá vỡ các mức Fibonacci
  - Động lực giá và khối lượng
- Thiết lập giao dịch ngược xu hướng
  - Chờ đợi xác nhận đảo chiều
  - Quản lý rủi ro chặt chẽ
- Các mức quản lý rủi ro
  - Đặt stop loss dưới/trên mức Fibonacci gần nhất
  - Tỷ lệ risk/reward tối thiểu 1:2

## Sử Dụng trong StockPal

### Phân Tích Xu Hướng
- Theo dõi các mức điều chỉnh
  - Cảnh báo khi giá tiếp cận mức Fibonacci
  - Thông báo khi có phản ứng giá
- Xác nhận hỗ trợ/kháng cự
  - Kết hợp với các chỉ báo kỹ thuật khác
  - Xác định độ mạnh của mức
- Tạo mục tiêu giá
  - Sử dụng các mức Fibonacci cao hơn làm mục tiêu
  - Điều chỉnh theo biến động thị trường
- Đặt stop loss
  - Dựa trên mức Fibonacci gần nhất
  - Tính toán khoảng cách an toàn

### Ứng Dụng Giao Dịch
1. Xác định điểm vào lệnh
   - Chờ đợi phản ứng giá tại mức Fibonacci
   - Xác nhận bằng các chỉ báo phụ trợ
   - Quan sát khối lượng giao dịch
2. Thiết lập mục tiêu lợi nhuận
   - Sử dụng mức Fibonacci tiếp theo
   - Điều chỉnh theo điều kiện thị trường
3. Đặt stop loss
   - Dưới/trên mức Fibonacci gần nhất
   - Tính toán % rủi ro chấp nhận được
4. Tính toán tỷ lệ risk/reward
   - Tối thiểu 1:2
   - Điều chỉnh theo biến động thị trường

## Các Nguyên Tắc Thực Hành
1. Sử dụng theo chiều xu hướng
   - Xác định xu hướng chính trước
   - Giao dịch theo chiều xu hướng
2. Kết hợp với các chỉ báo khác
   - RSI, MACD, Bollinger Bands
   - Xác nhận tín hiệu đa chiều
3. Xem xét bối cảnh thị trường
   - Tin tức và sự kiện quan trọng
   - Điều kiện thị trường tổng thể
4. Theo dõi phản ứng tại các mức
   - Quan sát hành vi giá
   - Chờ đợi xác nhận
5. Theo dõi khối lượng tại các mức
   - Khối lượng tăng = xác nhận mạnh
   - Khối lượng giảm = tín hiệu yếu