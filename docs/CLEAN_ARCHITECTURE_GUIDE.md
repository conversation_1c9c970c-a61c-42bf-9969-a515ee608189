# StockPal Clean Architecture Guide

## Overview

StockPal has been completely migrated to a clean architecture pattern that provides clear separation of concerns, improved maintainability, and enhanced testability. This guide covers the complete architecture, API usage, and development practices.

## Architecture Layers

### 1. Domain Layer (`stockpal/`)
The core business logic and entities.

```
stockpal/
├── core/
│   ├── stock.py              # Core stock data models
│   └── http_service.py       # HTTP service utilities
├── indicator/
│   ├── base_indicator.py     # Base indicator interface
│   ├── rsi.py               # RSI indicator (52-week calculation)
│   ├── bollinger_bands.py   # Bollinger Bands
│   ├── ichimoku.py          # Ichimoku Cloud
│   ├── moving_average.py    # Moving Averages (SMA, EMA)
│   ├── rs.py                # Relative Strength (52-week)
│   ├── pivot_points.py      # Pivot Points
│   ├── trend_predictor.py   # Trend Prediction
│   └── signal_synthesizer.py # Signal Synthesis
└── scraper/
    └── vietstockscraper.py  # VietStock data scraper
```

### 2. Application Layer (`application/`)
Use cases and application services.

```
application/
├── commands/
│   ├── fetch_data_command.py    # Data fetching commands
│   └── analyze_stock_command.py # Analysis commands
└── use_cases/
    └── batch_analysis_use_case.py # Batch processing
```

### 3. Infrastructure Layer (`infrastructure/`)
External concerns and data access.

```
infrastructure/
├── repositories/
│   ├── stock_repository.py      # Stock data repository
│   ├── price_repository.py      # Price data repository
│   └── symbol_repository.py     # Symbol repository
├── external/
│   ├── ssi_fetcher.py          # SSI data provider
│   ├── vietstock_fetcher.py    # VietStock data provider
│   ├── cafef_fetcher.py        # CafeF data provider
│   └── data_fetcher.py         # Unified data fetcher
└── cache/
    ├── local_cache.py          # Local caching implementation
    ├── cache_service.py        # Cache service
    └── cache_manager.py        # Cache management
```

### 4. Core Services (`core/`)
Business logic and domain services.

```
core/
├── services/
│   ├── data_service.py         # Data management service
│   ├── analysis_service.py     # Stock analysis service
│   ├── export_service.py       # Data export service
│   ├── signal_synthesizer.py   # Signal synthesis service
│   └── performance_monitor.py  # Performance monitoring
├── processors/
│   └── indicator_processor.py  # Technical indicator processing
└── analytics/
    ├── ml_analytics_service.py # ML-based analytics
    └── backtesting_service.py  # Strategy backtesting
```

## Key Features

### 52-Week Relative Strength (RS) Indicator
The RS indicator has been specifically configured to use 52 weeks (approximately 365 trading days) for calculation:

```python
from stockpal.indicator.rs import RelativeStrength

# RS calculation uses 52 weeks by default
rs = RelativeStrength("VIC", stock_prices, benchmark_prices, period=52)
values = rs.calculate()  # Uses 365 trading days for 52-week calculation
```

### Performance Monitoring
Comprehensive performance monitoring across all operations:

```python
from core.services.performance_monitor import performance_monitor

# Monitor data fetching
operation_id = performance_monitor.start_operation("data_fetch", "SSI", "VIC")
try:
    # Perform operation
    data = fetch_data()
    performance_monitor.end_operation(operation_id, success=True)
except Exception as e:
    performance_monitor.end_operation(operation_id, success=False, error_message=str(e))
```

### Local Caching (No Redis)
Efficient local caching system without external dependencies:

```python
from infrastructure.cache.local_cache import LocalCache

cache = LocalCache()
cache.set("key", data, ttl=3600)  # Cache for 1 hour
cached_data = cache.get("key")
```

### Enhanced Provider Selection
Support for multiple data providers with automatic fallback:

```python
# Providers: SSI, VietStock, CafeF
data_service.get_stock_prices("VIC", provider="SSI")  # Primary
# Automatically falls back to VietStock or CafeF if SSI fails
```

## API Usage

### Basic Stock Analysis

```python
from refactored_main import StockPalApplication

app = StockPalApplication()

# Fetch stock data
success = app.fetch_stock_data("VIC", days=365)

# Analyze stock
analysis = app.analyze_stock("VIC", days_back=365, include_zones=True)

# Get quick recommendation
recommendation = app.get_quick_recommendation("VIC")

# Compare multiple stocks
comparison = app.compare_stocks(["VIC", "VHM", "HPG"])
```

### ML-Based Predictions

```python
# Get ML prediction
prediction = app.get_ml_prediction("VIC", horizon_days=5)

# Run backtest
backtest_result = app.run_backtest("VIC", strategy_params={
    "stop_loss": 0.05,
    "take_profit": 0.10
})
```

### Batch Processing

```python
# Analyze multiple symbols
batch_results = app.run_batch_analysis(
    symbols=["VIC", "VHM", "HPG", "TCB", "BID"],
    export_results=True
)
```

## Technical Indicators

### Consolidated Technical Analysis
All indicators are consolidated into a unified analysis:

- **RSI**: Relative Strength Index
- **Bollinger Bands**: Price volatility bands
- **Ichimoku Cloud**: Comprehensive trend analysis
- **Moving Averages**: SMA and EMA
- **RS (52W)**: 52-week Relative Strength vs benchmark
- **Pivot Points**: Support and resistance levels

### Signal Synthesis
Intelligent signal synthesis combining multiple indicators:

```python
from core.services.signal_synthesizer import SignalSynthesizer

synthesizer = SignalSynthesizer()
signals = synthesizer.synthesize_signals(
    symbol="VIC",
    prices=price_data,
    include_zones=True,
    include_risk_analysis=True
)
```

## Testing

### Running Tests

```bash
# Run all tests
python server/run_tests.py

# Run specific test suites
python -m unittest tests.test_stockpal_indicators
python -m unittest tests.test_data_provider_integration
```

### Test Coverage
- **Unit Tests**: All technical indicators and core components
- **Integration Tests**: Data provider integration and architecture layers
- **Performance Tests**: Response time and throughput validation
- **Architecture Tests**: Clean architecture compliance validation

## Performance Optimization

### Caching Strategy
- Local file-based caching for price data
- Configurable TTL for different data types
- Automatic cache cleanup and management

### Logging
Comprehensive logging with separate log files:
- `/server/db/logs/performance/` - Performance metrics
- `/server/db/logs/` - General application logs
- Per-indicator, per-symbol logging for detailed analysis

### PEP 8 Compliance
All code follows PEP 8 standards:
- Standard library imports first
- Third-party imports second
- Local imports last
- Blank line separation between import groups
- Alphabetical ordering within groups

## Development Guidelines

### Adding New Indicators
1. Inherit from `BaseIndicator`
2. Implement `calculate()` method
3. Add trend prediction and recommendation methods
4. Include comprehensive docstrings
5. Add unit tests

### Adding New Data Providers
1. Implement data fetcher interface
2. Add to provider selection in repositories
3. Include error handling and fallback logic
4. Add integration tests
5. Update performance monitoring

### Code Quality
- Follow clean architecture principles
- Maintain clear separation of concerns
- Use dependency injection
- Write comprehensive tests
- Include detailed documentation

## Migration Status

### ✅ Completed
- Clean architecture implementation
- Legacy code removal (`legacy_compatibility.py`, `stock_analyzer.py`)
- 52-week RS indicator implementation
- Performance monitoring system
- Comprehensive testing suite
- Local caching system
- Enhanced provider selection
- PEP 8 compliance
- Documentation consolidation

### 🔄 Ongoing
- Performance optimization
- ML model improvements
- Additional technical indicators
- Enhanced backtesting strategies

## Support

For questions or issues:
1. Check the test suite for examples
2. Review the architecture documentation
3. Examine the performance monitoring logs
4. Refer to the comprehensive API documentation

The clean architecture ensures maintainable, testable, and scalable code that can easily accommodate future enhancements and requirements.
