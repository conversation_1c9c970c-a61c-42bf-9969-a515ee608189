# StockPal Server (Backend)

This directory contains the backend (server) code for the StockPal project.

## Structure

- `stockpal/` - Main backend Python package (core logic, indicators, data, db, etc.)
- `db/` - SQLite database(s)
- `cache/` - Data cache
- `main.py` - Backend entry point (API server, CLI, etc.)
- `.venv/` - Python virtual environment for backend
- `requirements.txt` - Backend dependencies

## Setup

1. **Create and activate the virtual environment** (if not already present):
   ```sh
   python -m venv .venv
   .venv\Scripts\activate  # On Windows
   source .venv/bin/activate  # On Unix/Mac
   ```
2. **Install dependencies:**
   ```sh
   pip install -r requirements.txt
   ```

## Usage

- Run backend services, scripts, or API server as needed:
  ```sh
  python main.py
  ```

## Notes

- All backend logic, data access, and analysis code should reside in this sub-project.
- See the root `README.md` for overall project context.
