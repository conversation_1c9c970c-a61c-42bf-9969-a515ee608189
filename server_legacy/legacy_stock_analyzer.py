from datetime import datetime
import json
import logging
import os
import shutil
import subprocess
from typing import Any, Dict, List, Optional
import argparse
import re
import numpy as np

from stockpal.core.constants import Constants
from stockpal.data import DataReader
from stockpal.core.sql_service import SqliteService
from stockpal.core.stock import PriceData
from stockpal.db import SymbolDao
from stockpal.indicator.signal_synthesizer import SignalSynthesizer
from stockpal.core.util import Utils
from stockpal.indicator.ma import MovingAverage
from stockpal.indicator.macd import MACD
from stockpal.indicator.rsi import RelativeStrengthIndex
from stockpal.indicator.bb import BollingerBands
from stockpal.indicator.stoch import StochasticOscillator
from stockpal.indicator.cci import CommodityChannelIndex
from stockpal.indicator.adx import AverageDirectionalIndex
from stockpal.indicator.wpr import WilliamsPercentR
from stockpal.indicator.roc import RateOfChange
from stockpal.indicator.ultosc import UltimateOscillator
from stockpal.indicator.sar import ParabolicSAR
from stockpal.indicator.stochrsi import StochasticRSI
from stockpal.indicator.adi import AccumulationDistributionIndex
from stockpal.indicator.ao import AwesomeOscillator
from stockpal.indicator.momentum import Momentum
from stockpal.indicator.bearpower import BearPower
from stockpal.indicator.obv import OnBalanceVolume
from stockpal.indicator.rs import RelativeStrength
from stockpal.indicator.pivot_points import PivotPoints

# Configure logging for the module
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)

# Get web directory
web_dir = Utils.get_web_dir()
logging.debug(f"web_dir: {web_dir}")
web2_dir = Utils.get_web2_dir()
logging.debug(f"web2_dir: {web2_dir}")
# Get bidaskprice.github.io directory
bidaskprice_dir = Utils.get_bidaskprice_dir()
logging.debug(f"bidaskprice_dir: {bidaskprice_dir}")


def copy_site_files_to_repo(
    src_dir: str, dest_dir: str, prediction_js_file: str
) -> None:
    """
    Copy generated site files (index.html, script.js, styles.css, prediction JS) from src_dir to dest_dir.

    Args:
        src_dir (str): Source directory containing generated files.
        dest_dir (str): Destination directory (repo) to copy files to.
        prediction_js_file (str): Path to the generated prediction JS file.
    """
    files_to_copy = [
        os.path.join(src_dir, "index.html"),
        os.path.join(src_dir, "script.js"),
        os.path.join(src_dir, "styles.css"),
        prediction_js_file,
    ]
    if not os.path.exists(dest_dir):
        try:
            os.makedirs(dest_dir, exist_ok=True)
            logging.info(f"Created destination directory: {dest_dir}")
        except Exception as e:
            logging.error(f"Failed to create destination directory {dest_dir}: {e}")
            raise
    for src_path in files_to_copy:
        dest_path = os.path.join(dest_dir, os.path.basename(src_path))
        try:
            shutil.copy2(src_path, dest_path)
            logging.info(f"Copied {src_path} to {dest_path}")
        except Exception as e:
            logging.error(f"Failed to copy {src_path} to {dest_path}: {e}")
            raise


def git_commit_and_push(repo_dir: str, commit_message: str) -> None:
    """
    Run git add, commit, and push in the specified repo directory.

    Args:
        repo_dir (str): Path to the git repository directory.
        commit_message (str): Commit message for the changes.
    """
    try:
        # Pull latest changes from main branch
        subprocess.run(["git", "pull", "origin", "main"], cwd=repo_dir, check=True)
        logging.info(f"Git pull completed in {repo_dir}")
        # Add all changes
        subprocess.run(["git", "add", "."], cwd=repo_dir, check=True)
        logging.info(f"Git add completed in {repo_dir}")
        # Commit changes
        subprocess.run(
            ["git", "commit", "-m", commit_message], cwd=repo_dir, check=True
        )
        logging.info(f"Git commit completed in {repo_dir}")
        # Push changes
        subprocess.run(["git", "push"], cwd=repo_dir, check=True)
        logging.info("Git push completed.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Git operation failed in {repo_dir}: {e}")
        raise


def save_analysis_json(symbol: str, analysis_data: Dict[str, Any]) -> None:
    """
    Save stock analysis data to a JSON file in the public/analysis directory.

    Args:
        symbol (str): Stock symbol
        analysis_data (Dict[str, Any]): Analysis data to save
    """
    # Create analysis directory if it doesn't exist
    analysis_dir = os.path.join(web2_dir, "public", "analysis")
    os.makedirs(analysis_dir, exist_ok=True)

    # Create JSON file path
    json_file = os.path.join(analysis_dir, f"{symbol}.json")

    try:
        # Save analysis data to JSON file
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(analysis_data, f, ensure_ascii=False, indent=2)
        logging.info(f"Saved analysis data for {symbol} to {json_file}")
    except Exception as e:
        logging.error(f"Failed to save analysis data for {symbol}: {e}")
        raise


def analyze_stock(
    symbol: str, end_date: Optional[datetime] = None, save_json: bool = True
) -> Dict[str, Any]:
    """
    Analyze a stock symbol and return detailed trend and price zone information, including a technical indicator summary table.

    Args:
        symbol (str): Stock symbol to analyze.
        end_date (datetime|None): Ngày kết thúc để lấy dữ liệu phân tích (mặc định là hiện tại).
        save_json (bool): Lưu kết quả phân tích vào file JSON riêng cho mã cổ phiếu.

    Returns:
        dict: Analysis result with trend, price zones, recommendation, and technical indicator summary.
    """
    # Get price data
    data_reader = DataReader(symbol=symbol)
    prices = data_reader.get_daily_prices(days=210, end_date=end_date)

    if not prices or len(prices) < 30:
        raise ValueError("Insufficient data")

    try:
        # Sort prices by timestamp in ascending order to ensure correct time sequence for all calculations
        sorted_prices = sorted(prices, key=lambda x: x.timestamp)

        # Get last trading date from last price
        last_trading_date = datetime.fromtimestamp(
            sorted_prices[-1].timestamp
        ).strftime("%Y-%m-%d")

        # Calculate all required MAs
        ma_periods = [5, 10, 20, 50, 100, 200]
        ma_values = {}

        for period in ma_periods:
            # Calculate SMA
            sma_calc = MovingAverage(
                symbol, sorted_prices, period=period, ma_type="simple"
            )
            sma_values = sma_calc.calculate()
            ma_values[f"sma{period}"] = (
                sma_values[-1] if sma_values and sma_values[-1] is not None else None
            )

            # Calculate EMA
            ema_calc = MovingAverage(
                symbol, sorted_prices, period=period, ma_type="exponential"
            )
            ema_values = ema_calc.calculate()
            ma_values[f"ema{period}"] = (
                ema_values[-1] if ema_values and ema_values[-1] is not None else None
            )

        # Get current price and change values
        current_price = sorted_prices[-1].close_price
        trend_change = (
            sorted_prices[-1].change_price
            if hasattr(sorted_prices[-1], "change_price")
            else 0.0
        )
        trend_change_percent = (
            sorted_prices[-1].change_price_percent
            if hasattr(sorted_prices[-1], "change_price_percent")
            else 0.0
        )

        # Generate MA signals table
        ma_signals = generate_ma_signals_table(current_price, ma_values)

        # Tính SMA5, SMA20
        sma5_calc = MovingAverage(symbol, sorted_prices, period=5, ma_type="simple")
        sma5_values = sma5_calc.calculate()
        sma5 = sma5_values[-1] if sma5_values and sma5_values[-1] is not None else 0.0

        sma20_calc = MovingAverage(symbol, sorted_prices, period=20, ma_type="simple")
        sma20_values = sma20_calc.calculate()
        sma20 = (
            sma20_values[-1] if sma20_values and sma20_values[-1] is not None else 0.0
        )

        # Tính MACD histogram và các giá trị liên quan
        macd_result = MACD(symbol, sorted_prices).calculate()
        macd_hist = (
            macd_result["histogram"][-1]
            if macd_result["histogram"] and macd_result["histogram"][-1] is not None
            else 0.0
        )
        macd_line = (
            macd_result["macd_line"][-1]
            if macd_result["macd_line"] and macd_result["macd_line"][-1] is not None
            else 0.0
        )
        macd_signal = (
            macd_result["signal_line"][-1]
            if macd_result["signal_line"] and macd_result["signal_line"][-1] is not None
            else 0.0
        )

        # Tính RSI
        rsi_calc = RelativeStrengthIndex(symbol, sorted_prices, period=14)
        rsi_values = rsi_calc.calculate()
        rsi = rsi_values[-1] if rsi_values and rsi_values[-1] is not None else 50.0

        # Tính Bollinger Band width
        bb_calc = BollingerBands(symbol, sorted_prices, period=20, num_std_dev=2.0)
        bb_widths = bb_calc.get_band_width()
        bb_width = bb_widths[-1] if bb_widths and bb_widths[-1] is not None else 0.0

        # Tính khối lượng trung bình và kiểm tra volume spike
        volumes = [p.match_volume for p in sorted_prices[-20:]]
        avg_volume = sum(volumes) / len(volumes) if volumes else 0
        volume_spike = (
            any(v > 1.5 * avg_volume for v in volumes[-5:]) if avg_volume else False
        )

        # Sinh mô tả kỹ thuật
        technical_summary = generate_technical_summary(
            symbol, sorted_prices, sma5, sma20, macd_hist, rsi, avg_volume, volume_spike
        )

        # Create SignalSynthesizer instance with Vietnamese language
        synthesizer = SignalSynthesizer(
            symbol=symbol, prices=sorted_prices, language="vi"
        )

        # Get analysis
        analysis = synthesizer.analyze()

        # Extract trend information
        trend_direction = analysis.get("trend_direction", "Đi ngang")
        trend_strength = analysis.get("trend_strength", "Trung bình")
        trend_confidence = analysis.get("trend_confidence", 0)
        market_condition = analysis.get("market_condition", "Trung lập")

        # Compare trend indicators with technical indicator signals
        # Get indicator signals for validation
        indicator_signals = []
        if "enhanced_signals" in analysis:
            indicator_signals = analysis["enhanced_signals"]

        # Count buy/sell/neutral signals
        buy_signals = sum(
            1 for signal in indicator_signals if signal.get("signal") == "Mua"
        )
        sell_signals = sum(
            1 for signal in indicator_signals if signal.get("signal") == "Bán"
        )
        neutral_signals = sum(
            1 for signal in indicator_signals if signal.get("signal") == "Trung tính"
        )
        total_signals = buy_signals + sell_signals + neutral_signals

        # Verify trend alignment with indicators
        if total_signals > 0:
            buy_ratio = buy_signals / total_signals
            sell_ratio = sell_signals / total_signals

            # Add a warning if trend and indicators don't align
            if (trend_direction == "Tăng" and sell_ratio > 0.6) or (
                trend_direction == "Giảm" and buy_ratio > 0.6
            ):
                logging.warning(
                    f"Possible trend-indicator mismatch for {symbol}: Trend={trend_direction}, Buy={buy_ratio:.2f}, Sell={sell_ratio:.2f}"
                )

                # Get trend details from analysis for debugging
                trend_raw = analysis.get("trend_raw", {})
                logging.debug(f"Trend raw data: {trend_raw}")

        # Extract buy, stop loss, and take profit zones
        buy_zones = analysis.get("buy_zones", [])
        stop_loss_zones = analysis.get("stop_loss_zones", [])
        take_profit_zones = analysis.get("take_profit_zones", [])

        # Extract risk-reward ratios
        risk_reward_ratios = analysis.get("risk_reward_ratios", [])

        # Extract recommendation
        recommendation = analysis.get("recommendation", "Không có khuyến nghị")

        # Format the data for display
        formatted_buy_zones: List[Dict[str, Any]] = []
        for zone in buy_zones:
            price = zone.get("price", 0)
            # Fix: Buy zones should be BELOW current price for entry opportunities
            if ensure_numeric_price(price) < current_price:
                formatted_buy_zones.append(
                    {
                        "price": format_price(price),
                        "confidence": zone.get("confidence", "Trung bình"),
                        "reason": zone.get("reason", "Không có lý do"),
                    }
                )

        # Sort buy zones to ensure logical price order (highest first)
        formatted_buy_zones.sort(
            key=lambda x: ensure_numeric_price(x["price"]), reverse=True
        )

        formatted_stop_loss_zones: List[Dict[str, Any]] = []
        for zone in stop_loss_zones:
            price = zone.get("price", 0)
            num_price = ensure_numeric_price(price)
            # Fix: Stop loss zones should be BELOW the lowest buy zone (or current price if no buy zones)
            lowest_buy_price = (
                ensure_numeric_price(formatted_buy_zones[-1]["price"])
                if formatted_buy_zones
                else current_price
            )

            if num_price < lowest_buy_price:
                formatted_stop_loss_zones.append(
                    {
                        "price": format_price(price),
                        "confidence": zone.get("confidence", "Trung bình"),
                        "reason": zone.get("reason", "Không có lý do"),
                    }
                )

        # Sort stop loss zones in descending order (highest first)
        formatted_stop_loss_zones.sort(
            key=lambda x: ensure_numeric_price(x["price"]), reverse=True
        )

        formatted_take_profit_zones: List[Dict[str, Any]] = []
        for zone in take_profit_zones:
            price = zone.get("price", 0)
            num_price = ensure_numeric_price(price)
            # Fix: Take profit zones should be ABOVE current price
            if num_price > current_price:
                formatted_take_profit_zones.append(
                    {
                        "price": format_price(price),
                        "confidence": zone.get("confidence", "Trung bình"),
                        "reason": zone.get("reason", "Không có lý do"),
                    }
                )

        # Sort take profit zones in ascending order (lowest first)
        formatted_take_profit_zones.sort(key=lambda x: ensure_numeric_price(x["price"]))

        formatted_risk_reward_ratios: List[Dict[str, Any]] = []
        for ratio in risk_reward_ratios[:3]:  # Take top 3 ratios
            buy_price = ratio.get("buy_price", 0)
            stop_loss_price = ratio.get("stop_loss_price", 0)
            take_profit_price = ratio.get("take_profit_price", 0)

            buy_price_num = ensure_numeric_price(buy_price)
            stop_loss_price_num = ensure_numeric_price(stop_loss_price)
            take_profit_price_num = ensure_numeric_price(take_profit_price)

            # Validate the ratio has logical price relationships and calculate actual ratio
            if (
                buy_price_num > stop_loss_price_num
                and take_profit_price_num > buy_price_num
            ):
                # Calculate risk (buy - stop loss) and reward (take profit - buy)
                risk = buy_price_num - stop_loss_price_num
                reward = take_profit_price_num - buy_price_num

                # Ensure we don't divide by zero
                risk_reward_ratio = round(reward / risk, 2) if risk > 0 else 0

                # Determine quality based on ratio
                quality = "Thấp"
                if risk_reward_ratio >= 3:
                    quality = "Tuyệt vời"
                elif risk_reward_ratio >= 2:
                    quality = "Tốt"
                elif risk_reward_ratio >= 1.5:
                    quality = "Khá"
                elif risk_reward_ratio >= 1:
                    quality = "Trung bình"

                formatted_risk_reward_ratios.append(
                    {
                        "buy_price": format_price(buy_price),
                        "stop_loss_price": format_price(stop_loss_price),
                        "take_profit_price": format_price(take_profit_price),
                        "ratio": f"{risk_reward_ratio:.2f}",
                        "quality": quality,
                    }
                )

        # Prepare technical indicator summary table
        technical_indicators = {
            "RSI": f"{rsi:.2f}",
            "MACD": f"{macd_line:.2f}",
            "MACD Signal": f"{macd_signal:.2f}",
            "MACD Histogram": f"{macd_hist:.2f}",
            "BB Width": f"{bb_width:.4f}",
            "RS(52W)": None,
        }

        # --- Indicator signals (Buy/Sell/Neutral) ---
        indicator_signals = []

        # Calculate RS(52W) - 52 weeks (1 year)
        # Get VNINDEX data for RS calculation
        benchmark_reader = DataReader(symbol="VNINDEX")
        benchmark_prices = benchmark_reader.get_daily_prices(
            days=365
        )  # Get enough data for 52 weeks (1 year)

        rs_calc = RelativeStrength(symbol, sorted_prices, benchmark_prices, period=52)
        rs_values = rs_calc.calculate()
        latest_rs = rs_values[-1] if rs_values and rs_values[-1] is not None else None

        # Add RS signal
        rs_signal = "Trung tính"
        rs_action = "Theo dõi thêm"

        if latest_rs is not None:
            if latest_rs >= 1.5:
                rs_signal = "Mua"
                rs_action = "RS(52W) cực cao, cổ phiếu mạnh hơn thị trường 50% trở lên, bám sát xu hướng tăng"
            elif latest_rs >= 1.2:
                rs_signal = "Mua"
                rs_action = "RS(52W) cao, cổ phiếu mạnh hơn thị trường 20% trở lên, tiếp tục xu hướng tăng"
            elif latest_rs >= 1.0:
                rs_signal = "Mua"
                rs_action = "RS(52W) tích cực, cổ phiếu mạnh hơn thị trường, có thể mở vị thế mua"
            elif latest_rs <= 0.5:
                rs_signal = "Bán"
                rs_action = "RS(52W) cực thấp, cổ phiếu yếu hơn thị trường 50% trở lên, cân nhắc tái cơ cấu danh mục"
            elif latest_rs <= 0.8:
                rs_signal = "Bán"
                rs_action = "RS(52W) thấp, cổ phiếu yếu hơn thị trường 20% trở lên, thận trọng với vị thế mua"
            else:
                rs_signal = "Trung tính"
                rs_action = "RS(52W) gần bằng thị trường, chưa có lợi thế vượt trội"

        indicator_signals.append(
            {
                "name": "RS(52W)",
                "value": round(latest_rs, 2) if latest_rs is not None else None,
                "signal": rs_signal,
                "action": rs_action,
                "description": "Relative Strength (52 tuần - 1 năm)",
                "info": "Chỉ báo sức mạnh tương đối so với thị trường (VNINDEX). Giá trị > 1 cho thấy cổ phiếu mạnh hơn thị trường, giá trị < 1 cho thấy yếu hơn. RS(52W) so sánh hiệu suất trong 52 tuần (1 năm), giúp xác định sức mạnh dài hạn của cổ phiếu trong ngành.",
            }
        )

        # RSI with more detailed action recommendations
        rsi_action = "Theo dõi thêm"
        if rsi < 30:
            rsi_signal = "Mua"
            if rsi < 20:
                rsi_action = "Cổ phiếu đang quá bán mạnh, cơ hội mua tốt"
            else:
                rsi_action = "Cổ phiếu đang trong vùng quá bán, xem xét mở vị thế mua"
        elif rsi > 70:
            rsi_signal = "Bán"
            if rsi > 80:
                rsi_action = "Cổ phiếu đang quá mua mạnh, nên bán/giảm vị thế"
            else:
                rsi_action = "Cổ phiếu đang trong vùng quá mua, xem xét mở vị thế bán"
        elif rsi > 50:
            rsi_signal = "Trung tính"
            rsi_action = "RSI > 50, xu hướng tăng nhẹ, có thể giữ vị thế mua"
        else:
            rsi_signal = "Trung tính"
            rsi_action = "RSI < 50, xu hướng giảm nhẹ, thận trọng với vị thế mua"

        indicator_signals.append(
            {
                "name": "RSI",
                "value": round(rsi, 2) if rsi is not None else None,
                "signal": rsi_signal,
                "action": rsi_action,
                "description": "Chỉ số sức mạnh tương đối (14 ngày)",
                "info": "RSI đo lường tốc độ và sự thay đổi của biến động giá. Giá trị dưới 30 thường được xem là quá bán (tín hiệu mua), trên 70 là quá mua (tín hiệu bán). Mức 50 là ngưỡng phân cách xu hướng lên/xuống.",
            }
        )
        # STOCHK
        stoch = StochasticOscillator(symbol, sorted_prices)
        stoch_result = stoch.calculate()
        stoch_k = None
        stoch_d = None
        if isinstance(stoch_result, dict):
            if "k_values" in stoch_result and stoch_result["k_values"]:
                # Lấy giá trị cuối cùng không phải None
                stoch_k = next(
                    (v for v in reversed(stoch_result["k_values"]) if v is not None),
                    None,
                )
                if stoch_k is not None:
                    stoch_k = round(stoch_k, 2)

            if "d_values" in stoch_result and stoch_result["d_values"]:
                stoch_d = next(
                    (v for v in reversed(stoch_result["d_values"]) if v is not None),
                    None,
                )
                if stoch_d is not None:
                    stoch_d = round(stoch_d, 2)

        # Thêm MACD vào danh sách indicator_signals
        macd_action = "Theo dõi thêm"
        macd_prev_hist = (
            macd_result["histogram"][-2] if len(macd_result["histogram"]) > 1 else 0
        )

        # Check for histogram direction change (acceleration/deceleration)
        if macd_hist > 0 and macd_hist > macd_prev_hist:
            macd_signal = "Mua"
            macd_action = "Tín hiệu mua đang mạnh lên, xem xét tăng vị thế mua"
        elif macd_hist < 0 and macd_hist < macd_prev_hist:
            macd_signal = "Bán"
            macd_action = "Tín hiệu bán đang mạnh lên, xem xét bán/giảm vị thế"
        elif macd_hist > 0 and macd_line > macd_signal:
            macd_signal = "Mua"
            macd_action = (
                "Đường MACD trên đường tín hiệu, xu hướng tăng đang chiếm ưu thế"
            )
        elif macd_hist < 0 and macd_line < macd_signal:
            macd_signal = "Bán"
            macd_action = (
                "Đường MACD dưới đường tín hiệu, xu hướng giảm đang chiếm ưu thế"
            )
        # Zero-line crossover
        elif macd_line > 0 and macd_prev_hist < 0:
            macd_signal = "Mua"
            macd_action = "MACD vừa cắt qua mức 0, tín hiệu mua mới"
        elif macd_line < 0 and macd_prev_hist > 0:
            macd_signal = "Bán"
            macd_action = "MACD vừa cắt xuống dưới mức 0, tín hiệu bán mới"
        else:
            macd_signal = "Trung tính"

        indicator_signals.append(
            {
                "name": "MACD",
                "value": round(macd_line, 2) if macd_line is not None else None,
                "signal": macd_signal,
                "action": macd_action,
                "description": "Moving Average Convergence Divergence (12,26,9)",
                "info": "MACD là sự khác biệt giữa EMA 12 và EMA 26, với đường tín hiệu là EMA 9 của MACD. Khi MACD cắt lên trên đường tín hiệu là dấu hiệu mua, cắt xuống dưới là dấu hiệu bán. Mức 0 là ngưỡng đảo chiều quan trọng.",
            }
        )

        # MACD Histogram với phân tích động lượng
        histogram_action = "Theo dõi thêm"
        if macd_hist > 0 and macd_hist > macd_prev_hist:
            histogram_signal = "Mua"
            histogram_action = "Động lượng tăng đang mạnh lên, cơ hội mua"
        elif macd_hist < 0 and macd_hist < macd_prev_hist:
            histogram_signal = "Bán"
            histogram_action = "Động lượng giảm đang mạnh lên, nên thận trọng"
        elif macd_hist > 0 and macd_hist < macd_prev_hist:
            histogram_signal = "Trung tính"
            histogram_action = "Động lượng tăng đang chậm lại, cẩn trọng với vị thế mua"
        elif macd_hist < 0 and macd_hist > macd_prev_hist:
            histogram_signal = "Trung tính"
            histogram_action = "Động lượng giảm đang chậm lại, có thể chuẩn bị mua"
        else:
            histogram_signal = "Trung tính"

        indicator_signals.append(
            {
                "name": "MACD Histogram",
                "value": round(macd_hist, 2) if macd_hist is not None else None,
                "signal": histogram_signal,
                "action": histogram_action,
                "description": "MACD Histogram",
                "info": "MACD Histogram là sự chênh lệch giữa đường MACD và đường tín hiệu. Giá trị dương và tăng chỉ báo động lượng tăng mạnh, giá trị âm và giảm chỉ báo động lượng giảm mạnh. Khi histogram thay đổi chiều là dấu hiệu đầu tiên của đảo chiều.",
            }
        )
        # STOCHRSI with enhanced action recommendations
        stochrsi = StochasticRSI(
            symbol,
            sorted_prices,
            rsi_period=14,
            stoch_period=14,
            fast_k_period=3,
            slow_d_period=3,
        )
        stochrsi_result = stochrsi.calculate()
        stochrsi_k = None
        if isinstance(stochrsi_result, dict) and "k_values" in stochrsi_result:
            k_values = stochrsi_result["k_values"]
            if isinstance(k_values, list) and k_values:
                stochrsi_k = next(
                    (v for v in reversed(k_values) if v is not None), None
                )
                if stochrsi_k is not None:
                    # StochasticRSI trả về giá trị từ 0-1, nhân với 100 để được phần trăm
                    stochrsi_k = round(stochrsi_k * 100, 2)  # Giá trị mong đợi là 82.98
        elif isinstance(stochrsi_result, list) and stochrsi_result:
            stochrsi_k = next(
                (v for v in reversed(stochrsi_result) if v is not None), None
            )
            if stochrsi_k is not None:
                stochrsi_k = round(stochrsi_k * 100, 2)

        stochrsi_signal = "Trung tính"
        stochrsi_action = "Theo dõi thêm"
        if stochrsi_k is not None:
            if stochrsi_k < 20:
                stochrsi_signal = "Mua"
                if stochrsi_k < 10:
                    stochrsi_action = (
                        "StochRSI cực thấp, cơ hội mua mạnh, có thể bật tăng nhanh"
                    )
                else:
                    stochrsi_action = (
                        "StochRSI trong vùng quá bán, xem xét mở vị thế mua"
                    )
            elif stochrsi_k > 80:
                stochrsi_signal = "Bán"
                if stochrsi_k > 90:
                    stochrsi_action = (
                        "StochRSI cực cao, áp lực bán mạnh, nên bán/giảm vị thế ngay"
                    )
                else:
                    stochrsi_action = (
                        "StochRSI trong vùng quá mua, xem xét mở vị thế bán"
                    )
            elif stochrsi_k > 50:
                stochrsi_signal = "Trung tính"
                stochrsi_action = (
                    "StochRSI > 50, động lượng tăng nhẹ, có thể giữ vị thế"
                )
            else:
                stochrsi_signal = "Trung tính"
                stochrsi_action = "StochRSI < 50, động lượng giảm nhẹ, thận trọng"

        indicator_signals.append(
            {
                "name": "STOCHRSI_fastk",
                "value": stochrsi_k,
                "signal": stochrsi_signal,
                "action": stochrsi_action,
                "description": "Stochastic RSI Fast %K (3,3,14,14)",
                "info": "Stochastic RSI kết hợp RSI và Stochastic để xác định quá mua/quá bán với độ nhạy cao hơn. Giá trị dưới 20 là quá bán mạnh (cơ hội mua), trên 80 là quá mua mạnh (nên bán). Chỉ báo này có độ nhạy cao hơn RSI thông thường.",
            }
        )
        # ADX with enhanced action recommendations
        adx = AverageDirectionalIndex(symbol, sorted_prices)
        adx_result = adx.calculate()
        adx_val = None
        plus_di = None
        minus_di = None
        if isinstance(adx_result, dict):
            if (
                "adx" in adx_result
                and isinstance(adx_result["adx"], list)
                and adx_result["adx"]
            ):
                adx_val = next(
                    (v for v in reversed(adx_result["adx"]) if v is not None), None
                )
            if (
                "plus_di" in adx_result
                and isinstance(adx_result["plus_di"], list)
                and adx_result["plus_di"]
            ):
                plus_di = next(
                    (v for v in reversed(adx_result["plus_di"]) if v is not None), None
                )
            if (
                "minus_di" in adx_result
                and isinstance(adx_result["minus_di"], list)
                and adx_result["minus_di"]
            ):
                minus_di = next(
                    (v for v in reversed(adx_result["minus_di"]) if v is not None), None
                )

        adx_signal = "Trung tính"
        adx_action = "Theo dõi thêm"
        if adx_val is not None and plus_di is not None and minus_di is not None:
            if adx_val > 40:  # Xu hướng rất mạnh
                if plus_di > minus_di:
                    adx_signal = "Mua"
                    adx_action = "Xu hướng tăng rất mạnh, mở vị thế theo xu hướng"
                elif minus_di > plus_di:
                    adx_signal = "Bán"
                    adx_action = "Xu hướng giảm rất mạnh, mở vị thế theo xu hướng"
            elif adx_val > 25:  # Xu hướng mạnh
                if plus_di > minus_di:
                    adx_signal = "Mua"
                    adx_action = "Xu hướng tăng mạnh, xem xét mua theo xu hướng"
                elif minus_di > plus_di:
                    adx_signal = "Bán"
                    adx_action = "Xu hướng giảm mạnh, xem xét bán theo xu hướng"
            elif adx_val < 20:  # Xu hướng yếu hoặc không có xu hướng
                adx_signal = "Trung tính"
                adx_action = (
                    "Thị trường không có xu hướng rõ ràng, thận trọng khi giao dịch"
                )
            elif adx_val < 15:  # Không có xu hướng
                adx_signal = "Trung tính"
                adx_action = "Thiếu xu hướng, nên tránh giao dịch theo xu hướng, tìm kiếm giao dịch giá trị"

        indicator_signals.append(
            {
                "name": "ADX",
                "value": round(adx_val, 2) if adx_val is not None else None,
                "signal": adx_signal,
                "action": adx_action,
                "description": "Average Directional Index (14)",
                "info": "ADX đo lường độ mạnh của xu hướng, bất kể hướng tăng hay giảm. Giá trị >25 cho thấy xu hướng mạnh, >40 là xu hướng rất mạnh, <20 là thị trường sideway. +DI và -DI xác định hướng của xu hướng: +DI > -DI là xu hướng tăng, ngược lại là giảm.",
            }
        )
        # CCI with improved action recommendations
        cci = CommodityChannelIndex(symbol, sorted_prices)
        cci_values = cci.calculate()
        cci_val = None
        if isinstance(cci_values, list) and cci_values:
            cci_val = next((v for v in reversed(cci_values) if v is not None), None)
        elif isinstance(cci_values, dict) and cci_values:
            last_key = sorted(cci_values.keys())[-1]
            cci_val = cci_values[last_key]

        cci_signal = "Trung tính"
        cci_action = "Theo dõi thêm"
        if cci_val is not None:
            if cci_val < -200:
                cci_signal = "Mua"
                cci_action = "CCI cực thấp, quá bán mạnh, cơ hội mua tốt"
            elif cci_val < -100:
                cci_signal = "Mua"
                cci_action = "CCI trong vùng quá bán, xem xét mở vị thế mua"
            elif cci_val > 200:
                cci_signal = "Bán"
                cci_action = "CCI cực cao, quá mua mạnh, nên bán/giảm vị thế"
            elif cci_val > 100:
                cci_signal = "Bán"
                cci_action = "CCI trong vùng quá mua, xem xét mở vị thế bán"
            elif cci_val > 0:
                cci_signal = "Trung tính"
                cci_action = "CCI dương, xu hướng tăng nhẹ"
            else:
                cci_signal = "Trung tính"
                cci_action = "CCI âm, xu hướng giảm nhẹ"

        indicator_signals.append(
            {
                "name": "CCI",
                "value": round(cci_val, 2) if cci_val is not None else None,
                "signal": cci_signal,
                "action": cci_action,
                "description": "Commodity Channel Index (20)",
                "info": "CCI đo lường sự khác biệt giữa giá hiện tại và giá trung bình lịch sử. Giá trị dưới -100 là quá bán (tín hiệu mua), trên +100 là quá mua (tín hiệu bán). Mức ±200 là vùng cực đoan, thường dẫn đến đảo chiều mạnh.",
            }
        )
        # Williams %R with better action recommendations
        wpr = WilliamsPercentR(symbol, sorted_prices)
        wpr_values = wpr.calculate()
        wpr_val = None
        if isinstance(wpr_values, list) and wpr_values:
            wpr_val = next((v for v in reversed(wpr_values) if v is not None), None)
        elif isinstance(wpr_values, dict) and wpr_values:
            last_key = sorted(wpr_values.keys())[-1]
            wpr_val = wpr_values[last_key]

        # Williams %R with better action recommendations
        wpr_signal = "Trung tính"
        wpr_action = "Theo dõi thêm"
        if wpr_val is not None:
            if wpr_val <= -95:
                wpr_signal = "Mua"
                wpr_action = "Williams %R cực thấp, quá bán mạnh, cơ hội mua rất tốt"
            elif wpr_val <= -80:
                wpr_signal = "Mua"
                wpr_action = "Williams %R trong vùng quá bán, xem xét mở vị thế mua"
            elif wpr_val >= -5:
                wpr_signal = "Bán"
                wpr_action = (
                    "Williams %R cực cao, quá mua mạnh, nên bán/giảm vị thế ngay"
                )
            elif wpr_val >= -20:
                wpr_signal = "Bán"
                wpr_action = "Williams %R trong vùng quá mua, xem xét mở vị thế bán"
            elif wpr_val > -50:
                wpr_signal = "Trung tính"
                wpr_action = "Williams %R trên -50, xu hướng tăng nhẹ"
            else:
                wpr_signal = "Trung tính"
                wpr_action = "Williams %R dưới -50, xu hướng giảm nhẹ"

        indicator_signals.append(
            {
                "name": "WPR",
                "value": round(wpr_val, 2) if wpr_val is not None else None,
                "signal": wpr_signal,
                "action": wpr_action,
                "description": "Williams %R (14)",
                "info": "Williams %R là chỉ báo động lượng đo lường mức độ quá mua hoặc quá bán. Giá trị từ -80 đến -100 là quá bán (tín hiệu mua), từ 0 đến -20 là quá mua (tín hiệu bán). Thường đảo chiều sớm hơn RSI và Stochastic.",
            }
        )
        # ULTOSC
        ultosc = UltimateOscillator(symbol, sorted_prices)
        ultosc_values = ultosc.calculate()
        ultosc_val = None
        if isinstance(ultosc_values, list) and ultosc_values:
            ultosc_val = next(
                (v for v in reversed(ultosc_values) if v is not None), None
            )
        else:
            ultosc_val = None

        # Ultimate Oscillator with detailed action recommendations
        ultosc_signal = "Trung tính"
        ultosc_action = "Theo dõi thêm"
        if ultosc_val is not None:
            if ultosc_val < 20:
                ultosc_signal = "Mua"
                ultosc_action = (
                    "Ultimate Oscillator cực thấp, quá bán mạnh, cơ hội mua tốt"
                )
            elif ultosc_val < 30:
                ultosc_signal = "Mua"
                ultosc_action = (
                    "Ultimate Oscillator trong vùng quá bán, xem xét mở vị thế mua"
                )
            elif ultosc_val > 80:
                ultosc_signal = "Bán"
                ultosc_action = (
                    "Ultimate Oscillator cực cao, quá mua mạnh, nên bán/giảm vị thế"
                )
            elif ultosc_val > 70:
                ultosc_signal = "Bán"
                ultosc_action = (
                    "Ultimate Oscillator trong vùng quá mua, xem xét mở vị thế bán"
                )
            elif ultosc_val > 50:
                ultosc_signal = "Trung tính"
                ultosc_action = "Ultimate Oscillator trên 50, xu hướng tăng nhẹ"
            else:
                ultosc_signal = "Trung tính"
                ultosc_action = "Ultimate Oscillator dưới 50, xu hướng giảm nhẹ"

        indicator_signals.append(
            {
                "name": "ULTOSC",
                "value": round(ultosc_val, 2) if ultosc_val is not None else None,
                "signal": ultosc_signal,
                "action": ultosc_action,
                "description": "Ultimate Oscillator (7,14,28)",
                "info": "Ultimate Oscillator kết hợp ba khoảng thời gian khác nhau (7, 14, 28) để giảm nhiễu và tạo tín hiệu chính xác hơn. Giá trị dưới 30 là quá bán, trên 70 là quá mua. Có độ tin cậy cao khi xác nhận với xu hướng giá chung.",
            }
        )

        # Rate of Change (ROC) with more detailed action recommendations
        roc = RateOfChange(symbol, sorted_prices)
        roc_values = roc.calculate()
        roc_val = None
        if isinstance(roc_values, list) and roc_values:
            roc_val = next((v for v in reversed(roc_values) if v is not None), None)
        elif isinstance(roc_values, dict) and roc_values:
            last_key = sorted(roc_values.keys())[-1]
            roc_val = roc_values[last_key]

        roc_signal = "Trung tính"
        roc_action = "Theo dõi thêm"
        if roc_val is not None:
            if roc_val > 10:
                roc_signal = "Bán"
                roc_action = "ROC cực cao, tốc độ tăng quá nhanh, cẩn trọng với hiện tượng quá mua"
            elif roc_val > 5:
                roc_signal = "Mua"
                roc_action = "ROC tăng mạnh, xác nhận đà tăng giá, theo xu hướng tăng"
            elif roc_val > 0:
                roc_signal = "Mua"
                roc_action = (
                    "ROC dương, đà tăng nhẹ, xu hướng có thể tiếp tục phát triển"
                )
            elif roc_val < -10:
                roc_signal = "Mua"
                roc_action = (
                    "ROC cực thấp, tốc độ giảm quá nhanh, có thể xuất hiện nhịp hồi"
                )
            elif roc_val < -5:
                roc_signal = "Bán"
                roc_action = "ROC giảm mạnh, xác nhận đà giảm giá, theo xu hướng giảm"
            else:
                roc_signal = "Trung tính"
                roc_action = "ROC âm nhẹ, đà giảm nhẹ, theo dõi thêm"

        indicator_signals.append(
            {
                "name": "ROC",
                "value": round(roc_val, 2) if roc_val is not None else None,
                "signal": roc_signal,
                "action": roc_action,
                "description": "Rate of Change (14)",
                "info": "Rate of Change đo lường tốc độ thay đổi giá theo phần trăm trong 14 phiên. Giá trị dương cao cho thấy tốc độ tăng mạnh (có thể là quá mua), giá trị âm sâu cho thấy tốc độ giảm mạnh (có thể là quá bán). Là chỉ báo động lượng tốt để xác định điểm đảo chiều.",
            }
        )

        # Parabolic SAR with enhanced action recommendations
        sar = ParabolicSAR(symbol, sorted_prices)
        sar_values = sar.calculate()
        sar_val = None
        prev_sar = None
        if isinstance(sar_values, list) and len(sar_values) > 1:
            sar_val = next((v for v in reversed(sar_values) if v is not None), None)
            prev_sar = next(
                (v for v in reversed(sar_values[:-1]) if v is not None), None
            )
        elif isinstance(sar_values, dict) and len(sar_values) > 1:
            sorted_keys = sorted(sar_values.keys())
            sar_val = sar_values[sorted_keys[-1]]
            prev_sar = sar_values[sorted_keys[-2]]

        last_close = sorted_prices[-1].close_price
        prev_close = sorted_prices[-2].close_price if len(sorted_prices) > 1 else None

        sar_signal = "Trung tính"
        sar_action = "Theo dõi thêm"
        if prev_sar is not None and prev_close is not None:
            if prev_close < prev_sar and last_close > sar_val:
                sar_signal = "Mua"
                sar_action = "Giá vừa cắt lên trên điểm SAR, tín hiệu mua mạnh, cơ hội mở vị thế mua"
            elif prev_close > prev_sar and last_close < sar_val:
                sar_signal = "Bán"
                sar_action = "Giá vừa cắt xuống dưới điểm SAR, tín hiệu bán mạnh, nên đóng vị thế mua"
            elif last_close > sar_val:
                sar_signal = "Mua"
                sar_action = "Giá đang nằm trên điểm SAR, xu hướng tăng đang tiếp diễn"
            elif last_close < sar_val:
                sar_signal = "Bán"
                sar_action = "Giá đang nằm dưới điểm SAR, xu hướng giảm đang tiếp diễn"

        indicator_signals.append(
            {
                "name": "SAR",
                "value": round(sar_val, 2) if sar_val is not None else None,
                "signal": sar_signal,
                "action": sar_action,
                "description": "Parabolic SAR",
                "info": "Parabolic SAR là chỉ báo theo xu hướng, dùng để xác định điểm dừng và đảo chiều. Điểm SAR nằm dưới giá là tín hiệu mua (xu hướng tăng), điểm SAR nằm trên giá là tín hiệu bán (xu hướng giảm). Khi giá cắt qua điểm SAR, đó là tín hiệu đảo chiều mạnh.",
            }
        )

        # Add OBV (On Balance Volume) indicator
        obv = OnBalanceVolume(symbol, sorted_prices)
        obv_values = obv.calculate()
        obv_val = obv_values[-1] if obv_values and obv_values[-1] is not None else None

        # Determine OBV signal by comparing with previous values
        obv_signal = "Trung tính"
        obv_action = "Theo dõi thêm"

        if len(obv_values) > 5:
            # Check if OBV is trending up or down
            obv_5_periods_ago = obv_values[-6] if len(obv_values) > 5 else obv_values[0]
            price_5_periods_ago = (
                sorted_prices[-6].close_price
                if len(sorted_prices) > 5
                else sorted_prices[0].close_price
            )

            # OBV divergence/confirmation check
            price_trend_up = current_price > price_5_periods_ago
            obv_trend_up = obv_val > obv_5_periods_ago

            if price_trend_up and obv_trend_up:
                obv_signal = "Mua"
                obv_action = "Xác nhận xu hướng tăng, lực mua mạnh"
            elif price_trend_up and not obv_trend_up:
                obv_signal = "Bán"
                obv_action = "Phân kỳ tiêu cực, cảnh báo đảo chiều giảm"
            elif not price_trend_up and obv_trend_up:
                obv_signal = "Mua"
                obv_action = "Phân kỳ tích cực, cảnh báo đảo chiều tăng"
            elif not price_trend_up and not obv_trend_up:
                obv_signal = "Bán"
                obv_action = "Xác nhận xu hướng giảm, lực bán mạnh"

        indicator_signals.append(
            {
                "name": "OBV",
                "value": obv_val,
                "signal": obv_signal,
                "action": obv_action,
                "description": "On Balance Volume",
                "info": "OBV là chỉ báo cộng dồn khối lượng theo chiều giá. Khi OBV tăng cùng chiều với giá xác nhận xu hướng tăng, ngược chiều với giá tạo phân kỳ cảnh báo đảo chiều.",
            }
        )

        # Awesome Oscillator (AO) indicator
        ao = AwesomeOscillator(symbol, sorted_prices)
        ao_values = ao.calculate()
        ao_val = ao_values[-1] if ao_values and ao_values[-1] is not None else None
        ao_prev = (
            ao_values[-2] if len(ao_values) > 1 and ao_values[-2] is not None else None
        )

        ao_signal = "Trung tính"
        ao_action = "Theo dõi thêm"

        if ao_val is not None and ao_prev is not None:
            if ao_val > 0 and ao_prev < 0:  # Zero-line crossover bullish
                ao_signal = "Mua"
                ao_action = "AO vừa chuyển từ âm sang dương, tín hiệu mua mạnh"
            elif ao_val < 0 and ao_prev > 0:  # Zero-line crossover bearish
                ao_signal = "Bán"
                ao_action = "AO vừa chuyển từ dương sang âm, tín hiệu bán mạnh"
            elif ao_val > 0 and ao_val > ao_prev:  # Positive & increasing
                ao_signal = "Mua"
                ao_action = "AO dương và tăng, xác nhận xu hướng tăng đang mạnh lên"
            elif ao_val < 0 and ao_val < ao_prev:  # Negative & decreasing
                ao_signal = "Bán"
                ao_action = "AO âm và giảm, xác nhận xu hướng giảm đang mạnh lên"
            elif ao_val > 0 and ao_val < ao_prev:  # Positive but decreasing
                ao_signal = "Trung tính"
                ao_action = "AO dương nhưng giảm, xu hướng tăng đang yếu đi"
            elif ao_val < 0 and ao_val > ao_prev:  # Negative but increasing
                ao_signal = "Trung tính"
                ao_action = "AO âm nhưng tăng, xu hướng giảm đang yếu đi"

        indicator_signals.append(
            {
                "name": "AO",
                "value": round(ao_val, 2) if ao_val is not None else None,
                "signal": ao_signal,
                "action": ao_action,
                "description": "Awesome Oscillator",
                "info": "Awesome Oscillator so sánh động lượng của 5 phiên gần nhất với 34 phiên. Khi AO vượt qua mức 0 từ dưới lên là tín hiệu mua, từ trên xuống là tín hiệu bán. AO tăng trong vùng dương xác nhận xu hướng tăng, giảm trong vùng âm xác nhận xu hướng giảm.",
            }
        )

        # Accumulation Distribution Index (ADI)
        adi = AccumulationDistributionIndex(symbol, sorted_prices)
        adi_values = adi.calculate()
        adi_val = (
            adi_values["adi"][-1]
            if adi_values
            and "adi" in adi_values
            and adi_values["adi"]
            and adi_values["adi"][-1] is not None
            else None
        )
        adi_prev = (
            adi_values["adi"][-6]
            if adi_values
            and "adi" in adi_values
            and len(adi_values["adi"]) > 5
            and adi_values["adi"][-6] is not None
            else None
        )

        adi_signal = "Trung tính"
        adi_action = "Theo dõi thêm"

        if adi_val is not None and adi_prev is not None:
            # Check if ADI is trending up or down
            price_5_periods_ago = (
                sorted_prices[-6].close_price
                if len(sorted_prices) > 5
                else sorted_prices[0].close_price
            )

            # ADI divergence/confirmation check
            price_trend_up = current_price > price_5_periods_ago
            adi_trend_up = adi_val > adi_prev

            if price_trend_up and adi_trend_up:
                adi_signal = "Mua"
                adi_action = "ADI tăng cùng chiều với giá, xác nhận xu hướng tăng, dòng tiền vào mạnh"
            elif price_trend_up and not adi_trend_up:
                adi_signal = "Bán"
                adi_action = "ADI giảm trong khi giá tăng, phân kỳ tiêu cực, cẩn trọng đảo chiều giảm"
            elif not price_trend_up and adi_trend_up:
                adi_signal = "Mua"
                adi_action = "ADI tăng trong khi giá giảm, phân kỳ tích cực, chuẩn bị đảo chiều tăng"
            elif not price_trend_up and not adi_trend_up:
                adi_signal = "Bán"
                adi_action = "ADI giảm cùng chiều với giá, xác nhận xu hướng giảm, dòng tiền ra mạnh"

        indicator_signals.append(
            {
                "name": "ADI",
                "value": round(adi_val, 2) if adi_val is not None else None,
                "signal": adi_signal,
                "action": adi_action,
                "description": "Accumulation Distribution Index",
                "info": "ADI kết hợp giá và khối lượng để đánh giá dòng tiền. Khi ADI tăng cùng chiều với giá là xác nhận xu hướng, ngược chiều là dấu hiệu phân kỳ. Phân kỳ tích cực (giá giảm nhưng ADI tăng) báo hiệu đảo chiều tăng, phân kỳ tiêu cực ngược lại.",
            }
        )

        # Bollinger Bands with detailed signals
        bb = BollingerBands(symbol, sorted_prices, period=20, num_std_dev=2.0)
        bb_result = bb.calculate()

        # Get upper, middle, and lower bands
        upper_band = (
            bb_result["upper_band"][-1]
            if "upper_band" in bb_result and bb_result["upper_band"]
            else None
        )
        middle_band = (
            bb_result["middle_band"][-1]
            if "middle_band" in bb_result and bb_result["middle_band"]
            else None
        )
        lower_band = (
            bb_result["lower_band"][-1]
            if "lower_band" in bb_result and bb_result["lower_band"]
            else None
        )

        bb_signal = "Trung tính"
        bb_action = "Theo dõi thêm"

        if upper_band is not None and lower_band is not None:
            # Check if price is near or outside bands
            if current_price >= upper_band * 0.98:  # Close to or above upper band
                bb_signal = "Bán"
                if bb_width > 0.1:  # Wide bands indicate strong trend
                    bb_action = "Giá tiếp cận dải trên Bollinger Bands trong xu hướng mạnh, cẩn trọng quá mua"
                else:  # Narrow bands indicate potential breakout
                    bb_action = "Giá chạm dải trên trong dải hẹp, có thể là điểm bán tốt hoặc chuẩn bị breakout lên"
            elif current_price <= lower_band * 1.02:  # Close to or below lower band
                bb_signal = "Mua"
                if bb_width > 0.1:  # Wide bands indicate strong trend
                    bb_action = "Giá tiếp cận dải dưới Bollinger Bands trong xu hướng mạnh, cẩn trọng quá bán"
                else:  # Narrow bands indicate potential breakout
                    bb_action = "Giá chạm dải dưới trong dải hẹp, có thể là điểm mua tốt hoặc chuẩn bị breakout xuống"
            elif bb_width < 0.05:  # Very narrow bands, potential for breakout
                bb_signal = "Trung tính"
                bb_action = "Dải Bollinger rất hẹp, chuẩn bị cho breakout, theo dõi hướng phá dải"
            elif current_price > middle_band:
                bb_signal = "Trung tính"
                bb_action = "Giá trên dải giữa Bollinger Bands, xu hướng tăng nhẹ"
            else:
                bb_signal = "Trung tính"
                bb_action = "Giá dưới dải giữa Bollinger Bands, xu hướng giảm nhẹ"

        indicator_signals.append(
            {
                "name": "BB",
                "value": round(bb_width, 4) if bb_width is not None else None,
                "signal": bb_signal,
                "action": bb_action,
                "description": "Bollinger Bands Width (20,2)",
                "info": "Bollinger Bands sử dụng độ lệch chuẩn để tạo dải biến động quanh SMA. Giá chạm dải trên thường là tín hiệu quá mua, chạm dải dưới là quá bán. Dải hẹp thường báo hiệu sự tích lũy và sắp có breakout. BB Width đo lường độ rộng của dải, giá trị thấp dự báo biến động mạnh sắp xảy ra.",
            }
        )

        # Momentum indicator
        momentum = Momentum(symbol, sorted_prices)
        mom_values = momentum.calculate()
        mom_val = mom_values[-1] if mom_values and mom_values[-1] is not None else None

        mom_signal = "Trung tính"
        mom_action = "Theo dõi thêm"

        if mom_val is not None:
            if mom_val > 10:
                mom_signal = "Mua"
                mom_action = "Động lượng tăng rất mạnh, theo chiều xu hướng tăng"
            elif mom_val > 5:
                mom_signal = "Mua"
                mom_action = "Động lượng tăng khá tốt, xem xét mở vị thế mua"
            elif mom_val > 0:
                mom_signal = "Trung tính"
                mom_action = "Động lượng tăng nhẹ, theo dõi tín hiệu xác nhận"
            elif mom_val < -10:
                mom_signal = "Bán"
                mom_action = "Động lượng giảm rất mạnh, theo chiều xu hướng giảm"
            elif mom_val < -5:
                mom_signal = "Bán"
                mom_action = "Động lượng giảm khá mạnh, xem xét mở vị thế bán"
            else:
                mom_signal = "Trung tính"
                mom_action = "Động lượng giảm nhẹ, theo dõi tín hiệu xác nhận"

        indicator_signals.append(
            {
                "name": "Momentum",
                "value": round(mom_val, 2) if mom_val is not None else None,
                "signal": mom_signal,
                "action": mom_action,
                "description": "Momentum (14)",
                "info": "Momentum đo lường tốc độ thay đổi giá trong 14 phiên. Giá trị dương cao thể hiện xu hướng tăng mạnh, giá trị âm sâu thể hiện xu hướng giảm mạnh. Khi Momentum đạt cực trị và bắt đầu đảo chiều là dấu hiệu sớm cho sự thay đổi xu hướng.",
            }
        )

        # Bear Power indicator
        bear_power = BearPower(symbol, sorted_prices)
        bear_values = bear_power.calculate()
        bear_val = (
            bear_values[-1] if bear_values and bear_values[-1] is not None else None
        )

        bear_signal = "Trung tính"
        bear_action = "Theo dõi thêm"

        if bear_val is not None:
            if (
                bear_val > 0 and bear_val > 0.02 * current_price
            ):  # Positive and significant
                bear_signal = "Mua"
                bear_action = (
                    "Bear Power dương mạnh, lực bán suy yếu, ưu thế thuộc về bên mua"
                )
            elif bear_val > 0:  # Positive but small
                bear_signal = "Mua"
                bear_action = "Bear Power dương nhẹ, xu hướng tăng đang chiếm ưu thế"
            elif (
                bear_val < 0 and bear_val < -0.02 * current_price
            ):  # Negative and significant
                bear_signal = "Bán"
                bear_action = (
                    "Bear Power âm mạnh, lực bán mạnh, ưu thế thuộc về bên bán"
                )
            else:  # Negative but small
                bear_signal = "Bán"
                bear_action = "Bear Power âm nhẹ, xu hướng giảm đang chiếm ưu thế"

        indicator_signals.append(
            {
                "name": "Bear Power",
                "value": round(bear_val, 2) if bear_val is not None else None,
                "signal": bear_signal,
                "action": bear_action,
                "description": "Bear Power",
                "info": "Bear Power đo lường sức mạnh của phe bán bằng cách so sánh giá thấp nhất với EMA 13 ngày. Giá trị dương cho thấy người mua đang kiểm soát thị trường, giá trị âm cho thấy người bán đang kiểm soát. Khi Bear Power âm nhưng đang tăng dần là tín hiệu mua tiềm năng.",
            }
        )

        # Stochastic Oscillator với thông tin chi tiết hơn
        stoch_signal = "Trung tính"
        stoch_action = "Theo dõi thêm"

        if stoch_k is not None and stoch_d is not None:
            if stoch_k < 20 and stoch_d < 20:
                stoch_signal = "Mua"
                stoch_action = "Stochastic trong vùng quá bán, cơ hội mua tốt"
                if stoch_k > stoch_d:
                    stoch_action = "Stochastic trong vùng quá bán với %K cắt lên %D, tín hiệu mua mạnh"
            elif stoch_k > 80 and stoch_d > 80:
                stoch_signal = "Bán"
                stoch_action = "Stochastic trong vùng quá mua, cơ hội bán tốt"
                if stoch_k < stoch_d:
                    stoch_action = "Stochastic trong vùng quá mua với %K cắt xuống %D, tín hiệu bán mạnh"
            elif stoch_k > stoch_d:
                stoch_signal = "Mua"
                stoch_action = "%K cắt lên trên %D, động lượng tăng, cơ hội mua"
            elif stoch_k < stoch_d:
                stoch_signal = "Bán"
                stoch_action = "%K cắt xuống dưới %D, động lượng giảm, cơ hội bán"

        indicator_signals.append(
            {
                "name": "Stochastic",
                "value": stoch_k,
                "signal": stoch_signal,
                "action": stoch_action,
                "description": "Stochastic Oscillator %K (14,3)",
                "info": "Stochastic Oscillator so sánh giá đóng cửa với phạm vi giá trong một khoảng thời gian nhất định. %K là đường chính, %D là đường tín hiệu. Giá trị dưới 20 là quá bán, trên 80 là quá mua. Tín hiệu mua khi %K cắt lên %D, bán khi %K cắt xuống %D.",
            }
        )

        return {
            "symbol": symbol,
            "current_price": format_price(current_price),
            "trend_change": trend_change,
            "trend_change_percent": trend_change_percent,
            "trend_direction": trend_direction,
            "trend_strength": trend_strength,
            "trend_confidence": f"{trend_confidence:.2f}%",
            "market_condition": market_condition,
            "buy_zones": formatted_buy_zones,
            "stop_loss_zones": formatted_stop_loss_zones,
            "take_profit_zones": formatted_take_profit_zones,
            "risk_reward_ratios": formatted_risk_reward_ratios,
            "recommendation": recommendation,
            "analysis_date": datetime.now().strftime("%Y-%m-%d"),
            "last_trading_date": last_trading_date,
            "technical_summary": technical_summary,
            "ma_signals": ma_signals,
            "technical_indicators": technical_indicators,
            "indicator_signals": indicator_signals,
        }
    except Exception as e:
        logging.error(f"Error analyzing stock {symbol}: {e}", exc_info=True)
        return {
            "symbol": symbol,
            "error": str(e),
            "trend_direction": "Error",
            "current_price": "N/A",
            "buy_zones": [],
            "stop_loss_zones": [],
            "take_profit_zones": [],
            "risk_reward_ratios": [],
            "recommendation": f"Lỗi: {str(e)}",
            "analysis_date": datetime.now().strftime("%Y-%m-%d"),
            "last_trading_date": None,
            "technical_summary": f"Lỗi: {str(e)}",
        }


def format_price(price_str: str | float) -> str:
    """
    Format price to Vietnamese format (#,##0)

    Args:
        price_str (str | float): Price as string or number.

    Returns:
        str: Formatted price string.
    """
    try:
        price = float(price_str)
        return f"{price:,.0f}"
    except (ValueError, TypeError) as e:
        logging.warning(f"Could not format price '{price_str}': {e}")
        return str(price_str)


# Add a new helper function to return numeric price values
def ensure_numeric_price(price_str: str | float) -> float:
    """
    Ensure a price value is numeric, converting from string if necessary.

    Args:
        price_str (str | float): Price as string or number.

    Returns:
        float: Price as a float value.
    """
    if isinstance(price_str, float) or isinstance(price_str, int):
        return float(price_str)
    elif isinstance(price_str, str):
        # Handle Vietnamese price formatting
        # For VN format like "10.000" or "10,000" where dots/commas are thousand separators
        try:
            # First check if it's already a simple float without separators
            try:
                return float(price_str)
            except ValueError:
                pass

            # For Vietnamese format like "10.000" (dot as thousand separator)
            if "." in price_str and "," not in price_str:
                # Replace dots with empty string only if they appear to be thousand separators
                parts = price_str.split(".")
                if len(parts[-1]) == 3 or all(len(p) == 3 for p in parts[1:]):
                    clean_str = price_str.replace(".", "")
                    return float(clean_str)
                # Otherwise it might be a decimal dot
                else:
                    return float(price_str)

            # For format like "10,000" (comma as thousand separator)
            elif "," in price_str and "." not in price_str:
                # Replace commas with empty string only if they appear to be thousand separators
                parts = price_str.split(",")
                if len(parts[-1]) == 3 or all(len(p) == 3 for p in parts[1:]):
                    clean_str = price_str.replace(",", "")
                    return float(clean_str)
                # Otherwise it might be a decimal comma (unlikely in Vietnamese format)
                else:
                    return float(price_str.replace(",", "."))

            # For mixed format or other cases, try a simpler approach
            else:
                # Remove all non-numeric characters except one decimal point
                clean_str = "".join(
                    c for c in price_str if c.isdigit() or c == "." or c == ","
                )
                if "." in clean_str and "," in clean_str:
                    # Keep the rightmost decimal separator
                    if clean_str.rindex(".") > clean_str.rindex(","):
                        clean_str = clean_str.replace(",", "")
                    else:
                        clean_str = clean_str.replace(".", "").replace(",", ".")
                elif "," in clean_str:
                    clean_str = clean_str.replace(",", ".")

                return float(clean_str)

        except (ValueError, TypeError) as e:
            logging.warning(
                f"Could not convert price '{price_str}' to numeric value: {e}"
            )
            return 0.0
    else:
        logging.warning(f"Unexpected price type: {type(price_str)}")
        return 0.0


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments for the stock analyzer script.

    Returns:
        argparse.Namespace: Parsed arguments namespace.
    """
    parser = argparse.ArgumentParser(
        description="Stock analysis and site generation workflow."
    )
    parser.add_argument(
        "--deploy",
        action="store_true",
        help="If set, perform post-processing: move files and push to GitHub.",
    )
    parser.add_argument(
        "--date",
        type=str,
        default=None,
        help="Ngày phân tích (định dạng YYYY-MM-DD, mặc định là ngày hiện tại)",
    )
    return parser.parse_args()


def update_prediction_js_reference(
    index_html_path: str, new_prediction_js: str
) -> None:
    """
    Update the <script src="prediction-*.js"> reference in index.html to point to the latest prediction JS file.

    Args:
        index_html_path (str): Path to the index.html file.
        new_prediction_js (str): Filename of the new prediction JS file (e.g., 'prediction-2025-05-08.js').
    """
    try:
        with open(index_html_path, "r", encoding="utf-8") as f:
            content = f.read()
        # Replace the prediction JS reference
        new_content, count = re.subn(
            r'<script src="prediction-[\d\-]+\.js"(.*?) defer></script>',
            f'<script src="{os.path.basename(new_prediction_js)}" defer></script>',
            content,
            count=1,
        )
        if count == 0:
            logging.warning(f"No prediction JS reference found in {index_html_path}.")
        else:
            with open(index_html_path, "w", encoding="utf-8") as f:
                f.write(new_content)
            logging.info(
                f"Updated prediction JS reference in {index_html_path} to {new_prediction_js}"
            )
    except Exception as e:
        logging.error(
            f"Failed to update prediction JS reference in {index_html_path}: {e}"
        )
        raise


def main(deploy: bool = False, analysis_date: Optional[str] = None) -> None:
    """
    Main entry point for stock analysis and site generation workflow.
    Handles data fetching, analysis, file generation, and optional deployment to GitHub Pages repo.

    Args:
        deploy (bool): If True, perform post-processing (move files and push to GitHub).
        analysis_date (str|None): Ngày phân tích (YYYY-MM-DD) hoặc None.
    """
    try:
        sql_service = SqliteService()
        symbol_dao = SymbolDao(sql_service=sql_service)
        all_symbols = symbol_dao.get_all()

        # Batch processing for performance
        batch_size = 50
        valid_symbols: List[str] = []

        for i in range(0, len(all_symbols), batch_size):
            batch = all_symbols[i : i + batch_size]
            logging.info(
                f"Checking data for batch {i//batch_size + 1}/{(len(all_symbols) + batch_size - 1)//batch_size}..."
            )

            for symbol in batch:
                try:
                    data_reader = DataReader(symbol=symbol.code)
                    # Lấy dữ liệu đến analysis_date nếu có
                    end_date = None
                    if analysis_date:
                        end_date = datetime.strptime(analysis_date, "%Y-%m-%d")
                    prices = data_reader.get_daily_prices(days=10, end_date=end_date)
                    if prices:
                        valid_symbols.append(symbol.code)
                except Exception as e:
                    logging.warning(f"Error fetching data for {symbol.code}: {e}")

        if not valid_symbols:
            logging.error("No stocks found with sufficient data for analysis.")
            return

        symbols = valid_symbols
        logging.info(f"Found {len(symbols)} stocks with sufficient data for analysis.")

    except Exception as e:
        logging.error(f"Database connection error: {e}")
        return

    # Xác định ngày phân tích
    end_date = None
    if analysis_date:
        end_date = datetime.strptime(analysis_date, "%Y-%m-%d")

    # Analyze each stock
    analysis_results: List[Dict[str, Any]] = []
    analysis_json_results: Dict[str, Dict[str, Any]] = {}

    if not deploy:
        symbols = ["HPG", "TCB", "VNM"]

    for symbol in symbols:
        if symbol in ["VNINDEX"]:
            continue

        try:
            result = analyze_stock(symbol, end_date=end_date)
            analysis_results.append(result)
            analysis_json_results[symbol] = result
        except Exception as e:
            logging.info(f"Ignore analyzing {symbol}: {e}")

    logging.info(f"Analyzed {len(analysis_results)} stocks.")

    # Lấy ngày giao dịch cuối cùng thực tế trong dữ liệu (nếu có end_date thì lấy ngày đó)
    if end_date:
        trading_date_str = end_date.strftime("%Y-%m-%d")
    else:
        last_trading_date = Utils.get_last_trading_date()
        trading_date_str = last_trading_date.strftime("%Y-%m-%d")
    today = datetime.now().strftime("%Y-%m-%d")

    # Save analysis results as JavaScript file with last trading date suffix
    js_output_path_with_date = os.path.join(
        web_dir, f"prediction-{trading_date_str}.js"
    )
    try:
        with open(js_output_path_with_date, "w", encoding="utf-8") as f:
            # Generate minified JavaScript output to reduce file size
            f.write(f"// Generated on {today} for trading date {trading_date_str}\n")
            f.write("const PREDICTION_DATA=")
            # Convert to JSON with minimal whitespace (no indent, no spaces after separators)
            json.dump(analysis_results, f, ensure_ascii=False, separators=(",", ":"))
            f.write(";\n")
            f.write(f"const PREDICTION_DATE='{today}';\n")
        logging.info(f"Analysis data saved to {js_output_path_with_date}")
        # Update index.html to reference the new prediction JS file
        update_prediction_js_reference(
            os.path.join(web_dir, "index.html"), js_output_path_with_date
        )
    except Exception as e:
        logging.error(f"Failed to write prediction JS file: {e}")
        return

    try:
        for symbol, result in analysis_json_results.items():
            # Lưu kết quả phân tích vào file JSON nếu được yêu cầu
            save_analysis_json(symbol, result)
    except Exception as e:
        logging.error(f"Failed to write prediction JSON files: {e}")
        return

    # Ensure index.html, script.js, and styles.css exist in web folder
    for filename in ["index.html", "script.js", "styles.css"]:
        file_path = os.path.join(web_dir, filename)
        if not os.path.exists(file_path):
            logging.error(
                f"Required file missing: {file_path}. Please provide this file in the web directory."
            )

    logging.debug(f"Deploy? {deploy}")

    if deploy:
        # === Post-processing: Move files and push to GitHub ===
        try:
            copy_site_files_to_repo(web_dir, bidaskprice_dir, js_output_path_with_date)
            commit_msg = f"Update prediction and site files for {trading_date_str}"
            git_commit_and_push(bidaskprice_dir, commit_msg)
        except Exception as e:
            logging.error(f"Deployment failed: {e}")
            return


def generate_technical_summary(
    symbol: str,
    prices: list[PriceData],
    sma5: float,
    sma20: float,
    macd_hist: float,
    rsi: float,
    avg_volume: float,
    volume_spike: bool,
) -> str:
    """
    Generate detailed technical analysis summary for a stock based on various indicators.

    Args:
        symbol (str): Stock symbol
        prices (list[PriceData]): List of price data points
        sma5 (float): 5-day Simple Moving Average
        sma20 (float): 20-day Simple Moving Average
        macd_hist (float): MACD histogram value
        rsi (float): Relative Strength Index value
        avg_volume (float): Average trading volume
        volume_spike (bool): Whether there is a volume spike

    Returns:
        str: Detailed technical analysis summary with line breaks between sections
    """
    if not prices:
        return "Không đủ dữ liệu để phân tích kỹ thuật."

    current_price = prices[-1].close_price
    price_change = prices[-1].change_price_percent
    last_trading_date = datetime.fromtimestamp(prices[-1].timestamp).strftime(
        "%Y-%m-%d"
    )

    summary_parts = []

    # Time frame context
    summary_parts.append(f"=== PHÂN TÍCH KỸ THUẬT {symbol} ({last_trading_date}) ===")
    summary_parts.append("• Khung thời gian: Phân tích hàng ngày (Daily)")
    summary_parts.append(
        f"• Giá hiện tại: {format_price(current_price)} ({price_change:+.2f}%)"
    )

    # SMA Analysis
    if current_price > sma5 and current_price > sma20:
        summary_parts.append(
            f"• Giá đang nằm trên cả SMA5 ({format_price(sma5)}) và SMA20 ({format_price(sma20)})"
        )
        summary_parts.append("• Xu hướng tăng ngắn hạn đang chiếm ưu thế")
        if sma5 > sma20:
            summary_parts.append("• SMA5 > SMA20: Tín hiệu tăng giá mạnh")
    elif current_price < sma5 and current_price < sma20:
        summary_parts.append(
            f"• Giá nằm dưới cả SMA5 ({format_price(sma5)}) và SMA20 ({format_price(sma20)})"
        )
        summary_parts.append("• Xu hướng giảm ngắn hạn đang chiếm ưu thế")
        if sma5 < sma20:
            summary_parts.append("• SMA5 < SMA20: Tín hiệu giảm giá mạnh")
    elif current_price > sma5 > sma20:
        summary_parts.append("• Giá vượt SMA5 nhưng chưa vượt SMA20")
        summary_parts.append("• Tín hiệu hồi phục ngắn hạn, cần quan sát thêm")
    elif current_price < sma5 < sma20:
        summary_parts.append("• Giá dưới SMA5 và SMA20")
        summary_parts.append("• Động lực giảm vẫn chiếm ưu thế")
    else:
        summary_parts.append(
            f"• Giá đang dao động quanh các đường trung bình SMA5 ({format_price(sma5)}) và SMA20 ({format_price(sma20)})"
        )
        summary_parts.append("• Thị trường trong trạng thái sideway hoặc tích lũy")

    # 2. Phân tích MACD
    summary_parts.append("\n=== PHÂN TÍCH MACD ===")
    if macd_hist is not None:
        if macd_hist > 1:
            summary_parts.append(f"• MACD histogram: {macd_hist:.2f} (dương mạnh)")
            summary_parts.append("• Động lực tăng giá rõ rệt")
            summary_parts.append("• Khả năng cao tiếp tục xu hướng tăng")
        elif 0 < macd_hist <= 1:
            summary_parts.append(f"• MACD histogram: {macd_hist:.2f} (dương nhẹ)")
            summary_parts.append("• Động lực tăng giá yếu")
            summary_parts.append("• Cần quan sát thêm để xác nhận xu hướng")
        elif macd_hist < -1:
            summary_parts.append(f"• MACD histogram: {macd_hist:.2f} (âm mạnh)")
            summary_parts.append("• Động lực giảm giá rõ rệt")
            summary_parts.append("• Khả năng cao tiếp tục xu hướng giảm")
        elif -1 <= macd_hist < 0:
            summary_parts.append(f"• MACD histogram: {macd_hist:.2f} (âm nhẹ)")
            summary_parts.append("• Động lực giảm giá yếu")
            summary_parts.append("• Có thể xuất hiện đảo chiều")
        else:
            summary_parts.append(f"• MACD histogram: {macd_hist:.2f} (trung tính)")
            summary_parts.append("• Thị trường thiếu động lực rõ ràng")
            summary_parts.append("• Cần chờ tín hiệu mới")

    # 3. Phân tích RSI
    summary_parts.append("\n=== PHÂN TÍCH RSI ===")
    if rsi >= 70:
        summary_parts.append(f"• RSI: {rsi:.2f} (vùng quá mua)")
        summary_parts.append("• Cảnh báo khả năng điều chỉnh giảm")
        if rsi >= 80:
            summary_parts.append("• RSI quá cao, rủi ro điều chỉnh mạnh")
    elif rsi <= 30:
        summary_parts.append(f"• RSI: {rsi:.2f} (vùng quá bán)")
        summary_parts.append("• Khả năng xuất hiện nhịp hồi")
        if rsi <= 20:
            summary_parts.append("• RSI quá thấp, cơ hội mua vào")
    else:
        summary_parts.append(f"• RSI: {rsi:.2f} (vùng trung tính)")
        summary_parts.append("• Chưa có tín hiệu quá mua/quá bán")
        if rsi > 50:
            summary_parts.append("• RSI > 50: Động lực tăng nhẹ")
        else:
            summary_parts.append("• RSI < 50: Động lực giảm nhẹ")

    # 4. Phân tích khối lượng
    summary_parts.append("\n=== PHÂN TÍCH KHỐI LƯỢNG ===")
    if volume_spike:
        summary_parts.append(f"• Khối lượng trung bình: {format_price(avg_volume)}")
        summary_parts.append("• Phát hiện tăng đột biến về khối lượng")
        summary_parts.append("• Dòng tiền vào mạnh hoặc có biến động bất thường")
        if price_change > 0:
            summary_parts.append("• Khối lượng tăng + giá tăng: Tín hiệu tích cực")
        else:
            summary_parts.append("• Khối lượng tăng + giá giảm: Cần thận trọng")
    else:
        summary_parts.append(f"• Khối lượng trung bình: {format_price(avg_volume)}")
        summary_parts.append("• Khối lượng giao dịch ổn định")
        if price_change > 0:
            summary_parts.append("• Giá tăng với khối lượng ổn định: Xu hướng bền vững")
        else:
            summary_parts.append("• Giá giảm với khối lượng ổn định: Xu hướng yếu")

    # 5. Phân tích Bollinger Bands
    try:
        bb_calc = BollingerBands(symbol, prices, period=20, num_std_dev=2.0)
        bb_result = bb_calc.calculate()
        bb_widths = bb_calc.get_band_width()
        bb_width = bb_widths[-1] if bb_widths and bb_widths[-1] is not None else 0.0

        upper_band = (
            bb_result["upper_band"][-1]
            if "upper_band" in bb_result and bb_result["upper_band"]
            else None
        )
        middle_band = (
            bb_result["middle_band"][-1]
            if "middle_band" in bb_result and bb_result["middle_band"]
            else None
        )
        lower_band = (
            bb_result["lower_band"][-1]
            if "lower_band" in bb_result and bb_result["lower_band"]
            else None
        )

        summary_parts.append("\n=== PHÂN TÍCH BOLLINGER BANDS ===")
        summary_parts.append(f"• Độ rộng dải Bollinger: {bb_width:.4f}")

        if upper_band and lower_band and middle_band:
            summary_parts.append(f"• Dải trên: {format_price(upper_band)}")
            summary_parts.append(f"• Dải giữa (SMA20): {format_price(middle_band)}")
            summary_parts.append(f"• Dải dưới: {format_price(lower_band)}")

            if bb_width < 0.05:
                summary_parts.append(
                    "• Dải Bollinger rất hẹp, chuẩn bị cho breakout mạnh"
                )
            elif bb_width < 0.1:
                summary_parts.append(
                    "• Dải Bollinger hẹp, khả năng tích lũy trước breakout"
                )
            else:
                summary_parts.append(
                    "• Dải Bollinger rộng, thị trường đang có biến động mạnh"
                )

            if current_price >= upper_band * 0.98:
                summary_parts.append(
                    "• Giá gần chạm dải trên: Cảnh báo quá mua hoặc breakout tăng"
                )
            elif current_price <= lower_band * 1.02:
                summary_parts.append(
                    "• Giá gần chạm dải dưới: Cảnh báo quá bán hoặc breakout giảm"
                )
            elif current_price > middle_band:
                summary_parts.append("• Giá trên dải giữa: Xu hướng tăng nhẹ")
            else:
                summary_parts.append("• Giá dưới dải giữa: Xu hướng giảm nhẹ")
    except Exception as e:
        logging.warning(f"Không thể phân tích Bollinger Bands: {e}")

    # 6. Xác định mức hỗ trợ/kháng cự
    try:
        # Kiểm tra xem PivotPoints đã được import thành công chưa
        if "PivotPoints" in globals():
            # Xác định các mức hỗ trợ/kháng cự dựa trên pivot points
            pivot = PivotPoints(symbol, prices)
            pivot_levels = pivot.calculate()

            summary_parts.append("\n=== MỨC HỖ TRỢ/KHÁNG CỰ ===")
            if "PP" in pivot_levels:
                summary_parts.append(
                    f"• Pivot Point: {format_price(pivot_levels['PP'])}"
                )

            # Mức kháng cự (Resistance)
            resistance_levels = []
            for level in ["R1", "R2", "R3"]:
                if level in pivot_levels:
                    resistance_levels.append(
                        f"{level}: {format_price(pivot_levels[level])}"
                    )

            if resistance_levels:
                summary_parts.append("• Mức kháng cự: " + ", ".join(resistance_levels))

            # Mức hỗ trợ (Support)
            support_levels = []
            for level in ["S1", "S2", "S3"]:
                if level in pivot_levels:
                    support_levels.append(
                        f"{level}: {format_price(pivot_levels[level])}"
                    )

            if support_levels:
                summary_parts.append("• Mức hỗ trợ: " + ", ".join(support_levels))

            # Xác định vị trí giá hiện tại so với các mức pivot
            if "R1" in pivot_levels and current_price > pivot_levels["R1"]:
                summary_parts.append("• Giá đang nằm trên R1: Xu hướng tăng mạnh")
            elif "PP" in pivot_levels and current_price > pivot_levels["PP"]:
                summary_parts.append("• Giá đang nằm trên PP: Xu hướng tăng")
            elif "S1" in pivot_levels and current_price < pivot_levels["S1"]:
                summary_parts.append("• Giá đang nằm dưới S1: Xu hướng giảm mạnh")
            elif "PP" in pivot_levels and current_price < pivot_levels["PP"]:
                summary_parts.append("• Giá đang nằm dưới PP: Xu hướng giảm")
        else:
            summary_parts.append("\n=== MỨC HỖ TRỢ/KHÁNG CỰ ===")
            summary_parts.append(
                "• Không thể tính toán mức hỗ trợ/kháng cự: Lớp PivotPoints không khả dụng"
            )
    except Exception as e:
        logging.warning(f"Không thể xác định mức hỗ trợ/kháng cự: {e}")
        summary_parts.append("\n=== MỨC HỖ TRỢ/KHÁNG CỰ ===")
        summary_parts.append(f"• Không thể tính toán mức hỗ trợ/kháng cự: {e}")

    # 7. Phân tích mẫu hình biểu đồ (Chart Patterns)
    try:
        # Kiểm tra xem đối tượng PriceData có thuộc tính high_price và low_price không
        has_high_low = hasattr(prices[0], "high_price") and hasattr(
            prices[0], "low_price"
        )

        if has_high_low:
            # Đơn giản hóa việc phát hiện một số mẫu hình cơ bản
            # Lấy dữ liệu giá đóng cửa
            closes = [p.close_price for p in prices[-30:]]  # 30 phiên gần nhất
            highs = [p.high_price for p in prices[-30:]]
            lows = [p.low_price for p in prices[-30:]]

            # Pattern detection logic
            pattern_detected = False
            summary_parts.append("\n=== MẪU HÌNH BIỂU ĐỒ ===")

            # Kiểm tra Double Top
            if len(highs) >= 10:
                peak1_idx = None
                peak2_idx = None
                for i in range(5, len(highs) - 5):
                    if (
                        highs[i] > highs[i - 1]
                        and highs[i] > highs[i - 2]
                        and highs[i] > highs[i + 1]
                        and highs[i] > highs[i + 2]
                    ):
                        if peak1_idx is None:
                            peak1_idx = i
                        elif (
                            peak2_idx is None
                            and abs(highs[i] - highs[peak1_idx]) / highs[peak1_idx]
                            < 0.03
                        ):
                            peak2_idx = i

                if (
                    peak1_idx is not None
                    and peak2_idx is not None
                    and peak2_idx - peak1_idx >= 5
                ):
                    summary_parts.append(
                        "• Phát hiện mẫu hình Double Top: Cảnh báo đảo chiều giảm"
                    )
                    pattern_detected = True

            # Kiểm tra Double Bottom
            if len(lows) >= 10:
                bottom1_idx = None
                bottom2_idx = None
                for i in range(5, len(lows) - 5):
                    if (
                        lows[i] < lows[i - 1]
                        and lows[i] < lows[i - 2]
                        and lows[i] < lows[i + 1]
                        and lows[i] < lows[i + 2]
                    ):
                        if bottom1_idx is None:
                            bottom1_idx = i
                        elif (
                            bottom2_idx is None
                            and abs(lows[i] - lows[bottom1_idx]) / lows[bottom1_idx]
                            < 0.03
                        ):
                            bottom2_idx = i

                if (
                    bottom1_idx is not None
                    and bottom2_idx is not None
                    and bottom2_idx - bottom1_idx >= 5
                ):
                    summary_parts.append(
                        "• Phát hiện mẫu hình Double Bottom: Tín hiệu đảo chiều tăng"
                    )
                    pattern_detected = True

            # Kiểm tra Head and Shoulders
            if len(highs) >= 15:
                left_idx = None
                head_idx = None
                right_idx = None

                # Tìm đỉnh cao nhất (head)
                max_idx = 5 + np.argmax(highs[5:-5])

                # Tìm đỉnh bên trái
                left_candidates = [
                    (i, highs[i])
                    for i in range(5, max_idx - 3)
                    if highs[i] > highs[i - 1] and highs[i] > highs[i + 1]
                ]
                if left_candidates:
                    left_idx = max(left_candidates, key=lambda x: x[1])[0]

                # Tìm đỉnh bên phải
                right_candidates = [
                    (i, highs[i])
                    for i in range(max_idx + 3, len(highs) - 5)
                    if highs[i] > highs[i - 1] and highs[i] > highs[i + 1]
                ]
                if right_candidates:
                    right_idx = max(right_candidates, key=lambda x: x[1])[0]

                if (
                    left_idx
                    and right_idx
                    and highs[max_idx] > highs[left_idx]
                    and highs[max_idx] > highs[right_idx]
                    and abs(highs[left_idx] - highs[right_idx]) / highs[left_idx] < 0.05
                ):
                    summary_parts.append(
                        "• Phát hiện mẫu hình Head and Shoulders: Cảnh báo đảo chiều giảm"
                    )
                    pattern_detected = True

            # Kiểm tra kênh giá
            if len(closes) >= 20:
                uptrend = all(closes[i] >= closes[i - 5] for i in range(19, 9, -1))
                downtrend = all(closes[i] <= closes[i - 5] for i in range(19, 9, -1))

                if uptrend:
                    summary_parts.append(
                        "• Phát hiện kênh tăng giá: Xu hướng tăng đang tiếp diễn"
                    )
                    pattern_detected = True
                elif downtrend:
                    summary_parts.append(
                        "• Phát hiện kênh giảm giá: Xu hướng giảm đang tiếp diễn"
                    )
                    pattern_detected = True

            if not pattern_detected:
                summary_parts.append("• Chưa phát hiện mẫu hình đặc trưng rõ ràng")
        else:
            summary_parts.append("\n=== MẪU HÌNH BIỂU ĐỒ ===")
            summary_parts.append(
                "• Không có dữ liệu giá cao/thấp để phân tích mẫu hình"
            )
    except Exception as e:
        logging.warning(f"Không thể phân tích mẫu hình biểu đồ: {e}")

    # 8. Phát hiện phân kỳ (Divergence)
    try:
        # Tính RSI cho 14 phiên gần nhất
        rsi_calc = RelativeStrengthIndex(symbol, prices, period=14)
        rsi_values = rsi_calc.calculate()

        # Kiểm tra xem đối tượng PriceData có thuộc tính high_price và low_price không
        has_high_low = hasattr(prices[0], "high_price") and hasattr(
            prices[0], "low_price"
        )

        summary_parts.append("\n=== PHÂN TÍCH PHÂN KỲ ===")

        # Phát hiện phân kỳ giữa giá và RSI
        if has_high_low and len(rsi_values) >= 14 and len(prices) >= 14:
            price_highs = []
            price_lows = []
            rsi_highs = []
            rsi_lows = []

            # Tìm các đỉnh và đáy cục bộ
            for i in range(5, len(prices) - 5):
                # Đỉnh giá
                if (
                    prices[i].high_price > prices[i - 1].high_price
                    and prices[i].high_price > prices[i + 1].high_price
                ):
                    price_highs.append((i, prices[i].high_price))

                # Đáy giá
                if (
                    prices[i].low_price < prices[i - 1].low_price
                    and prices[i].low_price < prices[i + 1].low_price
                ):
                    price_lows.append((i, prices[i].low_price))

                # Đỉnh RSI
                if i < len(rsi_values) and i > 0 and i + 1 < len(rsi_values):
                    if (
                        rsi_values[i] > rsi_values[i - 1]
                        and rsi_values[i] > rsi_values[i + 1]
                    ):
                        rsi_highs.append((i, rsi_values[i]))

                    # Đáy RSI
                    if (
                        rsi_values[i] < rsi_values[i - 1]
                        and rsi_values[i] < rsi_values[i + 1]
                    ):
                        rsi_lows.append((i, rsi_values[i]))

            # Kiểm tra phân kỳ âm (Bearish Divergence)
            bearish_div = False
            if len(price_highs) >= 2 and len(rsi_highs) >= 2:
                last_price_high = price_highs[-1]
                prev_price_high = price_highs[-2]
                last_rsi_high = rsi_highs[-1]
                prev_rsi_high = rsi_highs[-2]

                if (
                    last_price_high[1] > prev_price_high[1]
                    and last_rsi_high[1] < prev_rsi_high[1]
                ):
                    bearish_div = True

            # Kiểm tra phân kỳ dương (Bullish Divergence)
            bullish_div = False
            if len(price_lows) >= 2 and len(rsi_lows) >= 2:
                last_price_low = price_lows[-1]
                prev_price_low = price_lows[-2]
                last_rsi_low = rsi_lows[-1]
                prev_rsi_low = rsi_lows[-2]

                if (
                    last_price_low[1] < prev_price_low[1]
                    and last_rsi_low[1] > prev_rsi_low[1]
                ):
                    bullish_div = True

            if bearish_div:
                summary_parts.append(
                    "• Phát hiện phân kỳ âm (Bearish Divergence): Giá tạo đỉnh cao hơn nhưng RSI tạo đỉnh thấp hơn"
                )
                summary_parts.append(
                    "• Cảnh báo suy yếu động lực tăng, có thể đảo chiều giảm"
                )
            elif bullish_div:
                summary_parts.append(
                    "• Phát hiện phân kỳ dương (Bullish Divergence): Giá tạo đáy thấp hơn nhưng RSI tạo đáy cao hơn"
                )
                summary_parts.append(
                    "• Cảnh báo suy yếu động lực giảm, có thể đảo chiều tăng"
                )
            else:
                summary_parts.append("• Không phát hiện phân kỳ RSI")
        else:
            summary_parts.append("• Không có dữ liệu giá cao/thấp để phân tích phân kỳ")
    except Exception as e:
        logging.warning(f"Không thể phân tích phân kỳ: {e}")

    # Calculate RS(52W) - 52 weeks (1 year)
    try:
        # Get VNINDEX data for RS calculation
        benchmark_reader = DataReader(symbol="VNINDEX")
        benchmark_prices = benchmark_reader.get_daily_prices(
            days=365
        )  # Get enough data for 52 weeks (1 year)

        rs_calc = RelativeStrength(symbol, prices, benchmark_prices, period=52)
        rs_values = rs_calc.calculate()
        latest_rs = rs_values[-1] if rs_values and rs_values[-1] is not None else None

        if latest_rs is not None:
            summary_points = []
            if latest_rs >= 1.5:
                summary_points.append(
                    "- RS(52W) rất mạnh: Cổ phiếu mạnh hơn thị trường 50% trở lên, tín hiệu mua mạnh."
                )
            elif latest_rs >= 1.2:
                summary_points.append(
                    "- RS(52W) mạnh: Cổ phiếu mạnh hơn thị trường 20% trở lên, tín hiệu mua."
                )
            elif latest_rs >= 1.0:
                summary_points.append(
                    "- RS(52W) tích cực: Cổ phiếu mạnh hơn thị trường, có thể xem xét mua."
                )
            elif latest_rs <= 0.5:
                summary_points.append(
                    "- RS(52W) rất yếu: Cổ phiếu yếu hơn thị trường 50% trở lên, tín hiệu bán mạnh."
                )
            elif latest_rs <= 0.8:
                summary_points.append(
                    "- RS(52W) yếu: Cổ phiếu yếu hơn thị trường 20% trở lên, tín hiệu bán."
                )
            else:
                summary_points.append(
                    "- RS(52W) trung tính: Cổ phiếu có hiệu suất tương đương thị trường."
                )

            summary_parts.append("\n=== PHÂN TÍCH RS(52W) ===")
            summary_parts.append("\n".join(summary_points))
    except Exception as e:
        logging.warning(f"Could not calculate RS(52W) for {symbol}: {e}")

    # Join all parts with newlines
    return "\n".join(summary_parts)


def generate_ma_signals_table(
    current_price: float, ma_values: Dict[str, float]
) -> List[Dict[str, Any]]:
    """
    Generate a table of MA signals based on current price and MA values.

    Args:
        current_price (float): Current stock price
        ma_values (Dict[str, float]): Dictionary of MA values for different periods

    Returns:
        List[Dict[str, Any]]: List of MA signals with period, type, value and signal
    """
    signals = []
    ma_periods = [5, 10, 20, 50, 100, 200]

    for period in ma_periods:
        sma_key = f"sma{period}"
        ema_key = f"ema{period}"

        # Process SMA
        if sma_key in ma_values and ma_values[sma_key] is not None:
            sma_value = ma_values[sma_key]
            sma_signal = (
                "Mua"
                if current_price > sma_value
                else "Bán" if current_price < sma_value else "Trung tính"
            )
            signals.append(
                {
                    "period": period,
                    "type": "SMA",
                    "value": format_price(sma_value),
                    "signal": sma_signal,
                }
            )

        # Process EMA
        if ema_key in ma_values and ma_values[ema_key] is not None:
            ema_value = ma_values[ema_key]
            ema_signal = (
                "Mua"
                if current_price > ema_value
                else "Bán" if current_price < ema_value else "Trung tính"
            )
            signals.append(
                {
                    "period": period,
                    "type": "EMA",
                    "value": format_price(ema_value),
                    "signal": ema_signal,
                }
            )

    return signals


if __name__ == "__main__":
    args = parse_args()
    logging.debug(f"Args: {args}")
    main(
        deploy=args.deploy,
        analysis_date=args.date if args.date else datetime.now().strftime("%Y-%m-%d"),
    )
