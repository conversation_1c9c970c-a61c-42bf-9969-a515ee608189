import os
from datetime import datetime, timedelta

from stockpal.core.constants import Constants


class Utils:

    @staticmethod
    def get_history_time_ranges(
        span_in_seconds: int = 365 * 24 * 60 * 60,
    ) -> list[tuple]:
        # Define the start date as July 1, 2000, at 7 AM (GMT+7)
        start_ts = int(datetime(2000, 7, 1, 7).timestamp())  # Convert to epoch seconds

        # Last run at April 24, 2025, at 7 AM (GMT+7)
        last_run_ts = int(
            datetime(2025, 4, 24, 7).timestamp()
        )  # Convert to epoch seconds

        # Calculate the end time as 7 AM (GMT+7) of the current date plus 3 days
        now = datetime.now()
        end_time = datetime(now.year, now.month, now.day, 7) + timedelta(days=3)
        end_ts = int(end_time.timestamp())  # Convert to epoch seconds

        time_ranges = []

        # Part 1: From current run to the last run
        current_end = end_ts
        while current_end > last_run_ts:
            current_start = max(current_end - span_in_seconds, last_run_ts)
            time_ranges.append((current_start, current_end))
            current_end = current_start  # Move to the previous range

        # Part 2: From the last run to the start timestamp
        current_end = last_run_ts
        while current_end > start_ts:
            current_start = max(current_end - span_in_seconds, start_ts)
            time_ranges.append((current_start, current_end))
            current_end = current_start  # Move to the previous range

        for time_range in time_ranges:
            start_date = datetime.fromtimestamp(time_range[0]).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            end_date = datetime.fromtimestamp(time_range[1]).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            print(
                f"Time range: {time_range[0]} ({start_date}) to {time_range[1]} ({end_date})"
            )

        return time_ranges

    @staticmethod
    def get_last_trading_date() -> datetime:
        now = datetime.now().replace(hour=7, minute=0, second=0, microsecond=0)

        # Xác định ngày cần lấy dữ liệu
        if now.hour < 8:
            # Trước 8h sáng, lấy dữ liệu của ngày hôm trước do SSI reset yesterday intraday data at 8AM
            target_date = now - timedelta(days=1)
        else:
            # Sau 8h sáng, lấy dữ liệu của ngày hiện tại
            target_date = now

        # Kiểm tra và điều chỉnh target_date cho đến khi tìm được ngày giao dịch hợp lệ
        is_valid_trading_day = False
        while not is_valid_trading_day:
            # Kiểm tra nếu ngày đích là thứ 7 (weekday=5) hoặc chủ nhật (weekday=6)
            if target_date.weekday() >= 5:  # 5 = Thứ 7, 6 = Chủ nhật
                target_date = target_date - timedelta(days=1)
                continue

            # Kiểm tra nếu ngày đích là ngày lễ
            date_str = target_date.strftime("%Y-%m-%d")
            if date_str in Constants.HOLIDAYS:
                target_date = target_date - timedelta(days=1)
                continue

            # Nếu không phải cuối tuần và không phải ngày lễ, đây là ngày giao dịch hợp lệ
            is_valid_trading_day = True

        return target_date

    @staticmethod
    def is_trading_day(date: datetime) -> bool:
        return (
            date.weekday() < 5 and date.strftime("%Y-%m-%d") not in Constants.HOLIDAYS
        )

    @staticmethod
    def get_root_dir() -> str:
        # Get project root directory by finding the directory containing server/
        current_dir = os.path.dirname(os.path.abspath(__file__))
        while os.path.basename(current_dir) != "server_legacy":
            parent = os.path.dirname(current_dir)
            if parent == current_dir:  # Reached root without finding server/
                raise RuntimeError("Could not find project root directory")
            current_dir = parent
        return os.path.dirname(current_dir)

    @staticmethod
    def get_server_dir() -> str:
        return os.path.join(Utils.get_root_dir(), "server_legacy")

    @staticmethod
    def get_web_dir() -> str:
        web_dir = os.path.join(Utils.get_root_dir(), "web")
        os.makedirs(name=os.path.join(web_dir, "assets", "logos"), exist_ok=True)
        return web_dir

    @staticmethod
    def get_web2_dir() -> str:
        web_dir = os.path.join(Utils.get_root_dir(), "web2")
        os.makedirs(
            name=os.path.join(web_dir, "public", "assets", "logos"), exist_ok=True
        )
        os.makedirs(name=os.path.join(web_dir, "public", "analysis"), exist_ok=True)
        return web_dir

    @staticmethod
    def get_web_test_dir() -> str:
        web_dir = os.path.join(Utils.get_root_dir(), "web_test")
        os.makedirs(name=os.path.join(web_dir, "assets"), exist_ok=True)
        os.makedirs(name=os.path.join(web_dir, "analysis"), exist_ok=True)
        return web_dir

    @staticmethod
    def get_bidaskprice_dir() -> str:
        return os.path.join(
            os.path.dirname(Utils.get_root_dir()), "bidaskprice.github.io"
        )

    @staticmethod
    def get_db_dir() -> str:
        db_dir = os.path.join(Utils.get_server_dir(), "db")
        os.makedirs(name=os.path.join(db_dir, "xlsx"), exist_ok=True)
        return db_dir

    @staticmethod
    def get_cache_dir() -> str:
        cache_dir = os.path.join(Utils.get_server_dir(), "cache")
        os.makedirs(name=os.path.join(cache_dir, "logos"), exist_ok=True)
        return cache_dir
