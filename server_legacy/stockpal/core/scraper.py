from datetime import datetime, timedelta

from stockpal.core.util import Utils

from .stock import PriceData, Stock


class Scraper:

    def __init__(self, symbol: str):
        self._symbol = symbol.upper()
        self._cache_subfolder = symbol.upper()

    def prices(self, timeframe_in_minute: bool = False) -> list[PriceData]:
        raise NotImplementedError

    def events(self):
        raise NotImplementedError

    def quote(self):
        raise NotImplementedError

    def orders(self):
        raise NotImplementedError

    def fetch_stock_symbols(self) -> list[Stock]:
        raise NotImplementedError

    def _get_time_ranges(self, next_days: int = 0) -> list[tuple[int, int]]:
        # Opening date is 2000-07-01 07:00:00 GMT+7 and convert to epoch seconds
        opening = int(datetime(2000, 7, 1, 7).timestamp())
        # opening = int(datetime(2024, 1, 1, 7).timestamp())

        time_ranges: list[tuple[int, int]] = []

        # Part 1: From opening to 2001-01-01 07:00:00 GMT+7
        start = int(datetime(2001, 1, 1, 7).timestamp())
        # start = int(datetime(2025, 1, 1, 7).timestamp())
        time_ranges.append((opening, start))

        # Part 2: To 2025-04-25 07:00:00 GMT+7 with span of 1 year
        another = int(datetime(2025, 4, 25, 7).timestamp())
        while start < another:
            one_year_later = int(
                datetime.fromtimestamp(start)
                .replace(year=datetime.fromtimestamp(start).year + 1)
                .timestamp()
            )
            end = min(one_year_later, another)
            time_ranges.append((start, end))
            start = end

        # Part 3: To current with span of 1 day
        day_span: int | None = 24 * 60 * 60

        next_date = datetime.now() + timedelta(days=next_days)
        dest = int(
            next_date.replace(hour=7, minute=0, second=0, microsecond=0).timestamp()
        )
        while start < dest:
            end = min(start + day_span, dest)
            if Utils.is_trading_day(datetime.fromtimestamp(end)):
                time_ranges.append((start, end))
            start = end

        # for time_range in time_ranges:
        #     start_date = datetime.fromtimestamp(time_range[0]).strftime(
        #         "%Y-%m-%d %H:%M:%S"
        #     )
        #     end_date = datetime.fromtimestamp(time_range[1]).strftime(
        #         "%Y-%m-%d %H:%M:%S"
        #     )
        #     print(
        #         f"Time range: {time_range[0]} ({start_date}) to {time_range[1]} ({end_date})"
        #     )

        time_ranges.sort(key=lambda x: x[0], reverse=True)

        return time_ranges

    def _stop_scraping(self, start: int) -> bool:
        return start < (datetime.now() - timedelta(days=5)).timestamp()
