import json
import os
import random
import re
from typing import Any
from datetime import datetime, timedelta

import requests

from .util import Utils

from .constants import Constants

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Android 14; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/*********",
    "Mozilla/5.0 (Linux; Android 14) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.144 Mobile Safari/537.36",
]


class HttpCacheService:
    """Base class for HTTP services with caching functionality."""

    def __init__(
        self,
        headers: dict[str, str],
        cookies: dict[str, str] | None = None,
        cache_dir: str | None = None,
    ):
        self.cache_dir: str = cache_dir or Utils.get_cache_dir()
        os.makedirs(self.cache_dir, exist_ok=True)

        self.headers: dict[str, str] = headers
        if "User-Agent" not in self.headers:
            self.headers["User-Agent"] = random.choice(USER_AGENTS)

        self.cookies = cookies

    def get(
        self,
        url: str,
        use_cache: bool = True,
        cache_foldername: str | None = None,
        cache_filename: str | None = None,
        get_cache_result: bool = True,
    ):
        if use_cache and cache_filename is None:
            raise ValueError("cache_filename must be provided when use_cache is True")

        cache_file: str = os.path.join(
            self.cache_dir,
            cache_foldername if cache_foldername else "",
            re.sub(r"[^a-zA-Z0-9._-]", "_", cache_filename) if cache_filename else "",
        )

        # Create parent directory if it doesn't exist
        cache_dir: str = os.path.dirname(cache_file)
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir, exist_ok=True)

        if use_cache:
            if not get_cache_result:
                return None

            cached_data = self.load_cached_data(cache_file)
            if cached_data is not None:
                print(f"\U0001f31f Using cached data for {cache_filename}")
                return cached_data

        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()

            data = self.process_response(response)
            print(f"\u267b Fetched data for {cache_filename}")

            if data is not None:
                self.save_to_cache(cache_file, data)

            return data

        except requests.exceptions.RequestException as e:
            print(f"Error fetching data: {e}")
            return None

    def post1(self):
        # Define the API endpoint URL
        url = "https://finance.vietstock.vn/data/gettradingresult"

        # Define the headers
        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip",
            "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Host": "finance.vietstock.vn",
            "Origin": "https://finance.vietstock.vn",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "X-Requested-With": "XMLHttpRequest",
        }

        # Define the cookies
        cookies = {
            "__RequestVerificationToken": "QfeoSi7Yif5BhFXKLD3wmyl4TwMHiGub074idQIUdC-NWAdwIT6FFas8wdem-8qiWTSpqrIV4_1DuCGSvvRRzKMKGvVk19727kq1bnLGybQ1"
        }

        # Define the form data
        form_data = {
            "Code": "HPG",
            "OrderBy": "",
            "OrderDirection": "desc",
            "PageIndex": "1",
            "PageSize": "10",
            "FromDate": "2025-01-27",
            "ToDate": "2025-05-03",
            "ExportType": "default",
            "Cols": "TKLGD,TGTGD,VHTT,TGG,DC,TGPTG,KLGDKL,GTGDKL,Room,RoomCL,RoomCLPT,KL_M_GDKL,GT_M_GDKL,KL_B_GDKL,GT_B_GDKL,KL_M_GDTT,GT_M_GDTT,KL_B_GDTT,GT_B_GDTT,KL_M_TKL,GT_M_TGT,KL_B_TKL,GT_B_TGT",
            "ExchangeID": "1",
            "__RequestVerificationToken": "G7omdqRcXEdnU1Q0zrRyFEpGDTsGDbe_oTmLlsYW0ciuLGykgwn1-Duwe7HFaFLIEmQ09UQyYFFJ6eQiXPk7mGvtglz25H53D_Nufce4nD41",
        }

        # Create the request
        response = requests.post(url, headers=headers, cookies=cookies, data=form_data)

        # Print the response
        print(response.status_code)
        print(response.json())

    def post_form_data(
        self,
        url: str,
        form_data: dict[str, str],
        use_cache: bool = True,
        cache_foldername: str | None = None,
        cache_filename: str | None = None,
    ):
        if use_cache and cache_filename is None:
            raise ValueError("cache_filename must be provided when use_cache is True")

        cache_file: str = os.path.join(
            self.cache_dir,
            cache_foldername if cache_foldername else "",
            (re.sub(r"[^a-zA-Z0-9._-]", "_", cache_filename) if cache_filename else ""),
        )

        # Create parent directory if it doesn't exist
        cache_dir: str = os.path.dirname(cache_file)
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir, exist_ok=True)

        if use_cache:
            cached_data = self.load_cached_data(cache_file)
            if cached_data is not None:
                return cached_data

        try:
            response = requests.post(
                url, headers=self.headers, cookies=self.cookies, data=form_data
            )
            response.raise_for_status()

            result_data = self.process_response(response)

            if use_cache and result_data is not None:
                self.save_to_cache(cache_file, result_data)

            return result_data
        except requests.exceptions.RequestException as e:
            print(f"Error fetching data: {e}")
            return None

    def post(
        self,
        url: str,
        json_data: dict[str, Any] | None = None,
        use_cache: bool = True,
        cache_foldername: str | None = None,
        cache_filename: str | None = None,
    ):
        if use_cache and cache_filename is None:
            raise ValueError("cache_filename must be provided when use_cache is True")

        cache_file: str = os.path.join(
            self.cache_dir,
            cache_foldername if cache_foldername else "",
            (re.sub(r"[^a-zA-Z0-9._-]", "_", cache_filename) if cache_filename else ""),
        )

        # Create parent directory if it doesn't exist
        cache_dir: str = os.path.dirname(cache_file)
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir, exist_ok=True)

        if use_cache:
            cached_data = self.load_cached_data(cache_file)
            if cached_data is not None:
                return cached_data

        try:
            response = requests.post(url, json=json_data, headers=self.headers)
            response.raise_for_status()

            result_data = self.process_response(response)

            if use_cache and result_data is not None:
                self.save_to_cache(cache_file, result_data)

            return result_data
        except requests.exceptions.RequestException as e:
            print(f"Error fetching data: {e}")
            return None

    def _load_cached_data(self, cache_file: str) -> bytes | None:
        """Load cached data from file if it exists and is not expired."""
        if not os.path.exists(cache_file):
            return None
        with open(cache_file, "rb") as f:
            return f.read()

    def _save_to_cache(self, cache_file: str, data: Any) -> None:
        """Save data to cache file."""
        if isinstance(data, str):
            with open(cache_file, "w", encoding="utf-8") as f:
                _ = f.write(data)
        elif isinstance(data, bytes):
            with open(cache_file, "wb") as f:
                _ = f.write(data)
        else:
            with open(cache_file, "w", encoding="utf-8") as f:
                f.write(str(data))

    def process_response(self, response: requests.Response) -> Any:
        """Process the response. To be implemented by subclasses."""
        raise NotImplementedError

    def load_cached_data(self, cache_file: str):
        raise NotImplementedError

    def save_to_cache(self, cache_file: str, data: Any):
        raise NotImplementedError


class HttpJsonService(HttpCacheService):
    def __init__(
        self,
        headers: dict[str, str] | None = None,
        cookies: dict[str, str] | None = None,
        cache_dir: str | None = None,
    ):
        super().__init__(
            cache_dir=cache_dir,
            headers=({"Accept": "application/json"} if headers is None else headers),
            cookies=cookies,
        )

    def process_response(self, response: requests.Response) -> dict[str, Any]:
        return response.json()

    def load_cached_data(self, cache_file: str):
        data = self._load_cached_data(cache_file)
        if data is None:
            return None
        return json.loads(data)

    def save_to_cache(self, cache_file: str, data: Any):
        self._save_to_cache(cache_file, json.dumps(data))


class HttpXlsxService(HttpCacheService):
    def __init__(
        self, headers: dict[str, str] | None = None, cache_dir: str | None = None
    ):
        super().__init__(
            cache_dir=cache_dir,
            headers=({"Accept": "*/*"} if headers is None else headers),
        )

    def process_response(self, response: requests.Response) -> Any:
        return response.content

    def load_cached_data(self, cache_file: str):
        if not os.path.exists(cache_file):
            return None
        with open(cache_file, "rb") as f:
            return f.read()

    def save_to_cache(self, cache_file: str, data: Any):
        self._save_to_cache(cache_file, data)
