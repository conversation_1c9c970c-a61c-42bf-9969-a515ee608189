from typing import Literal

from stockpal.core.stock import DailyPrice, EventData, MinutePrice, PriceData, Stock

from .ssi_scraper import SsiScraper
from .vietstock_scraper import VietstockScraper
from .cafef_scraper import CafefScraper


class DataScraper:

    def __init__(self, symbol: str, provider: Literal["ssi", "vietstock", "cafef"] = "ssi"):
        self._symbol = symbol.upper()
        self._provider = provider

        if self._provider == "vietstock":
            self._scraper = VietstockScraper(symbol=self._symbol)
        elif self._provider == "cafef":
            self._scraper = CafefScraper(symbol=self._symbol)
        else:
            self._scraper = SsiScraper(symbol=self._symbol)

    def fetch_prices(self, timeframe_in_minute: bool = False) -> list[PriceData]:
        prices = self._scraper.prices(timeframe_in_minute)

        if timeframe_in_minute:
            return [MinutePrice(**price.__dict__) for price in prices]
        else:
            return [DailyPrice(**price.__dict__) for price in prices]

    def fetch_stock_symbols(self) -> list[Stock]:
        return self._scraper.fetch_stock_symbols()

    def events(self) -> list[EventData]:
        return self._scraper.events()

    def orders(self):
        return self._scraper.orders()
