from stockpal.core.stock import DailyPrice, EventData, MinutePrice, PriceData
from .data_storage import DataStorage
from datetime import datetime, timedelta
from typing import Optional


class DataReader:

    def __init__(self, symbol: str):
        self._symbol = symbol.upper()
        self._storage = DataStorage(symbol=self._symbol)

    def get_daily_prices(self, days: int, end_date: Optional[datetime] = None) -> list[PriceData]:
        """
        L<PERSON>y danh sách giá theo ng<PERSON>, kết thúc tại end_date (nếu có), ngược về trước days ngày.
        Args:
            days (int): S<PERSON> ngày cần lấy.
            end_date (datetime|None): <PERSON><PERSON><PERSON> kết thúc (mặc định là hôm nay).
        Returns:
            list[PriceData]: <PERSON>h sách giá.
        """
        prices = self._storage.get_prices(False, days, end_date=end_date)
        return [DailyPrice(**price.__dict__) for price in prices]

    def get_minute_prices(self, days: int) -> list[PriceData]:
        prices = self._storage.get_prices(True, days)
        return [MinutePrice(**price.__dict__) for price in prices]

    def get_events(self) -> list[EventData]:
        return self._scraper.events()

    def get_orders(self):
        return self._ssi.orders()
