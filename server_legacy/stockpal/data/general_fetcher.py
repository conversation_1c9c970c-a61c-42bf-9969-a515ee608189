import os
import time
from datetime import datetime

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from stockpal.core import Constants, HttpJsonService, Stock
from stockpal.core.util import Utils
from stockpal.db import SqliteService, SymbolDao


class GeneralDataFetcher:

    def __init__(self):
        self._cache_subfolder = None
        self._sql_service = SqliteService()
        self._symbol_dao = SymbolDao(self._sql_service)

    def fetch_all(self):
        self.__fetch_symbols_from_trading_view()

    def __fetch_symbols_from_trading_view(self) -> list[Stock]:
        stocks: list[Stock] = []

        http_service = HttpJsonService(
            headers={
                "Accept": "application/json",
                "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                "Content-Type": "text/plain;charset=UTF-8",
                "Origin": "https://vn.tradingview.com",
                "Referer": "https://vn.tradingview.com/",
            }
        )

        cache_filename = f"symbols_{datetime.now().strftime("%Y_%m_%d")}-tv.json"

        response = http_service.post(
            url="https://scanner.tradingview.com/vietnam/scan?label-product=screener-stock",
            cache_filename=cache_filename,
            json_data=tv_symbols_payload_request,
        )

        if not response or "data" not in response or not response["data"]:
            print(f"No data found for {cache_filename}")
            return []

        stocks.append(
            Stock(
                symbol="VNINDEX",
                name="VNINDEX",
                exchange="HOSE",
                industry="VNINDEX",
                sector="VNINDEX",
            )
        )

        for item in response["data"]:
            d = item["d"]
            symbol = d[0]
            logo_id = d[2]

            stocks.append(
                Stock(
                    symbol=symbol, name=d[1], exchange=d[5], industry=d[3], sector=d[4]
                )
            )

            if not logo_id:
                continue

            try:
                # Create directory for logos if it doesn't exist
                web_dir = os.path.join(Utils.get_web_dir(), "assets", "logos")
                os.makedirs(web_dir, exist_ok=True)

                # Create filename and path
                logo_filename = f"{symbol}.svg"
                logo_path = os.path.join(web_dir, logo_filename)

                # Check if the file already exists
                if os.path.exists(logo_path):
                    print(f"Logo for {symbol} already exists at {logo_path}")
                    continue

                # Create a session with retry capability
                session = requests.Session()
                retry_strategy = Retry(
                    total=3,  # Maximum number of retries
                    backoff_factor=1,  # Time factor between retries
                    status_forcelist=[
                        429,
                        500,
                        502,
                        503,
                        504,
                    ],  # Retry on these status codes
                )
                adapter = HTTPAdapter(max_retries=retry_strategy)
                session.mount("https://", adapter)
                session.mount("http://", adapter)

                # Download the logo with timeout
                try:
                    logo_resp = session.get(
                        f"https://s3-symbol-logo.tradingview.com/{logo_id}--big.svg",
                        timeout=(5, 10),  # (connect timeout, read timeout)
                    )
                    logo_resp.raise_for_status()

                    # Save logo
                    with open(logo_path, "wb") as f:
                        f.write(logo_resp.content)
                    print(f"Downloaded logo for {symbol} to {logo_path}")
                except requests.exceptions.Timeout:
                    print(f"Timeout while downloading logo for {symbol}")
                    # Continue to the next symbol
                    continue

            except requests.exceptions.RequestException as e:
                print(f"Failed to download logo for {symbol}: {e}")
                # Log more details for debugging
                if hasattr(e, "response") and e.response is not None:
                    print(f"  Status code: {e.response.status_code}")
                    print(
                        f"  Response: {e.response.text[:200]}..."
                    )  # Print first 200 chars
            except Exception as e:
                print(f"An error occurred while processing {symbol}: {e}")
                # Add a small delay before continuing to the next symbol
                time.sleep(0.5)

        self._symbol_dao.upsert(stocks)

        return stocks

    def __fetch_symbols_from_cafef(self) -> list[Stock]:
        stocks: list[Stock] = []
        http_service = HttpJsonService()

        has_next = True
        page_index = 0
        page_size = 2000

        while has_next:
            url = f"https://cafef.vn/du-lieu/ajax/pagenew/databusiness/congtyniemyet.ashx?centerid=0&skip={page_index * page_size}&take={page_size}&major=0"

            cache_filename = f"symbols_page_{page_index}-cafef.json"

            response = http_service.get(url=url, cache_filename=cache_filename)

            if not response or "Data" not in response or not response["Data"]:
                print(f"No data found for {cache_filename}")
                has_next = False
                break

            for item in response["Data"]:
                exchange_id = int(item.get("TradeCenterId", "0"))
                if exchange_id == Constants.VIETSTOCK_HNX_EXCHANGE_ID:
                    exchange = "HNX"
                elif exchange_id == Constants.VIETSTOCK_OTC_EXCHANGE_ID:
                    exchange = "OTC"
                elif exchange_id == Constants.VIETSTOCK_UPCOM_EXCHANGE_ID:
                    exchange = "UPCOM"
                else:
                    exchange = "HOSE"
                stocks.append(
                    Stock(
                        symbol=item.get("Symbol", ""),
                        name=item.get("CompanyName", ""),
                        exchange=exchange,
                        industry=item.get("CategoryName", ""),
                    )
                )

            page_index += 1

        return stocks


tv_symbols_payload_request = {
    "columns": [
        "name",
        "description",
        "logoid",
        "industry.tr",
        "sector.tr",
        "exchange.tr",
        "indexes.tr",
        "market",
        "recommendation_mark",
        "price_target_1y",
        "price_target_1y_delta",
    ],
    "filter": [
        {"left": "volume|1W", "operation": "greater", "right": 10000},
        {"left": "exchange", "operation": "in_range", "right": ["HOSE"]},
        {"left": "is_primary", "operation": "equal", "right": True},
    ],
    "ignore_unknown_fields": False,
    "options": {"lang": "vi"},
    "range": [0, 2000],
    "sort": {"sortBy": "volume", "sortOrder": "desc"},
    "symbols": {},
    "markets": ["vietnam"],
    "filter2": {
        "operator": "and",
        "operands": [
            {
                "operation": {
                    "operator": "or",
                    "operands": [
                        {
                            "operation": {
                                "operator": "and",
                                "operands": [
                                    {
                                        "expression": {
                                            "left": "type",
                                            "operation": "equal",
                                            "right": "stock",
                                        }
                                    },
                                    {
                                        "expression": {
                                            "left": "typespecs",
                                            "operation": "has",
                                            "right": ["common"],
                                        }
                                    },
                                ],
                            }
                        }
                    ],
                }
            },
            {
                "operation": {
                    "operator": "or",
                    "operands": [
                        {
                            "operation": {
                                "operator": "and",
                                "operands": [
                                    {
                                        "expression": {
                                            "left": "type",
                                            "operation": "equal",
                                            "right": "stock",
                                        }
                                    },
                                    {
                                        "expression": {
                                            "left": "typespecs",
                                            "operation": "has",
                                            "right": ["common"],
                                        }
                                    },
                                ],
                            }
                        },
                        {
                            "operation": {
                                "operator": "and",
                                "operands": [
                                    {
                                        "expression": {
                                            "left": "type",
                                            "operation": "equal",
                                            "right": "stock",
                                        }
                                    },
                                    {
                                        "expression": {
                                            "left": "typespecs",
                                            "operation": "has",
                                            "right": ["preferred"],
                                        }
                                    },
                                ],
                            }
                        },
                        {
                            "operation": {
                                "operator": "and",
                                "operands": [
                                    {
                                        "expression": {
                                            "left": "type",
                                            "operation": "equal",
                                            "right": "dr",
                                        }
                                    }
                                ],
                            }
                        },
                        {
                            "operation": {
                                "operator": "and",
                                "operands": [
                                    {
                                        "expression": {
                                            "left": "type",
                                            "operation": "equal",
                                            "right": "fund",
                                        }
                                    },
                                    {
                                        "expression": {
                                            "left": "typespecs",
                                            "operation": "has_none_of",
                                            "right": ["etf"],
                                        }
                                    },
                                ],
                            }
                        },
                    ],
                }
            },
        ],
    },
}
