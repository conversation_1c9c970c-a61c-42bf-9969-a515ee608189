from stockpal.core import PriceData
from stockpal.indicator.ma import MovingAverage
from .base import BaseIndicator
from typing import List, Dict, Any

"""
This implementation of the Moving Average Convergence Divergence (MACD) indicator includes:

1. A calculate() method that returns:
- MACD line (difference between fast and slow EMAs)
- Signal line (EMA of the MACD line)
- Histogram (difference between MACD line and signal line)
2. Default parameters:
- Fast EMA period: 12
- Slow EMA period: 26
- Signal line period: 9
3. A get_crossover_signals() method that identifies:
- Bullish crossover: MACD line crosses above signal line
- Bearish crossover: MACD line crosses below signal line
4. A get_zero_line_signals() method that identifies:
- Bullish zero line cross: MACD line crosses above zero
- Bearish zero line cross: MACD line crosses below zero
5. A get_divergence_signals() method that identifies:
- Bearish divergence: Price makes higher high but MACD makes lower high
- Bullish divergence: Price makes lower low but MACD makes higher low
6. Proper handling of the initial period values with None values

The MACD indicator is a versatile tool that combines trend-following and momentum elements. It's particularly useful for identifying potential entry and exit points, trend direction, and momentum shifts.

Tín hiệu lực mua/bán của MACD:
- Lực mua tăng:
  + MACD line cắt lên trên Signal line (bullish crossover)
  + Histogram chuyển từ âm sang dương
  + Hành động: Cân nhắc mua
- Lực bán tăng:
  + MACD line cắt xuống dưới Signal line (bearish crossover)
  + Histogram chuyển từ dương sang âm
  + Hành động: Cân nhắc bán, giảm vị thế mua
- Phân kỳ giảm dần (MACD tạo đỉnh thấp hơn trong khi giá tạo đỉnh cao hơn):
  + Hành động: Cảnh báo sớm về khả năng đảo chiều giảm
"""


class MACD(BaseIndicator):
    """
    Moving Average Convergence Divergence (MACD) indicator implementation.
    Inherits from BaseIndicator and provides calculation, signal, trend, and recommendation methods.
    """
    def __init__(
        self,
        symbol: str,
        prices: List[PriceData],
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
    ):
        super().__init__(symbol, prices, fast_period=fast_period, slow_period=slow_period, signal_period=signal_period)
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period

    def calculate(self) -> Dict[str, List[Any]]:
        """
        Calculate MACD, Signal Line, and Histogram values.
        Returns:
            dict: Dictionary containing MACD line, signal line, and histogram values
        """
        if len(self.prices) <= self.slow_period:
            return {
                "macd_line": [None] * len(self.prices),
                "signal_line": [None] * len(self.prices),
                "histogram": [None] * len(self.prices),
            }
        fast_ma = MovingAverage(
            symbol=self.symbol,
            prices=self.prices,
            period=self.fast_period,
            ma_type="exponential",
        )
        fast_ema = fast_ma.calculate()
        slow_ma = MovingAverage(
            symbol=self.symbol,
            prices=self.prices,
            period=self.slow_period,
            ma_type="exponential",
        )
        slow_ema = slow_ma.calculate()
        macd_line = []
        for i in range(len(self.prices)):
            if i < self.slow_period - 1 or fast_ema[i] is None or slow_ema[i] is None:
                macd_line.append(None)
            else:
                macd_line.append(fast_ema[i] - slow_ema[i])
        macd_prices = []
        for i, price in enumerate(self.prices):
            if macd_line[i] is not None:
                macd_price = PriceData(
                    symbol=price.symbol,
                    timestamp=price.timestamp,
                    open_price=macd_line[i],
                    highest_price=macd_line[i],
                    lowest_price=macd_line[i],
                    close_price=macd_line[i],
                    match_volume=price.match_volume,
                )
                macd_prices.append(macd_price)
        signal_ma = MovingAverage(
            symbol=self.symbol,
            prices=macd_prices,
            period=self.signal_period,
            ma_type="exponential",
        )
        signal_values = signal_ma.calculate()
        signal_line = [None] * (len(self.prices) - len(signal_values))
        signal_line.extend(signal_values)
        histogram = []
        for i in range(len(self.prices)):
            if macd_line[i] is None or signal_line[i] is None:
                histogram.append(None)
            else:
                histogram.append(macd_line[i] - signal_line[i])
        return {
            "macd_line": macd_line,
            "signal_line": signal_line,
            "histogram": histogram,
        }

    def get_crossover_signals(self) -> list[dict]:
        """
        Identify crossover signals between MACD line and Signal line.

        Returns:
            list[dict]: List of crossover signals with timestamps
        """
        result = self.calculate()
        macd_line = result["macd_line"]
        signal_line = result["signal_line"]

        signals = []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Find crossovers (need at least 2 valid values)
        for i in range(1, len(sorted_prices)):
            # Skip if we don't have valid values
            if (
                macd_line[i] is None
                or signal_line[i] is None
                or macd_line[i - 1] is None
                or signal_line[i - 1] is None
            ):
                continue

            # Bullish crossover: MACD line crosses above Signal line
            if macd_line[i - 1] <= signal_line[i - 1] and macd_line[i] > signal_line[i]:
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": sorted_prices[i].close_price,
                        "signal": "bullish_crossover",
                        "macd": macd_line[i],
                        "signal_line": signal_line[i],
                        "histogram": macd_line[i] - signal_line[i],
                    }
                )

            # Bearish crossover: MACD line crosses below Signal line
            elif (
                macd_line[i - 1] >= signal_line[i - 1] and macd_line[i] < signal_line[i]
            ):
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": sorted_prices[i].close_price,
                        "signal": "bearish_crossover",
                        "macd": macd_line[i],
                        "signal_line": signal_line[i],
                        "histogram": macd_line[i] - signal_line[i],
                    }
                )

        return signals

    def get_zero_line_signals(self) -> list[dict]:
        """
        Identify signals when MACD line crosses the zero line.

        Returns:
            list[dict]: List of zero line crossover signals
        """
        result = self.calculate()
        macd_line = result["macd_line"]

        signals = []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Find zero line crossovers
        for i in range(1, len(sorted_prices)):
            # Skip if we don't have valid values
            if macd_line[i] is None or macd_line[i - 1] is None:
                continue

            # Bullish: MACD crosses above zero line
            if macd_line[i - 1] <= 0 and macd_line[i] > 0:
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": sorted_prices[i].close_price,
                        "signal": "zero_line_bullish",
                        "macd": macd_line[i],
                    }
                )

            # Bearish: MACD crosses below zero line
            elif macd_line[i - 1] >= 0 and macd_line[i] < 0:
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": sorted_prices[i].close_price,
                        "signal": "zero_line_bearish",
                        "macd": macd_line[i],
                    }
                )

        return signals

    def get_divergence_signals(self) -> list[dict]:
        """
        Identify potential divergence signals between price and MACD.

        Returns:
            list[dict]: List of divergence signals
        """
        result = self.calculate()
        macd_line = result["macd_line"]

        signals = []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Need at least 10 valid values to look for divergence
        valid_indices = [
            i
            for i in range(len(sorted_prices))
            if i >= self.slow_period and macd_line[i] is not None
        ]

        if len(valid_indices) < 10:
            return signals

        # Look for local highs and lows in price and MACD
        for i in range(2, len(valid_indices) - 2):
            idx = valid_indices[i]
            prev_idx = valid_indices[i - 2]

            # Check if we have a price high but MACD lower high (bearish divergence)
            if (
                sorted_prices[idx].close_price > sorted_prices[prev_idx].close_price
                and macd_line[idx] < macd_line[prev_idx]
                and macd_line[idx] > 0
            ):  # MACD should be positive
                signals.append(
                    {
                        "timestamp": sorted_prices[idx].timestamp,
                        "price": sorted_prices[idx].close_price,
                        "signal": "bearish_divergence",
                        "macd": macd_line[idx],
                    }
                )

            # Check if we have a price low but MACD higher low (bullish divergence)
            elif (
                sorted_prices[idx].close_price < sorted_prices[prev_idx].close_price
                and macd_line[idx] > macd_line[prev_idx]
                and macd_line[idx] < 0
            ):  # MACD should be negative
                signals.append(
                    {
                        "timestamp": sorted_prices[idx].timestamp,
                        "price": sorted_prices[idx].close_price,
                        "signal": "bullish_divergence",
                        "macd": macd_line[idx],
                    }
                )

        return signals

    def get_signals(self) -> List[Dict[str, Any]]:
        """
        Generate trading signals based on MACD crossovers and histogram.
        Returns:
            List[Dict]: List of signal dictionaries
        """
        result = self.calculate()
        macd_line = result["macd_line"]
        signal_line = result["signal_line"]
        histogram = result["histogram"]
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        for i in range(1, len(sorted_prices)):
            if (
                macd_line[i] is None
                or signal_line[i] is None
                or macd_line[i - 1] is None
                or signal_line[i - 1] is None
            ):
                continue
            signal_type = ""
            action = ""
            if macd_line[i - 1] <= signal_line[i - 1] and macd_line[i] > signal_line[i]:
                signal_type = "bullish_crossover"
                action = "MACD bullish crossover, consider buying."
            elif macd_line[i - 1] >= signal_line[i - 1] and macd_line[i] < signal_line[i]:
                signal_type = "bearish_crossover"
                action = "MACD bearish crossover, consider selling."
            elif histogram[i] is not None and histogram[i - 1] is not None:
                if histogram[i - 1] < 0 and histogram[i] > 0:
                    signal_type = "histogram_bullish"
                    action = "MACD histogram turns positive, momentum increasing."
                elif histogram[i - 1] > 0 and histogram[i] < 0:
                    signal_type = "histogram_bearish"
                    action = "MACD histogram turns negative, momentum decreasing."
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "macd": macd_line[i],
                    "signal_line": signal_line[i],
                    "histogram": histogram[i],
                    "signal": signal_type,
                    "action": action
                })
        return signals

    def predict_trend(self) -> Dict[str, Any]:
        """
        Predict the trend based on the latest MACD and signal line values.
        Returns:
            Dict[str, Any]: Trend direction and confidence
        """
        result = self.calculate()
        macd_line = result["macd_line"]
        signal_line = result["signal_line"]
        latest_macd = next((v for v in reversed(macd_line) if v is not None), 0.0)
        latest_signal = next((v for v in reversed(signal_line) if v is not None), 0.0)
        if latest_macd > latest_signal:
            return {"trend": "uptrend", "confidence": min(1.0, abs(latest_macd - latest_signal) / 2)}
        elif latest_macd < latest_signal:
            return {"trend": "downtrend", "confidence": min(1.0, abs(latest_macd - latest_signal) / 2)}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        """
        Generate a recommendation (Buy/Sell/Hold) based on the latest MACD and signal line values.
        Returns:
            str: Recommendation string
        """
        result = self.calculate()
        macd_line = result["macd_line"]
        signal_line = result["signal_line"]
        latest_macd = next((v for v in reversed(macd_line) if v is not None), 0.0)
        latest_signal = next((v for v in reversed(signal_line) if v is not None), 0.0)
        if latest_macd > latest_signal:
            return "Buy (MACD Bullish Crossover)"
        elif latest_macd < latest_signal:
            return "Sell (MACD Bearish Crossover)"
        else:
            return "Hold (MACD Neutral)"
