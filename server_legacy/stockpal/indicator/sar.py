from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

"""
Parabolic SAR (Stop and Reverse) là chỉ báo theo xu hướng cho thấy điểm có khả năng đảo chiều của giá.
Chỉ báo này hiển thị dưới dạng điểm trên hoặc dưới đường giá.

Tín hiệu lực mua/bán của Parabolic SAR:
- Xu hướng tăng: Điểm SAR xuất hiện dưới giá
  + Hành động: Cân nhắc mua và giữ cho đến khi xuất hiện tín hiệu đảo chiều
- Xu hướng giảm: Điểm SAR xuất hiện trên giá
  + Hành động: Cân nhắc bán và đứng ngoài cho đến khi xuất hiện tín hiệu đảo chiều
- <PERSON><PERSON><PERSON> chiều xu hướng: Điểm SAR chuyển từ trên giá xuống dưới giá (tăng) hoặc từ dưới giá lên trên giá (giảm)
  + Hành động: Tín hiệu mua/bán mạnh, cân nhắc mở vị thế mới
"""

class ParabolicSAR(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], step: float = 0.02, max_step: float = 0.2):
        super().__init__(symbol, prices, step=step, max_step=max_step)
        self.step = step
        self.max_step = max_step

    def calculate(self) -> List[float]:
        """
        Tính toán chỉ báo Parabolic SAR (Stop And Reverse).
        
        Parabolic SAR là một chỉ báo kỹ thuật được sử dụng để xác định xu hướng của thị trường
        và điểm có khả năng đảo chiều xu hướng.
        
        Returns:
            list[float]: Danh sách giá trị SAR
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        if len(sorted_prices) < 2:
            return [None] * len(sorted_prices)
            
        # Xác định xu hướng ban đầu
        if sorted_prices[1].close_price > sorted_prices[0].close_price:
            trend = 1  # 1: xu hướng tăng
        else:
            trend = -1  # -1: xu hướng giảm
            
        # Khởi tạo các giá trị ban đầu
        sar_values = [None]  # Giá trị SAR đầu tiên là None
        ep = sorted_prices[0].highest_price if trend == 1 else sorted_prices[0].lowest_price  # Extreme Point
        af = self.step  # Acceleration Factor
        
        # Giá trị SAR ban đầu
        if trend == 1:
            sar_value = sorted_prices[0].lowest_price  # Xu hướng tăng, SAR bắt đầu từ giá thấp nhất
        else:
            sar_value = sorted_prices[0].highest_price  # Xu hướng giảm, SAR bắt đầu từ giá cao nhất
            
        for i in range(1, len(sorted_prices)):
            prev_sar = sar_value  # Lưu giá trị SAR trước đó
            curr = sorted_prices[i]
            prev = sorted_prices[i-1]
            
            # Cập nhật SAR cho chu kỳ hiện tại
            sar_value = prev_sar + af * (ep - prev_sar)
            
            # Kiểm tra và điều chỉnh SAR nếu cần (để đảm bảo nó không cắt qua giá)
            if trend == 1:  # Xu hướng tăng
                # SAR không được lớn hơn giá thấp nhất của 2 nến trước
                sar_value = min(sar_value, min(prev.lowest_price, 
                                               sorted_prices[i-2].lowest_price if i >= 2 else prev.lowest_price))
                
                # Kiểm tra đảo chiều
                if curr.lowest_price < sar_value:
                    # Đảo chiều từ tăng sang giảm
                    trend = -1
                    sar_value = max(ep, curr.highest_price)  # Reset SAR
                    ep = curr.lowest_price  # Reset Extreme Point
                    af = self.step  # Reset Acceleration Factor
                else:
                    # Tiếp tục xu hướng tăng
                    if curr.highest_price > ep:
                        ep = curr.highest_price  # Cập nhật Extreme Point
                        af = min(af + self.step, self.max_step)  # Tăng Acceleration Factor
            else:  # Xu hướng giảm
                # SAR không được nhỏ hơn giá cao nhất của 2 nến trước
                sar_value = max(sar_value, max(prev.highest_price, 
                                               sorted_prices[i-2].highest_price if i >= 2 else prev.highest_price))
                
                # Kiểm tra đảo chiều
                if curr.highest_price > sar_value:
                    # Đảo chiều từ giảm sang tăng
                    trend = 1
                    sar_value = min(ep, curr.lowest_price)  # Reset SAR
                    ep = curr.highest_price  # Reset Extreme Point
                    af = self.step  # Reset Acceleration Factor
                else:
                    # Tiếp tục xu hướng giảm
                    if curr.lowest_price < ep:
                        ep = curr.lowest_price  # Cập nhật Extreme Point
                        af = min(af + self.step, self.max_step)  # Tăng Acceleration Factor
                        
            # Thêm giá trị SAR đã làm tròn vào danh sách kết quả
            sar_values.append(round(sar_value, 2))
            
        return sar_values

    def get_signals(self) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị Parabolic SAR.
        
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        sar_values = self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(1, len(sorted_prices)):
            if i >= len(sar_values) or sar_values[i] is None or i-1 >= len(sar_values) or sar_values[i-1] is None:
                continue
                
            prev_close = sorted_prices[i-1].close_price
            curr_close = sorted_prices[i].close_price
            prev_sar = sar_values[i-1]
            curr_sar = sar_values[i]
            
            signal_type = ""
            buying_power = ""
            action = ""
                
            # Đảo chiều từ giảm sang tăng (SAR từ trên giá xuống dưới giá)
            if prev_close < prev_sar and curr_close > curr_sar:
                signal_type = "bullish_reversal"
                buying_power = "Lực mua tăng mạnh, chuyển từ xu hướng giảm sang tăng"
                action = "Tín hiệu mua mạnh, xem xét mở vị thế mua"
                
            # Đảo chiều từ tăng sang giảm (SAR từ dưới giá lên trên giá)
            elif prev_close > prev_sar and curr_close < curr_sar:
                signal_type = "bearish_reversal"
                buying_power = "Lực bán tăng mạnh, chuyển từ xu hướng tăng sang giảm"
                action = "Tín hiệu bán mạnh, xem xét đóng vị thế mua hoặc mở vị thế bán"
                
            # Tiếp tục xu hướng tăng (SAR vẫn dưới giá)
            elif curr_close > curr_sar:
                signal_type = "uptrend"
                buying_power = "Lực mua duy trì, xu hướng tăng tiếp tục"
                action = "Giữ vị thế mua hiện tại, có thể tăng thêm nếu phù hợp"
                
            # Tiếp tục xu hướng giảm (SAR vẫn trên giá)
            elif curr_close < curr_sar:
                signal_type = "downtrend"
                buying_power = "Lực bán duy trì, xu hướng giảm tiếp tục"
                action = "Giữ vị thế bán hiện tại, tránh mua vào lúc này"
                
            signals.append({
                "timestamp": sorted_prices[i].timestamp,
                "price": curr_close,
                "value": curr_sar,
                "signal": signal_type,
                "buying_power": buying_power,
                "action": action
            })
            
        return signals 

    def predict_trend(self) -> Dict[str, Any]:
        sar_values = self.calculate()
        latest_sar = next((v for v in reversed(sar_values) if v is not None), 0.0)
        if latest_sar < 0:
            return {"trend": "uptrend", "confidence": 1.0}
        elif latest_sar > 0:
            return {"trend": "downtrend", "confidence": 1.0}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        sar_values = self.calculate()
        latest_sar = next((v for v in reversed(sar_values) if v is not None), 0.0)
        if latest_sar < 0:
            return "Buy (SAR Below Price)"
        elif latest_sar > 0:
            return "Sell (SAR Above Price)"
        else:
            return "Hold (SAR Neutral)" 