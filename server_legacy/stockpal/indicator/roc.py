from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

"""
Rate of Change (ROC) là chỉ báo đo lường tốc độ thay đổi của giá theo thời gian,
gi<PERSON>p xác định xu hướng và điểm đảo chiều tiềm năng.

Tín hiệu lực mua/bán của ROC:
- Lực mua cao: ROC > 0 (giá hiện tại cao hơn giá N chu kỳ trước)
  + Hành động: Cân nhắc mua hoặc giữ
- Lực bán cao: ROC < 0 (gi<PERSON> hiện tại thấp hơn giá N chu kỳ trước)
  + Hành động: Cân nhắc bán hoặc giảm vị thế
- Tăng trưởng tích cực: ROC tăng từ âm sang dương
  + Hành động: Tín hiệu mua tiềm năng
"""

class RateOfChange(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        super().__init__(symbol, prices, period=period)
        self.period = period

    def calculate(self) -> List[float]:
        """
        Tính toán chỉ báo ROC (Rate of Change).
        
        ROC là chỉ báo động lượng so sánh giá hiện tại với giá N chu kỳ trước đó.
        Công thức: ROC = ((Giá hiện tại - Giá N chu kỳ trước) / Giá N chu kỳ trước) * 100
        
        Returns:
            list[float]: Danh sách giá trị ROC
        """
        if len(self.prices) <= self.period:
            return [None] * len(self.prices)
            
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        close_prices = [p.close_price for p in sorted_prices]
        
        # Tạo danh sách kết quả với period giá trị None đầu tiên
        roc_values = [None] * self.period
        
        # Tính ROC cho mỗi điểm giá
        for i in range(self.period, len(sorted_prices)):
            current_price = close_prices[i]
            prev_price = close_prices[i - self.period]
            
            if prev_price == 0:
                roc = 0.0
            else:
                # ROC = ((Current Price - Price n periods ago) / Price n periods ago) * 100
                roc = ((current_price - prev_price) / prev_price) * 100
            
            # Làm tròn đến 2 chữ số thập phân
            roc_values.append(round(roc, 2))
            
        return roc_values

    def get_signals(self) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị ROC.
        
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị ROC và hành động gợi ý
        """
        roc_values = self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(1, len(sorted_prices)):
            if i >= len(roc_values) or roc_values[i] is None or (i > 0 and i-1 >= len(roc_values)) or (i > 0 and roc_values[i-1] is None):
                continue
                
            roc = roc_values[i]
            prev_roc = roc_values[i-1] if i > 0 else 0
            
            signal_type = ""
            buying_power = ""
            action = ""
                
            # Xác định tín hiệu dựa trên giá trị ROC và sự thay đổi của nó
            if roc > 5:  # ROC rất dương - tăng mạnh
                signal_type = "strong_bullish"
                buying_power = "Lực mua mạnh, tốc độ tăng cao"
                action = "Xem xét mua hoặc giữ vị thế hiện tại"
                
            elif roc > 0:  # ROC dương - tăng nhẹ
                signal_type = "bullish"
                buying_power = "Lực mua nhẹ, xu hướng tăng"
                action = "Có thể mua nếu phù hợp với chiến lược"
                
            elif roc < -5:  # ROC rất âm - giảm mạnh
                signal_type = "strong_bearish"
                buying_power = "Lực bán mạnh, tốc độ giảm cao"
                action = "Xem xét bán hoặc tránh mua vào"
                
            elif roc < 0:  # ROC âm - giảm nhẹ
                signal_type = "bearish"
                buying_power = "Lực bán nhẹ, xu hướng giảm"
                action = "Thận trọng khi mua vào"
                
            else:  # ROC = 0
                signal_type = "neutral"
                buying_power = "Thị trường cân bằng"
                action = "Đợi tín hiệu rõ ràng hơn"
            
            # Phát hiện đảo chiều
            if prev_roc < 0 and roc > 0:
                signal_type = "reversal_bullish"
                buying_power = "Thay đổi từ lực bán sang lực mua"
                action = "Tín hiệu mua tiềm năng"
                
            elif prev_roc > 0 and roc < 0:
                signal_type = "reversal_bearish"  
                buying_power = "Thay đổi từ lực mua sang lực bán"
                action = "Tín hiệu bán tiềm năng"
                
            signals.append({
                "timestamp": sorted_prices[i].timestamp,
                "price": sorted_prices[i].close_price,
                "value": roc,
                "signal": signal_type,
                "buying_power": buying_power,
                "action": action
            })
            
        return signals 

    def predict_trend(self) -> Dict[str, Any]:
        roc_values = self.calculate()
        latest_roc = next((v for v in reversed(roc_values) if v is not None), 0.0)
        if latest_roc > 0:
            return {"trend": "uptrend", "confidence": min(1.0, abs(latest_roc)/10)}
        elif latest_roc < 0:
            return {"trend": "downtrend", "confidence": min(1.0, abs(latest_roc)/10)}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        roc_values = self.calculate()
        latest_roc = next((v for v in reversed(roc_values) if v is not None), 0.0)
        if latest_roc > 0:
            return "Buy (ROC Positive)"
        elif latest_roc < 0:
            return "Sell (ROC Negative)"
        else:
            return "Hold (ROC Neutral)" 