from stockpal.core import PriceData
from .base import BaseIndicator
from typing import List, Dict, Any

"""
This implementation of the Commodity Channel Index (CCI) indicator includes:

1. A calculate() method that returns CCI values for each price point
2. Default parameters:
- Period: 20 (standard setting)
- Constant: 0.015 (standard multiplier)
3. A get_signals() method that identifies:
- Overbought and oversold conditions (default thresholds at +100 and -100)
- Zero line crossovers (bullish and bearish)
- Entries and exits from overbought/oversold zones
4. A get_divergence_signals() method that identifies:
- Bearish divergence: Price makes higher high but CCI makes lower high
- Bullish divergence: Price makes lower low but CCI makes higher low
5. Proper handling of the initial period values with None values

The CCI is a versatile oscillator that measures the current price level relative to an average price level over a given period. It's particularly useful for identifying overbought and oversold conditions, trend strength, and potential reversals.

Tín hiệu lực mua/bán của CCI:
- Dải giá trị: Thường dao động từ -300 đến +300
- Lực mua cao: CCI > 100
  + Hành động: <PERSON><PERSON><PERSON> giác với khả năng đảo chiều, cân nhắc chốt lời
- <PERSON><PERSON><PERSON> bán cao: CCI < -100
  + <PERSON>ành động: Cơ hội mua tiềm năng, theo dõi tín hiệu xác nhận
- Tín hiệu mua mạnh: CCI vượt qua mức -100 từ dưới lên
  + Hành động: Cân nhắc mua với xác suất thành công cao
- Tín hiệu bán mạnh: CCI giảm xuống dưới mức +100 từ trên xuống
  + Hành động: Cân nhắc bán với xác suất thành công cao
"""


class CommodityChannelIndex(BaseIndicator):
    def __init__(
        self,
        symbol: str,
        prices: List[PriceData],
        period: int = 20,
        constant: float = 0.015,
    ):
        super().__init__(symbol, prices, period=period, constant=constant)
        self.period = period
        self.constant = constant  # Default constant is 0.015

    def calculate(self) -> List[float]:
        """
        Calculate CCI values for the price data.

        Returns:
            list[float]: List of CCI values corresponding to each price point
        """
        if len(self.prices) <= self.period:
            return [0.0] * len(self.prices)  # Default value when not enough data

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Initialize CCI list with None values for the first (period-1) elements
        cci_values = [None] * (self.period - 1)

        for i in range(self.period - 1, len(sorted_prices)):
            # Get window of prices
            window = sorted_prices[i - (self.period - 1) : i + 1]

            # Calculate typical price for each candle in the window
            typical_prices = [
                (price.highest_price + price.lowest_price + price.close_price) / 3
                for price in window
            ]

            # Calculate simple moving average of typical prices
            sma_tp = sum(typical_prices) / self.period

            # Calculate mean deviation
            mean_deviation = (
                sum(abs(tp - sma_tp) for tp in typical_prices) / self.period
            )

            # Calculate CCI
            if mean_deviation == 0:
                # Avoid division by zero
                cci_values.append(0)
            else:
                current_tp = (
                    sorted_prices[i].highest_price
                    + sorted_prices[i].lowest_price
                    + sorted_prices[i].close_price
                ) / 3
                cci = (current_tp - sma_tp) / (self.constant * mean_deviation)
                cci_values.append(cci)

        return cci_values

    def get_signals(
        self, overbought: float = 100, oversold: float = -100
    ) -> List[Dict[str, Any]]:
        """
        Xác định tín hiệu dựa trên giá trị CCI.
        
        Args:
            overbought (float): Ngưỡng quá mua (mặc định 100)
            oversold (float): Ngưỡng quá bán (mặc định -100)
        
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị CCI và hành động gợi ý
        """
        cci_values = self.calculate()
        signals = []
        
        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        # Xác định tín hiệu (cần ít nhất 2 giá trị hợp lệ)
        for i in range(1, len(sorted_prices)):
            # Bỏ qua nếu không có giá trị CCI hợp lệ
            if i >= len(cci_values) or cci_values[i] is None or cci_values[i-1] is None:
                continue
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Điều kiện quá mua
            if cci_values[i] > overbought:
                # Vào vùng quá mua
                if cci_values[i-1] <= overbought:
                    signal_type = "enter_overbought"
                    buying_power = "Lực mua đang tăng mạnh vào vùng quá mua"
                    action = "Thận trọng, chuẩn bị cho tín hiệu bán"
                # Đã trong vùng quá mua
                else:
                    signal_type = "overbought"
                    buying_power = "Lực mua cao, có thể xuất hiện đảo chiều"
                    action = "Cảnh giác với khả năng đảo chiều, cân nhắc chốt lời"
            
            # Điều kiện quá bán
            elif cci_values[i] < oversold:
                # Vào vùng quá bán
                if cci_values[i-1] >= oversold:
                    signal_type = "enter_oversold"
                    buying_power = "Lực bán đang tăng mạnh vào vùng quá bán"
                    action = "Theo dõi, chuẩn bị cho tín hiệu mua"
                # Đã trong vùng quá bán
                else:
                    signal_type = "oversold"
                    buying_power = "Lực bán cao, có thể xuất hiện đảo chiều"
                    action = "Cơ hội mua tiềm năng, theo dõi tín hiệu xác nhận"
            
            # Cắt qua đường 0 từ dưới lên (tín hiệu tăng)
            elif cci_values[i-1] < 0 and cci_values[i] > 0:
                signal_type = "bullish_zero_cross"
                buying_power = "Chuyển từ lực bán sang lực mua"
                action = "Tín hiệu mua, đặc biệt nếu đang trong xu hướng tăng"
            
            # Cắt qua đường 0 từ trên xuống (tín hiệu giảm)
            elif cci_values[i-1] > 0 and cci_values[i] < 0:
                signal_type = "bearish_zero_cross"
                buying_power = "Chuyển từ lực mua sang lực bán"
                action = "Tín hiệu bán, đặc biệt nếu đang trong xu hướng giảm"
            
            # Thoát khỏi vùng quá mua (tín hiệu bán tiềm năng)
            elif cci_values[i-1] > overbought and cci_values[i] <= overbought:
                signal_type = "exit_overbought"
                buying_power = "Lực mua đang suy yếu từ vùng quá mua"
                action = "Tín hiệu bán mạnh, cân nhắc mở vị thế bán"
            
            # Thoát khỏi vùng quá bán (tín hiệu mua tiềm năng)
            elif cci_values[i-1] < oversold and cci_values[i] >= oversold:
                signal_type = "exit_oversold"
                buying_power = "Lực bán đang suy yếu từ vùng quá bán"
                action = "Tín hiệu mua mạnh, cân nhắc mở vị thế mua"
            
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "value": round(cci_values[i], 2),
                    "signal": signal_type,
                    "buying_power": buying_power,
                    "action": action
                })
        
        return signals

    def get_divergence_signals(self) -> list[dict]:
        """
        Identify potential divergence signals between price and CCI.

        Returns:
            list[dict]: List of divergence signals
        """
        cci_values = self.calculate()
        signals = []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Need at least 10 valid values to look for divergence
        valid_indices = [
            i
            for i in range(len(sorted_prices))
            if i >= self.period - 1 and cci_values[i] is not None
        ]

        if len(valid_indices) < 10:
            return signals

        # Look for local highs and lows in price and CCI
        for i in range(2, len(valid_indices) - 2):
            idx = valid_indices[i]
            prev_idx = valid_indices[i - 2]

            # Check if we have a price high but CCI lower high (bearish divergence)
            if (
                sorted_prices[idx].close_price > sorted_prices[prev_idx].close_price
                and cci_values[idx] < cci_values[prev_idx]
                and cci_values[idx] > 100  # CCI should be in overbought territory
            ):
                signals.append(
                    {
                        "timestamp": sorted_prices[idx].timestamp,
                        "price": sorted_prices[idx].close_price,
                        "signal": "bearish_divergence",
                        "cci": cci_values[idx],
                    }
                )

            # Check if we have a price low but CCI higher low (bullish divergence)
            elif (
                sorted_prices[idx].close_price < sorted_prices[prev_idx].close_price
                and cci_values[idx] > cci_values[prev_idx]
                and cci_values[idx] < -100  # CCI should be in oversold territory
            ):
                signals.append(
                    {
                        "timestamp": sorted_prices[idx].timestamp,
                        "price": sorted_prices[idx].close_price,
                        "signal": "bullish_divergence",
                        "cci": cci_values[idx],
                    }
                )

        return signals

    def predict_trend(self) -> Dict[str, Any]:
        cci_values = self.calculate()
        latest_cci = next((v for v in reversed(cci_values) if v is not None), 0.0)
        if latest_cci > 100:
            return {"trend": "uptrend", "confidence": min(1.0, (latest_cci-100)/200)}
        elif latest_cci < -100:
            return {"trend": "downtrend", "confidence": min(1.0, (abs(latest_cci)+100)/200)}
        else:
            return {"trend": "sideways", "confidence": 1 - abs(latest_cci)/100}

    def get_recommendation(self) -> str:
        cci_values = self.calculate()
        latest_cci = next((v for v in reversed(cci_values) if v is not None), 0.0)
        if latest_cci > 100:
            return "Sell (Overbought)"
        elif latest_cci < -100:
            return "Buy (Oversold)"
        else:
            return "Hold (Neutral)"
