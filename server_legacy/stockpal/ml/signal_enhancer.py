from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from datetime import datetime
import os
import json


class SignalEnhancer:
    """
    Enhances trading signals by weighting them based on historical effectiveness.
    
    This class uses historical effectiveness data to adjust the weight of each technical indicator
    in generating the final trading signal. It can adapt to different market conditions and
    learn from past performance.
    """

    def __init__(self, history_file: Optional[str] = None):
        """
        Initialize the Signal Enhancer.
        
        Args:
            history_file: Optional path to a JSON file containing historical effectiveness data
        """
        self.history_file = history_file or os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            "data", 
            "indicator_effectiveness.json"
        )
        
        # Load historical effectiveness data if available
        self.effectiveness_data = self._load_effectiveness_data()
        
        # Default weights for each indicator by market condition
        self.default_weights = {
            "trending": {
                "ADX": 2.0,
                "MACD": 1.8,
                "RSI": 1.2,
                "MA": 1.5,
                "BB": 1.0,
                "Stochastic": 1.0,
                "CCI": 1.0,
                "OBV": 1.5,
                "WPR": 0.8,
                "ROC": 1.2,
                "ULTOSC": 1.0,
                "SAR": 1.8,
                "STOCHRSI_fastk": 1.2,
                "Momentum": 1.3,
                "Bear Power": 1.0,
                "RS(52)": 1.5,
                "ADI": 1.3,
                "AO": 1.3,
                "Pivot Points": 1.0,
                "Elliott Wave": 1.2,
                "Market Sentiment": 1.0
            },
            "ranging": {
                "ADX": 0.8,
                "MACD": 1.0,
                "RSI": 1.8,
                "MA": 0.8,
                "BB": 1.5,
                "Stochastic": 1.8,
                "CCI": 1.5,
                "OBV": 1.0,
                "WPR": 1.5,
                "ROC": 1.0,
                "ULTOSC": 1.5,
                "SAR": 0.8,
                "STOCHRSI_fastk": 1.8,
                "Momentum": 1.0,
                "Bear Power": 1.2,
                "RS(52)": 0.8,
                "ADI": 1.0,
                "AO": 1.0,
                "Pivot Points": 1.5,
                "Elliott Wave": 1.0,
                "Market Sentiment": 1.2
            }
        }

    def enhance_signals(self, symbol: str, signals: List[Dict], market_condition: str = "trending") -> List[Dict]:
        """
        Enhance trading signals with adaptive weighting based on historical effectiveness.
        
        Args:
            symbol: Stock symbol
            signals: List of indicator signals
            market_condition: Current market condition ("trending" or "ranging")
            
        Returns:
            Enhanced signals with adjusted weights
        """
        if not signals:
            return []
            
        # Normalize market condition
        market_condition = market_condition.lower()
        if market_condition not in ["trending", "ranging"]:
            market_condition = "trending"  # Default to trending
            
        # Get effectiveness data for this symbol
        symbol_effectiveness = self.effectiveness_data.get(symbol, {})
        
        # Enhanced signals will contain the original signals plus weight information
        enhanced_signals = []
        
        for signal in signals:
            indicator_name = signal.get("name")
            if not indicator_name:
                enhanced_signals.append(signal)  # Keep original if no name
                continue
                
            # Get default weight for this indicator in the current market condition
            default_weight = self.default_weights.get(market_condition, {}).get(indicator_name, 1.0)
            
            # Get historical effectiveness for this indicator for this symbol
            indicator_effectiveness = symbol_effectiveness.get(indicator_name, {})
            historical_weight = indicator_effectiveness.get(market_condition, default_weight)
            
            # Create enhanced signal with weight information
            enhanced_signal = signal.copy()
            enhanced_signal["weight"] = historical_weight
            enhanced_signal["default_weight"] = default_weight
            enhanced_signal["adjusted_weight"] = self._adjust_weight(
                historical_weight, 
                signal.get("signal"),  # Don't provide a default here, let _adjust_weight handle None
                market_condition
            )
            
            enhanced_signals.append(enhanced_signal)
        
        # Sort signals by adjusted weight (highest first)
        enhanced_signals.sort(key=lambda x: x.get("adjusted_weight", 0), reverse=True)
        
        return enhanced_signals
    
    def record_signal_outcome(self, symbol: str, indicator_name: str, signal_type: str, 
                             market_condition: str, outcome: bool) -> None:
        """
        Record the outcome of a signal to improve future effectiveness weighting.
        
        Args:
            symbol: Stock symbol
            indicator_name: Name of the indicator
            signal_type: Type of signal (Buy, Sell, Neutral)
            market_condition: Market condition when signal was generated
            outcome: Whether the signal led to a successful trade
        """
        # Initialize if symbol not in data
        if symbol not in self.effectiveness_data:
            self.effectiveness_data[symbol] = {}
            
        # Initialize if indicator not in symbol data
        if indicator_name not in self.effectiveness_data[symbol]:
            self.effectiveness_data[symbol][indicator_name] = {
                "trending": 1.0,
                "ranging": 1.0,
                "success_count": 0,
                "total_count": 0,
                "signals": {
                    "Buy": {"success": 0, "total": 0},
                    "Sell": {"success": 0, "total": 0},
                    "Neutral": {"success": 0, "total": 0}
                }
            }
            
        # Update counts
        indicator_data = self.effectiveness_data[symbol][indicator_name]
        indicator_data["total_count"] += 1
        if outcome:
            indicator_data["success_count"] += 1
            
        # Update signal-specific counts
        signal_data = indicator_data["signals"].get(signal_type, {"success": 0, "total": 0})
        signal_data["total"] += 1
        if outcome:
            signal_data["success"] += 1
        indicator_data["signals"][signal_type] = signal_data
        
        # Calculate success rate
        success_rate = indicator_data["success_count"] / indicator_data["total_count"] if indicator_data["total_count"] > 0 else 0.5
        
        # Update weight for the specific market condition
        # Start with default weight and adjust based on success rate
        default_weight = self.default_weights.get(market_condition, {}).get(indicator_name, 1.0)
        
        # Scale weight: 0.5x for 0% success, 1x for 50% success, 1.5x for 100% success
        adjusted_weight = default_weight * (0.5 + success_rate)
        
        # Update the weight in the data
        indicator_data[market_condition] = adjusted_weight
        
        # Save updated data
        self._save_effectiveness_data()
    
    def _adjust_weight(self, base_weight: float, signal_type: str, market_condition: str) -> float:
        """
        Make final adjustments to weight based on signal type and market conditions.
        
        Args:
            base_weight: Base weight from historical data
            signal_type: Signal type (Buy, Sell, Neutral)
            market_condition: Current market condition
            
        Returns:
            Adjusted weight
        """
        # Handle None signal_type
        if signal_type is None:
            return base_weight
            
        # Convert signal_type to lowercase if it's a string
        signal_type_lower = signal_type.lower() if isinstance(signal_type, str) else ""
        
        # No adjustment for neutral signals
        if signal_type_lower in ["trung tính", "neutral"]:
            return base_weight * 0.8  # Lower weight for neutral signals
        
        # In trending markets, favor stronger signals
        if market_condition == "trending":
            if signal_type_lower in ["mua", "buy"]:
                return base_weight * 1.2  # Boost buy signals in trending markets
            elif signal_type_lower in ["bán", "sell"]:
                return base_weight * 1.1  # Slightly boost sell signals
        
        # In ranging markets, favor oscillator signals more
        elif market_condition == "ranging":
            # No special adjustment needed here as the default weights already account for this
            pass
            
        return base_weight
    
    def _load_effectiveness_data(self) -> Dict:
        """
        Load historical effectiveness data from the JSON file.
        
        Returns:
            Dictionary of effectiveness data
        """
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # Create empty structure if file doesn't exist
                return {}
        except Exception as e:
            print(f"Error loading effectiveness data: {e}")
            return {}
    
    def _save_effectiveness_data(self) -> None:
        """Save the current effectiveness data to the JSON file."""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.effectiveness_data, f, indent=2)
        except Exception as e:
            print(f"Error saving effectiveness data: {e}")
    
    def get_top_indicators(self, symbol: str, market_condition: str = "trending", limit: int = 5) -> List[Dict]:
        """
        Get the most effective indicators for a symbol in a given market condition.
        
        Args:
            symbol: Stock symbol
            market_condition: Market condition type
            limit: Maximum number of indicators to return
            
        Returns:
            List of the most effective indicators with their weights
        """
        if symbol not in self.effectiveness_data:
            return []
            
        # Get indicator data for the symbol
        symbol_data = self.effectiveness_data[symbol]
        
        # Build list of indicators with their weights
        indicators = []
        for indicator_name, indicator_data in symbol_data.items():
            weight = indicator_data.get(market_condition, 1.0)
            success_rate = indicator_data.get("success_count", 0) / indicator_data.get("total_count", 1)
            
            indicators.append({
                "name": indicator_name,
                "weight": weight,
                "success_rate": success_rate,
                "sample_size": indicator_data.get("total_count", 0)
            })
        
        # Sort by weight (highest first) and take top N
        indicators.sort(key=lambda x: x["weight"], reverse=True)
        return indicators[:limit] 