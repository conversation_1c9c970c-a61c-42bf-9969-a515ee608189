from datetime import date

from stockpal.core import SqliteService
from stockpal.core.trading_date import TradingDate


class TradingDateDao:
    def __init__(self, sql_service: SqliteService):
        self._sql_service = sql_service

    def get_all_trading_dates(self) -> list[TradingDate]:
        query = "SELECT date, dow FROM trading_dates ORDER BY date"
        rows = self._sql_service.fetch_all(query)
        return [TradingDate(date.fromisoformat(row[0]), row[1]) for row in rows]

    def get_trading_date(self, trading_date: date) -> TradingDate | None:
        query = "SELECT date, dow FROM trading_dates WHERE date = ?"
        row = self._sql_service.fetch_one(query, (trading_date.isoformat(),))
        return TradingDate(date.fromisoformat(row[0]), row[1]) if row else None

    def upsert_trading_date(self, trading_date: TradingDate) -> None:
        query = """
        INSERT INTO trading_dates (date, dow)
        VALUES (?, ?)
        ON CONFLICT(date) DO UPDATE SET
            dow = excluded.dow
        """
        self._sql_service.execute(
            query, (trading_date.date.isoformat(), trading_date.dow)
        )

    def upsert_trading_dates(self, trading_dates: list[TradingDate]) -> None:
        query = """
        INSERT INTO trading_dates (date, dow)
        VALUES (?, ?)
        ON CONFLICT(date) DO UPDATE SET
            dow = excluded.dow
        """
        params = [(td.date.isoformat(), td.dow) for td in trading_dates]
        self._sql_service.execute_many(query, params)
