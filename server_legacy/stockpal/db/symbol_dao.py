from stockpal.core import SqliteService, Stock


class SymbolDao:

    def __init__(self, sql_service: SqliteService):
        self._sql_service = sql_service
        self._column_order = "code, name, exchange, industry, sector"

    def get_all(self) -> list[Stock]:
        query = f"SELECT {self._column_order} FROM symbols"
        rows = self._sql_service.fetch_all(query)
        return [
            Stock(
                symbol=row[0],
                name=row[1],
                exchange=row[2],
                industry=row[3],
                sector=row[4],
            )
            for row in rows
        ]

    def upsert(self, stocks: list[Stock]):
        stocks.sort(key=lambda x: x.code)

        non_stocks = [
            stock
            for stock in stocks
            if any(getattr(stock, attr) is None for attr in ["name", "exchange"])
        ]
        print(non_stocks)

        # Filter out stocks with None values
        stocks = [
            stock
            for stock in stocks
            if all(
                getattr(stock, attr) is not None
                for attr in ["code", "name", "exchange"]
            )
        ]

        # Upsert
        self._sql_service.execute_many(
            f"""
            INSERT INTO `symbols` ({self._column_order}) 
            VALUES (?,?,?,?,?) ON CONFLICT (code) DO NOTHING
            """,
            params_list=[
                (s.code, s.name, s.exchange, s.industry, s.sector) for s in stocks
            ],
        )
