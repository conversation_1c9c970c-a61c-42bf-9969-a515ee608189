{"[python]": {"editor.defaultFormatter": "ms-python.black-formatter"}, "isort.args": ["--profile", "black"], "python.analysis.typeCheckingMode": "off", "python.analysis.autoImportCompletions": true, "livePreview.defaultPreviewPath": "/web/index.html", "python.defaultInterpreterPath": "server/.venv/bin/python", "mcp": {"inputs": [], "servers": {"mcp-server-time": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone=America/Los_Angeles"], "env": {}}, "Context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}}}, "vscode-edge-devtools.defaultEntrypoint": "web_test/index.html", "vscode-edge-devtools.headless": false, "vscode-edge-devtools.defaultUrl": "web_test/index.html", "vscode-edge-devtools.showWorkers": true, "vscode-edge-devtools.browserFlavor": "Stable"}