/*
! tailwindcss v3.4.6 | MIT License | https://tailwindcss.com
*/
*,
:after,
:before {
  box-sizing: border-box;
  border: 0 solid #e5e7eb;
}

:after,
:before {
  --tw-content: "";
}

:host,
html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-family: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  line-height: inherit;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b,
strong {
  font-weight: bolder;
}

code,
kbd,
pre,
samp {
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button,
select {
  text-transform: none;
}

button,
input:where([type="button"]),
input:where([type="reset"]),
input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
}

fieldset,
legend {
  padding: 0;
}

menu,
ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

[role="button"],
button {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
  display: block;
  vertical-align: middle;
}

img,
video {
  max-width: 100%;
  height: auto;
}

[hidden] {
  display: none;
}

:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 240 5.9% 10%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 72.22% 50.59%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 5% 64.9%;
  --radius: 0.5rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 240 5% 64.9%;
  --font-geist-sans: var(--font-sans);
  --font-geist-mono: var(--font-mono);
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 85.7% 97.3%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --sidebar-background: 240 5.9% 10%;
  --sidebar-foreground: 240 4.8% 95.9%;
  --sidebar-primary: 224.3 76.3% 48%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 240 4.8% 95.9%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 240 4.9% 83.9%;
}

* {
  border-color: hsl(var(--border));
}

html {
  scroll-behavior: smooth;
}

body {
  overscroll-behavior: none;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-synthesis-weight: none;
  text-rendering: optimizeLegibility;
}

@supports (font: -apple-system-body) and (-webkit-appearance: none) {
  @media (min-width: 1800px) {
    [data-wrapper] {
      border-top-width: 1px;
    }
  }
}

::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 5px;
}

* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) transparent;
}

*,
:after,
:before {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0;
}

.inset-32 {
  inset: 8rem;
}

.inset-x-0 {
  left: 0;
  right: 0;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.-bottom-12 {
  bottom: -3rem;
}

.-left-12 {
  left: -3rem;
}

.-right-12 {
  right: -3rem;
}

.-top-12 {
  top: -3rem;
}

.bottom-0 {
  bottom: 0;
}

.bottom-1 {
  bottom: 0.25rem;
}

.bottom-16 {
  bottom: 4rem;
}

.bottom-24 {
  bottom: 6rem;
}

.left-0 {
  left: 0;
}

.left-1 {
  left: 0.25rem;
}

.left-1\/2 {
  left: 50%;
}

.left-16 {
  left: 4rem;
}

.left-2 {
  left: 0.5rem;
}

.left-2\.5 {
  left: 0.625rem;
}

.left-\[-35px\] {
  left: -35px;
}

.left-\[122px\] {
  left: 122px;
}

.left-\[30px\] {
  left: 30px;
}

.left-\[50\%\] {
  left: 50%;
}

.left-\[50px\] {
  left: 50px;
}

.left-\[5px\] {
  left: 5px;
}

.left-\[85px\] {
  left: 85px;
}

.right-0 {
  right: 0;
}

.right-1 {
  right: 0.25rem;
}

.right-16 {
  right: 4rem;
}

.right-2 {
  right: 0.5rem;
}

.right-2\.5 {
  right: 0.625rem;
}

.right-24 {
  right: 6rem;
}

.right-3 {
  right: 0.75rem;
}

.right-4 {
  right: 1rem;
}

.right-\[0\.3rem\] {
  right: 0.3rem;
}

.top-0 {
  top: 0;
}

.top-1 {
  top: 0.25rem;
}

.top-1\.5 {
  top: 0.375rem;
}

.top-1\/2 {
  top: 50%;
}

.top-14 {
  top: 3.5rem;
}

.top-16 {
  top: 4rem;
}

.top-2 {
  top: 0.5rem;
}

.top-2\.5 {
  top: 0.625rem;
}

.top-20 {
  top: 5rem;
}

.top-3 {
  top: 0.75rem;
}

.top-3\.5 {
  top: 0.875rem;
}

.top-4 {
  top: 1rem;
}

.top-\[--header-height\] {
  top: var(--header-height);
}

.top-\[0\.3rem\] {
  top: 0.3rem;
}

.top-\[0px\] {
  top: 0;
}

.top-\[10px\] {
  top: 10px;
}

.top-\[1px\] {
  top: 1px;
}

.top-\[38px\] {
  top: 38px;
}

.top-\[45px\] {
  top: 45px;
}

.top-\[50\%\] {
  top: 50%;
}

.top-\[50px\] {
  top: 50px;
}

.top-\[60\%\] {
  top: 60%;
}

.top-\[60px\] {
  top: 60px;
}

.top-full {
  top: 100%;
}

.isolate {
  isolation: isolate;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[100\] {
  z-index: 100;
}

.z-\[1\] {
  z-index: 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.m-0 {
  margin: 0;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-mx-4 {
  margin-left: -1rem;
  margin-right: -1rem;
}

.mx-0 {
  margin-left: 0;
  margin-right: 0;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3\.5 {
  margin-left: 0.875rem;
  margin-right: 0.875rem;
}

.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}

.mx-\[-0\.65rem\] {
  margin-left: -0.65rem;
  margin-right: -0.65rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-0\.5 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.-ml-1 {
  margin-left: -0.25rem;
}

.-ml-2 {
  margin-left: -0.5rem;
}

.-ml-3 {
  margin-left: -0.75rem;
}

.-ml-4 {
  margin-left: -1rem;
}

.-mr-1 {
  margin-right: -0.25rem;
}

.-mt-1 {
  margin-top: -0.25rem;
}

.-mt-4 {
  margin-top: -1rem;
}

.-mt-6 {
  margin-top: -1.5rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.ml-0 {
  margin-left: 0;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-\[160px\] {
  margin-left: 160px;
}

.ml-auto {
  margin-left: auto;
}

.mr-0 {
  margin-right: 0;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-\[14px\] {
  margin-right: 14px;
}

.mr-auto {
  margin-right: auto;
}

.mt-0 {
  margin-top: 0;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-24 {
  margin-top: 6rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-\[21px\] {
  margin-top: 21px;
}

.mt-auto {
  margin-top: auto;
}

.mt-px {
  margin-top: 1px;
}

.line-clamp-1 {
  -webkit-line-clamp: 1;
}

.line-clamp-1,
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.\!table {
  display: table !important;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.\!hidden {
  display: none !important;
}

.hidden {
  display: none;
}

.aspect-\[1440\/900\] {
  aspect-ratio: 1440/900;
}

.aspect-\[3\/1\] {
  aspect-ratio: 3/1;
}

.aspect-\[3\/4\] {
  aspect-ratio: 3/4;
}

.aspect-\[4\/2\.5\] {
  aspect-ratio: 4/2.5;
}

.aspect-auto {
  aspect-ratio: auto;
}

.aspect-square {
  aspect-ratio: 1/1;
}

.aspect-video {
  aspect-ratio: 16/9;
}

.\!size-2\.5 {
  width: 0.625rem !important;
  height: 0.625rem !important;
}

.\!size-6 {
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}

.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}

.size-4 {
  width: 1rem;
  height: 1rem;
}

.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}

.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}

.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}

.size-8 {
  width: 2rem;
  height: 2rem;
}

.size-full {
  width: 100%;
  height: 100%;
}

.\!h-\[calc\(100svh-var\(--header-height\)\)\] {
  height: calc(100svh - var(--header-height)) !important;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-2\.5 {
  height: 0.625rem;
}

.h-24 {
  height: 6rem;
}

.h-3 {
  height: 0.75rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-72 {
  height: 18rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[--header-height\] {
  height: var(--header-height);
}

.h-\[1\.2rem\] {
  height: 1.2rem;
}

.h-\[1\.45rem\] {
  height: 1.45rem;
}

.h-\[100vh\] {
  height: 100vh;
}

.h-\[120px\] {
  height: 120px;
}

.h-\[125px\] {
  height: 125px;
}

.h-\[140px\] {
  height: 140px;
}

.h-\[150px\] {
  height: 150px;
}

.h-\[1px\] {
  height: 1px;
}

.h-\[200px\] {
  height: 200px;
}

.h-\[20px\] {
  height: 20px;
}

.h-\[22px\] {
  height: 22px;
}

.h-\[250px\] {
  height: 250px;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[32px\] {
  height: 32px;
}

.h-\[450px\] {
  height: 450px;
}

.h-\[480px\] {
  height: 480px;
}

.h-\[52px\] {
  height: 52px;
}

.h-\[53px\] {
  height: 53px;
}

.h-\[60px\] {
  height: 60px;
}

.h-\[800px\] {
  height: 800px;
}

.h-\[80px\] {
  height: 80px;
}

.h-\[96\%\] {
  height: 96%;
}

.h-\[calc\(100vh-3\.5rem\)\] {
  height: calc(100vh - 3.5rem);
}

.h-\[calc\(theme\(spacing\.7\)_-_1px\)\] {
  height: calc(1.75rem - 1px);
}

.h-\[var\(--cmdk-list-height\)\] {
  height: var(--cmdk-list-height);
}

.h-\[var\(--radix-navigation-menu-viewport-height\)\] {
  height: var(--radix-navigation-menu-viewport-height);
}

.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}

.h-auto {
  height: auto;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.h-svh {
  height: 100svh;
}

.max-h-32 {
  max-height: 8rem;
}

.max-h-\[--radix-context-menu-content-available-height\] {
  max-height: var(--radix-context-menu-content-available-height);
}

.max-h-\[--radix-select-content-available-height\] {
  max-height: var(--radix-select-content-available-height);
}

.max-h-\[250px\] {
  max-height: 250px;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[400px\] {
  max-height: 400px;
}

.max-h-\[450px\] {
  max-height: 450px;
}

.max-h-\[650px\] {
  max-height: 650px;
}

.max-h-\[800px\] {
  max-height: 800px;
}

.max-h-\[80svh\] {
  max-height: 80svh;
}

.max-h-\[80vh\] {
  max-height: 80vh;
}

.max-h-\[var\(--radix-dropdown-menu-content-available-height\)\] {
  max-height: var(--radix-dropdown-menu-content-available-height);
}

.max-h-screen {
  max-height: 100vh;
}

.\!min-h-full {
  min-height: 100% !important;
}

.min-h-0 {
  min-height: 0;
}

.min-h-12 {
  min-height: 3rem;
}

.min-h-32 {
  min-height: 8rem;
}

.min-h-\[100vh\] {
  min-height: 100vh;
}

.min-h-\[200px\] {
  min-height: 200px;
}

.min-h-\[280px\] {
  min-height: 280px;
}

.min-h-\[300px\] {
  min-height: 300px;
}

.min-h-\[350px\] {
  min-height: 350px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-\[50vh\] {
  min-height: 50vh;
}

.min-h-\[60px\] {
  min-height: 60px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-\[9\.5rem\] {
  min-height: 9.5rem;
}

.min-h-\[VALUE\] {
  min-height: VALUE;
}

.min-h-\[calc\(100vh_-_theme\(spacing\.16\)\)\] {
  min-height: calc(100vh - 4rem);
}

.min-h-screen {
  min-height: 100vh;
}

.min-h-svh {
  min-height: 100svh;
}

.\!w-\[calc\(var\(--sidebar-width-icon\)_\+_1px\)\] {
  width: calc(var(--sidebar-width-icon) + 1px) !important;
}

.w-0 {
  width: 0;
}

.w-1 {
  width: 0.25rem;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-2\.5 {
  width: 0.625rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\.5 {
  width: 0.875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-40 {
  width: 10rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-72 {
  width: 18rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-9 {
  width: 2.25rem;
}

.w-96 {
  width: 24rem;
}

.w-\[--radix-dropdown-menu-trigger-width\] {
  width: var(--radix-dropdown-menu-trigger-width);
}

.w-\[--radix-popper-anchor-width\] {
  width: var(--radix-popper-anchor-width);
}

.w-\[--sidebar-width\] {
  width: var(--sidebar-width);
}

.w-\[1\.2rem\] {
  width: 1.2rem;
}

.w-\[100px\] {
  width: 100px;
}

.w-\[110px\] {
  width: 110px;
}

.w-\[116px\] {
  width: 116px;
}

.w-\[130px\] {
  width: 130px;
}

.w-\[145px\] {
  width: 145px;
}

.w-\[150px\] {
  width: 150px;
}

.w-\[1600px\] {
  width: 1600px;
}

.w-\[160px\] {
  width: 160px;
}

.w-\[180px\] {
  width: 180px;
}

.w-\[1px\] {
  width: 1px;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[22px\] {
  width: 22px;
}

.w-\[230px\] {
  width: 230px;
}

.w-\[240px\] {
  width: 240px;
}

.w-\[250px\] {
  width: 250px;
}

.w-\[260px\] {
  width: 260px;
}

.w-\[280px\] {
  width: 280px;
}

.w-\[300px\] {
  width: 300px;
}

.w-\[320px\] {
  width: 320px;
}

.w-\[32px\] {
  width: 32px;
}

.w-\[340px\] {
  width: 340px;
}

.w-\[350px\] {
  width: 350px;
}

.w-\[380px\] {
  width: 380px;
}

.w-\[400px\] {
  width: 400px;
}

.w-\[40px\] {
  width: 40px;
}

.w-\[450px\] {
  width: 450px;
}

.w-\[520px\] {
  width: 520px;
}

.w-\[535px\] {
  width: 535px;
}

.w-\[60\%\] {
  width: 60%;
}

.w-\[64px\] {
  width: 64px;
}

.w-\[70px\] {
  width: 70px;
}

.w-\[72px\] {
  width: 72px;
}

.w-\[80px\] {
  width: 80px;
}

.w-\[896px\] {
  width: 896px;
}

.w-\[8rem\] {
  width: 8rem;
}

.w-\[970px\] {
  width: 970px;
}

.w-\[9rem\] {
  width: 9rem;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-max {
  width: -moz-max-content;
  width: max-content;
}

.w-px {
  width: 1px;
}

.min-w-0 {
  min-width: 0;
}

.min-w-10 {
  min-width: 2.5rem;
}

.min-w-11 {
  min-width: 2.75rem;
}

.min-w-5 {
  min-width: 1.25rem;
}

.min-w-56 {
  min-width: 14rem;
}

.min-w-8 {
  min-width: 2rem;
}

.min-w-9 {
  min-width: 2.25rem;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-\[12rem\] {
  min-width: 12rem;
}

.min-w-\[130px\] {
  min-width: 130px;
}

.min-w-\[250px\] {
  min-width: 250px;
}

.min-w-\[50px\] {
  min-width: 50px;
}

.min-w-\[8rem\] {
  min-width: 8rem;
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}

.max-w-20 {
  max-width: 5rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-\[--skeleton-width\] {
  max-width: var(--skeleton-width);
}

.max-w-\[250px\] {
  max-width: 250px;
}

.max-w-\[260px\] {
  max-width: 260px;
}

.max-w-\[300px\] {
  max-width: 300px;
}

.max-w-\[420px\] {
  max-width: 420px;
}

.max-w-\[500px\] {
  max-width: 500px;
}

.max-w-\[59rem\] {
  max-width: 59rem;
}

.max-w-\[600px\] {
  max-width: 600px;
}

.max-w-\[75\%\] {
  max-width: 75%;
}

.max-w-\[80\%\] {
  max-width: 80%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-max {
  max-width: -moz-max-content;
  max-width: max-content;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.basis-1\/3 {
  flex-basis: 33.333333%;
}

.basis-full {
  flex-basis: 100%;
}

.caption-bottom {
  caption-side: bottom;
}

.border-collapse {
  border-collapse: collapse;
}

.origin-\[--radix-context-menu-content-transform-origin\] {
  transform-origin: var(--radix-context-menu-content-transform-origin);
}

.origin-\[--radix-dropdown-menu-content-transform-origin\] {
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
}

.origin-\[--radix-hover-card-content-transform-origin\] {
  transform-origin: var(--radix-hover-card-content-transform-origin);
}

.origin-\[--radix-menubar-content-transform-origin\] {
  transform-origin: var(--radix-menubar-content-transform-origin);
}

.origin-\[--radix-popover-content-transform-origin\] {
  transform-origin: var(--radix-popover-content-transform-origin);
}

.origin-\[--radix-select-content-transform-origin\] {
  transform-origin: var(--radix-select-content-transform-origin);
}

.origin-\[--radix-tooltip-content-transform-origin\] {
  transform-origin: var(--radix-tooltip-content-transform-origin);
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
}

.-translate-x-1\/2,
.-translate-x-1\/4 {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-1\/4 {
  --tw-translate-x: -25%;
}

.-translate-x-px {
  --tw-translate-x: -1px;
}

.-translate-x-px,
.-translate-y-1\/2 {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
}

.-translate-y-1\/4 {
  --tw-translate-y: -25%;
}

.-translate-y-1\/4,
.-translate-y-2 {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-2 {
  --tw-translate-y: -0.5rem;
}

.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
}

.translate-x-\[-50\%\],
.translate-x-px {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-px {
  --tw-translate-x: 1px;
}

.translate-y-1 {
  --tw-translate-y: 0.25rem;
}

.translate-y-1,
.translate-y-\[-50\%\] {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
}

.translate-y-\[2px\] {
  --tw-translate-y: 2px;
}

.rotate-0,
.translate-y-\[2px\] {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
  --tw-rotate: 0deg;
}

.rotate-180 {
  --tw-rotate: 180deg;
}

.rotate-180,
.rotate-45 {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
}

.rotate-90 {
  --tw-rotate: 90deg;
}

.rotate-90,
.rotate-\[-40deg\] {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-\[-40deg\] {
  --tw-rotate: -40deg;
}

.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
}

.scale-0,
.scale-100 {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
}

.-scale-x-100 {
  --tw-scale-x: -1;
}

.-scale-x-100,
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes caret-blink {
  0%,
  70%,
  to {
    opacity: 1;
  }

  20%,
  50% {
    opacity: 0;
  }
}

.animate-caret-blink {
  animation: caret-blink 1.25s ease-out infinite;
}

@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  to {
    transform: rotate(1turn);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.touch-none {
  touch-action: none;
}

.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.scroll-m-20 {
  scroll-margin: 5rem;
}

.scroll-mt-20 {
  scroll-margin-top: 5rem;
}

.scroll-mt-24 {
  scroll-margin-top: 6rem;
}

.list-decimal {
  list-style-type: decimal;
}

.list-disc {
  list-style-type: disc;
}

.list-none {
  list-style-type: none;
}

.appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.grid-flow-row {
  grid-auto-flow: row;
}

.auto-rows-max {
  grid-auto-rows: max-content;
}

.auto-rows-min {
  grid-auto-rows: min-content;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-\[1fr_110px\] {
  grid-template-columns: 1fr 110px;
}

.grid-cols-\[25px_1fr\] {
  grid-template-columns: 25px 1fr;
}

.grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.place-items-center {
  place-items: center;
}

.content-center {
  align-content: center;
}

.\!items-start {
  align-items: flex-start !important;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.\!justify-start {
  justify-content: flex-start !important;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-0 {
  gap: 0;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-2\.5 {
  gap: 0.625rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-3\.5 {
  gap: 0.875rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.self-center {
  align-self: center;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
}

.truncate,
.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.whitespace-break-spaces {
  white-space: break-spaces;
}

.text-balance {
  text-wrap: balance;
}

.break-words {
  overflow-wrap: break-word;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-\[0\.5rem\] {
  border-radius: 0.5rem;
}

.rounded-\[12px\] {
  border-radius: 12px;
}

.rounded-\[2px\] {
  border-radius: 2px;
}

.rounded-\[6px\] {
  border-radius: 6px;
}

.rounded-\[inherit\] {
  border-radius: inherit;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: var(--radius);
}

.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}

.rounded-none {
  border-radius: 0;
}

.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}

.rounded-xl {
  border-radius: calc(var(--radius) + 4px);
}

.rounded-t-\[10px\] {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.rounded-t-none {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.rounded-tl-sm {
  border-top-left-radius: calc(var(--radius) - 4px);
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0;
}

.border-2 {
  border-width: 2px;
}

.border-\[1\.5px\] {
  border-width: 1.5px;
}

.border-y {
  border-top-width: 1px;
}

.border-b,
.border-y {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-0 {
  border-left-width: 0;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-r {
  border-right-width: 1px;
}

.border-r-0 {
  border-right-width: 0;
}

.border-t {
  border-top-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-\[--color-border\] {
  border-color: var(--color-border);
}

.border-amber-200 {
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity));
}

.border-background {
  border-color: hsl(var(--background));
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.border-border {
  border-color: hsl(var(--border));
}

.border-border\/50 {
  border-color: hsl(var(--border) / 0.5);
}

.border-destructive {
  --tw-border-opacity: 1;
  border-color: hsl(var(--destructive) / var(--tw-border-opacity));
}

.border-destructive\/50 {
  border-color: hsl(var(--destructive) / 0.5);
}

.border-input {
  border-color: hsl(var(--input));
}

.border-muted {
  border-color: hsl(var(--muted));
}

.border-primary {
  border-color: hsl(var(--primary));
}

.border-primary\/50 {
  border-color: hsl(var(--primary) / 0.5);
}

.border-sidebar-border {
  border-color: hsl(var(--sidebar-border));
}

.border-stone-700 {
  --tw-border-opacity: 1;
  border-color: rgb(68 64 60 / var(--tw-border-opacity));
}

.border-transparent {
  border-color: transparent;
}

.border-zinc-700 {
  --tw-border-opacity: 1;
  border-color: rgb(63 63 70 / var(--tw-border-opacity));
}

.border-zinc-800 {
  --tw-border-opacity: 1;
  border-color: rgb(39 39 42 / var(--tw-border-opacity));
}

.border-b-transparent {
  border-bottom-color: transparent;
}

.border-l-transparent {
  border-left-color: transparent;
}

.border-t-transparent {
  border-top-color: transparent;
}

.bg-\[\#adfa1d\] {
  --tw-bg-opacity: 1;
  background-color: rgb(173 250 29 / var(--tw-bg-opacity));
}

.bg-\[\#ecedef\] {
  --tw-bg-opacity: 1;
  background-color: rgb(236 237 239 / var(--tw-bg-opacity));
}

.bg-\[--bg\] {
  background-color: var(--bg);
}

.bg-\[--color-1\] {
  background-color: var(--color-1);
}

.bg-\[--color-2\] {
  background-color: var(--color-2);
}

.bg-\[--color-3\] {
  background-color: var(--color-3);
}

.bg-\[--color-4\] {
  background-color: var(--color-4);
}

.bg-\[--color-bg\] {
  background-color: var(--color-bg);
}

.bg-\[--theme-primary\] {
  background-color: var(--theme-primary);
}

.bg-accent {
  background-color: hsl(var(--accent));
}

.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity));
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-background\/80 {
  background-color: hsl(var(--background) / 0.8);
}

.bg-background\/95 {
  background-color: hsl(var(--background) / 0.95);
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-black\/80 {
  background-color: rgba(0, 0, 0, 0.8);
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.bg-border {
  background-color: hsl(var(--border));
}

.bg-card {
  background-color: hsl(var(--card));
}

.bg-destructive {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--destructive) / var(--tw-bg-opacity));
}

.bg-foreground {
  background-color: hsl(var(--foreground));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.bg-muted-foreground\/30 {
  background-color: hsl(var(--muted-foreground) / 0.3);
}

.bg-muted\/40 {
  background-color: hsl(var(--muted) / 0.4);
}

.bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}

.bg-popover {
  background-color: hsl(var(--popover));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}

.bg-primary\/20 {
  background-color: hsl(var(--primary) / 0.2);
}

.bg-secondary {
  background-color: hsl(var(--secondary));
}

.bg-sidebar {
  background-color: hsl(var(--sidebar-background));
}

.bg-sidebar-accent {
  background-color: hsl(var(--sidebar-accent));
}

.bg-sidebar-border {
  background-color: hsl(var(--sidebar-border));
}

.bg-sidebar-primary {
  background-color: hsl(var(--sidebar-primary));
}

.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}

.bg-slate-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity));
}

.bg-slate-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.bg-slate-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-zinc-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity));
}

.bg-zinc-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity));
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.from-muted\/50 {
  --tw-gradient-from: hsl(var(--muted) / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-zinc-700\/30 {
  --tw-gradient-from: rgba(63, 63, 70, 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(63, 63, 70, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-muted {
  --tw-gradient-to: hsl(var(--muted)) var(--tw-gradient-to-position);
}

.to-zinc-950\/90 {
  --tw-gradient-to: rgba(9, 9, 11, 0.9) var(--tw-gradient-to-position);
}

.fill-\[--color-desktop\] {
  fill: var(--color-desktop);
}

.fill-\[--color-label\] {
  fill: var(--color-label);
}

.fill-background {
  fill: hsl(var(--background));
}

.fill-current {
  fill: currentColor;
}

.fill-foreground {
  fill: hsl(var(--foreground));
}

.fill-muted-foreground {
  fill: hsl(var(--muted-foreground));
}

.fill-primary {
  fill: hsl(var(--primary));
}

.fill-sky-400 {
  fill: #38bdf8;
}

.fill-white {
  fill: #fff;
}

.stroke-transparent {
  stroke: transparent;
}

.stroke-2 {
  stroke-width: 2;
}

.object-cover {
  -o-object-fit: cover;
  object-fit: cover;
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: 0.25rem;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-\[1px\] {
  padding: 1px;
}

.p-\[2px\] {
  padding: 2px;
}

.px-0 {
  padding-left: 0;
  padding-right: 0;
}

.px-0\.5 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-\[0\.3rem\] {
  padding-left: 0.3rem;
  padding-right: 0.3rem;
}

.px-\[calc\(theme\(spacing\.1\)_-_2px\)\] {
  padding-left: calc(0.25rem - 2px);
  padding-right: calc(0.25rem - 2px);
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-\[0\.2rem\] {
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
}

.py-\[theme\(spacing\.1\)\] {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-1\.5 {
  padding-bottom: 0.375rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-\[--header-height\] {
  padding-bottom: var(--header-height);
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-12 {
  padding-left: 3rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-2\.5 {
  padding-left: 0.625rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-7 {
  padding-left: 1.75rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pl-\[--index\] {
  padding-left: var(--index);
}

.pl-\[53px\] {
  padding-left: 53px;
}

.pl-\[VALUE\] {
  padding-left: VALUE;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-2\.5 {
  padding-right: 0.625rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pt-0 {
  padding-top: 0;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-1\.5 {
  padding-top: 0.375rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-2\.5 {
  padding-top: 0.625rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-middle {
  vertical-align: middle;
}

.font-mono {
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.font-sans {
  font-family: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}

.text-\[0\.70rem\] {
  font-size: 0.7rem;
}

.text-\[0\.8rem\] {
  font-size: 0.8rem;
}

.text-\[1\.15rem\] {
  font-size: 1.15rem;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-\[40px\] {
  font-size: 40px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.not-italic {
  font-style: normal;
}

.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero)
    var(--tw-numeric-figure) var(--tw-numeric-spacing)
    var(--tw-numeric-fraction);
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-\[1\.1\] {
  line-height: 1.1;
}

.leading-\[1\.5\] {
  line-height: 1.5;
}

.leading-\[1\.65rem\] {
  line-height: 1.65rem;
}

.leading-loose {
  line-height: 2;
}

.leading-none {
  line-height: 1;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-snug {
  line-height: 1.375;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-normal {
  letter-spacing: 0;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-tighter {
  letter-spacing: -0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.text-\[\#000000\] {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-\[--text\] {
  color: var(--text);
}

.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}

.text-amber-300 {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity));
}

.text-background {
  color: hsl(var(--background));
}

.text-card-foreground {
  color: hsl(var(--card-foreground));
}

.text-current {
  color: currentColor;
}

.text-destructive {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive) / var(--tw-text-opacity));
}

.text-destructive-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive-foreground) / var(--tw-text-opacity));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-foreground\/50 {
  color: hsl(var(--foreground) / 0.5);
}

.text-foreground\/80 {
  color: hsl(var(--foreground) / 0.8);
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.text-muted-foreground\/70 {
  color: hsl(var(--muted-foreground) / 0.7);
}

.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}

.text-primary {
  color: hsl(var(--primary));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}

.text-sidebar-accent-foreground {
  color: hsl(var(--sidebar-accent-foreground));
}

.text-sidebar-foreground {
  color: hsl(var(--sidebar-foreground));
}

.text-sidebar-foreground\/70 {
  color: hsl(var(--sidebar-foreground) / 0.7);
}

.text-sidebar-primary-foreground {
  color: hsl(var(--sidebar-primary-foreground));
}

.text-sky-300 {
  --tw-text-opacity: 1;
  color: rgb(125 211 252 / var(--tw-text-opacity));
}

.text-sky-400 {
  --tw-text-opacity: 1;
  color: rgb(56 189 248 / var(--tw-text-opacity));
}

.text-stone-400 {
  --tw-text-opacity: 1;
  color: rgb(168 162 158 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-zinc-400 {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity));
}

.text-zinc-50 {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity));
}

.text-zinc-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity));
}

.text-zinc-700 {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.no-underline {
  text-decoration-line: none;
}

.underline-offset-2 {
  text-underline-offset: 2px;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.accent-foreground {
  accent-color: hsl(var(--foreground));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-80 {
  opacity: 0.8;
}

.opacity-90 {
  opacity: 0.9;
}

.mix-blend-luminosity {
  mix-blend-mode: luminosity;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
    0 1px 2px -1px var(--tw-shadow-color);
}

.shadow,
.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
    0 4px 6px -4px var(--tw-shadow-color);
}

.shadow-lg,
.shadow-md {
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
    0 2px 4px -2px var(--tw-shadow-color);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
}

.shadow-none,
.shadow-sm {
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),
    0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-card {
  --tw-shadow-color: hsl(var(--card));
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

.ring,
.ring-0 {
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

.ring-1,
.ring-2 {
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.ring-2,
.ring-\[2px\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

.ring-\[2px\] {
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.ring-border {
  --tw-ring-color: hsl(var(--border));
}

.ring-primary\/30 {
  --tw-ring-color: hsl(var(--primary) / 0.3);
}

.ring-ring {
  --tw-ring-color: hsl(var(--ring));
}

.ring-sidebar-ring {
  --tw-ring-color: hsl(var(--sidebar-ring));
}

.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}

.grayscale {
  --tw-grayscale: grayscale(100%);
}

.grayscale,
.invert-\[1\] {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
    var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate)
    var(--tw-sepia) var(--tw-drop-shadow);
}

.invert-\[1\] {
  --tw-invert: invert(1);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
    var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate)
    var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
}

.backdrop-blur,
.backdrop-blur-sm {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
    var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
    var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
}

.transition {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    -webkit-backdrop-filter;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-\[left\2c right\2c width\] {
  transition-property: left, right, width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-\[margin\2c opacity\] {
  transition-property: margin, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-\[width\2c height\2c padding\] {
  transition-property: width, height, padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-\[width\2c height\] {
  transition-property: width, height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-\[width\] {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-colors {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-none {
  transition-property: none;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.duration-1000 {
  transition-duration: 1s;
}

.duration-200 {
  transition-duration: 0.2s;
}

.duration-300 {
  transition-duration: 0.3s;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-linear {
  transition-timing-function: linear;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

@keyframes enter {
  0% {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(
        var(--tw-enter-translate-x, 0),
        var(--tw-enter-translate-y, 0),
        0
      )
      scale3d(
        var(--tw-enter-scale, 1),
        var(--tw-enter-scale, 1),
        var(--tw-enter-scale, 1)
      )
      rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(
        var(--tw-exit-translate-x, 0),
        var(--tw-exit-translate-y, 0),
        0
      )
      scale3d(
        var(--tw-exit-scale, 1),
        var(--tw-exit-scale, 1),
        var(--tw-exit-scale, 1)
      )
      rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in {
  animation-name: enter;
  animation-duration: 0.15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.fade-in-0 {
  --tw-enter-opacity: 0;
}

.fade-in-80 {
  --tw-enter-opacity: 0.8;
}

.zoom-in-95 {
  --tw-enter-scale: 0.95;
}

.duration-1000 {
  animation-duration: 1s;
}

.duration-200 {
  animation-duration: 0.2s;
}

.duration-300 {
  animation-duration: 0.3s;
}

.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-linear {
  animation-timing-function: linear;
}

.ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.running {
  animation-play-state: running;
}

.\@container\/card {
  container-type: inline-size;
  container-name: card;
}

.\@container\/main {
  container-type: inline-size;
  container-name: main;
}

.step {
  counter-increment: step;
}

.step:before {
  margin-right: 0.5rem;
  display: inline-flex;
  height: 2rem;
  width: 2rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-width: 4px;
  border-color: hsl(var(--background));
  background-color: hsl(var(--muted));
  text-align: center;
  text-indent: -1px;
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
}

@media (min-width: 768px) {
  .step:before {
    position: absolute;
    height: 2.25rem;
    width: 2.25rem;
    margin-left: -50px;
    margin-top: -4px;
  }
}

.step:before {
  content: counter(step);
}

.chunk-container {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
}

.chunk-container,
.chunk-container:after {
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.chunk-container:after {
  content: "";
  position: absolute;
  inset: -1rem;
  border-radius: calc(var(--radius) + 4px);
  border-width: 1px;
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),
    0 8px 10px -6px var(--tw-shadow-color);
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.border-grid {
  border-style: dashed;
  border-color: hsl(var(--border) / 0.5);
}

.border-grid:is(.dark *) {
  border-color: hsl(var(--border));
}

.container-wrapper {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 1400px;
  border-style: dashed;
  border-color: hsl(var(--border) / 0.7);
}

.container-wrapper:is(.dark *) {
  border-color: hsl(var(--border));
}

@media (min-width: 1400px) {
  .container-wrapper {
    border-left-width: 1px;
    border-right-width: 1px;
  }
}

@media (min-width: 1800px) {
  .container-wrapper {
    max-width: 1536px;
  }
}

.container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1536px;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 1280px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.\[--header-height\: calc\(theme\(spacing\.14\)\)\] {
  --header-height: calc(3.5rem);
}

.\[counter-reset\:step\] {
  counter-reset: step;
}

.file\:border-0::file-selector-button {
  border-width: 0;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}

.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}

.after\:absolute:after {
  content: var(--tw-content);
  position: absolute;
}

.after\:-inset-2:after {
  content: var(--tw-content);
  inset: -0.5rem;
}

.after\:inset-0:after {
  content: var(--tw-content);
  inset: 0;
}

.after\:inset-x-0:after {
  content: var(--tw-content);
  left: 0;
  right: 0;
}

.after\:inset-y-0:after {
  content: var(--tw-content);
  top: 0;
  bottom: 0;
}

.after\:inset-y-\[-2px\]:after {
  content: var(--tw-content);
  top: -2px;
  bottom: -2px;
}

.after\:left-0:after {
  content: var(--tw-content);
  left: 0;
}

.after\:left-1\/2:after {
  content: var(--tw-content);
  left: 50%;
}

.after\:right-0:after {
  content: var(--tw-content);
  right: 0;
}

.after\:top-1\/2:after {
  content: var(--tw-content);
  top: 50%;
}

.after\:z-0:after {
  content: var(--tw-content);
  z-index: 0;
}

.after\:flex:after {
  content: var(--tw-content);
  display: flex;
}

.after\:h-8:after {
  content: var(--tw-content);
  height: 2rem;
}

.after\:w-1:after {
  content: var(--tw-content);
  width: 0.25rem;
}

.after\:w-10:after {
  content: var(--tw-content);
  width: 2.5rem;
}

.after\:w-\[2px\]:after {
  content: var(--tw-content);
  width: 2px;
}

.after\:w-\[6px\]:after {
  content: var(--tw-content);
  width: 6px;
}

.after\:-translate-x-1\/2:after {
  --tw-translate-x: -50%;
}

.after\:-translate-x-1\/2:after,
.after\:-translate-y-1\/2:after {
  content: var(--tw-content);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:-translate-y-1\/2:after {
  --tw-translate-y: -50%;
}

.after\:translate-x-\[-1px\]:after {
  content: var(--tw-content);
  --tw-translate-x: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:items-center:after {
  content: var(--tw-content);
  align-items: center;
}

.after\:rounded-full:after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:rounded-lg:after {
  content: var(--tw-content);
  border-radius: var(--radius);
}

.after\:border-t:after {
  content: var(--tw-content);
  border-top-width: 1px;
}

.after\:border-border:after {
  content: var(--tw-content);
  border-color: hsl(var(--border));
}

.after\:bg-border:after {
  content: var(--tw-content);
  background-color: hsl(var(--border));
}

.after\:bg-zinc-950:after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity));
}

.after\:transition-all:after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.first\:mt-0:first-child {
  margin-top: 0;
}

.first\:rounded-l-md:first-child {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}

.first\:border-l:first-child {
  border-left-width: 1px;
}

.first\:fill-muted:first-child {
  fill: hsl(var(--muted));
}

.first\:pt-6:first-child {
  padding-top: 1.5rem;
}

.last\:mb-0:last-child {
  margin-bottom: 0;
}

.last\:rounded-r-md:last-child {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}

.last\:border-b-0:last-child {
  border-bottom-width: 0;
}

.last\:border-none:last-child {
  border-style: none;
}

.last\:fill-background:last-child {
  fill: hsl(var(--background));
}

.last\:pb-0:last-child {
  padding-bottom: 0;
}

.even\:border-l:nth-child(2n) {
  border-left-width: 1px;
}

.even\:bg-muted:nth-child(2n) {
  background-color: hsl(var(--muted));
}

.focus-within\:relative:focus-within {
  position: relative;
}

.focus-within\:z-20:focus-within {
  z-index: 20;
}

.focus-within\:ring-1:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-ring:focus-within {
  --tw-ring-color: hsl(var(--ring));
}

.hover\:z-30:hover {
  z-index: 30;
}

.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
}

.hover\:-translate-y-0\.5:hover,
.hover\:scale-105:hover {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
}

.hover\:border-accent:hover {
  border-color: hsl(var(--accent));
}

.hover\:border-border:hover {
  border-color: hsl(var(--border));
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-black:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / 0.8);
}

.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}

.hover\:bg-input\/30:hover {
  background-color: hsl(var(--input) / 0.3);
}

.hover\:bg-muted:hover {
  background-color: hsl(var(--muted));
}

.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / 0.5);
}

.hover\:bg-primary:hover {
  background-color: hsl(var(--primary));
}

.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / 0.8);
}

.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-secondary:hover {
  background-color: hsl(var(--secondary));
}

.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:bg-sidebar-accent:hover {
  background-color: hsl(var(--sidebar-accent));
}

.hover\:bg-transparent:hover {
  background-color: transparent;
}

.hover\:bg-zinc-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity));
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

.hover\:text-foreground\/80:hover {
  color: hsl(var(--foreground) / 0.8);
}

.hover\:text-muted-foreground:hover {
  color: hsl(var(--muted-foreground));
}

.hover\:text-primary:hover {
  color: hsl(var(--primary));
}

.hover\:text-primary-foreground:hover {
  color: hsl(var(--primary-foreground));
}

.hover\:text-sidebar-accent-foreground:hover {
  color: hsl(var(--sidebar-accent-foreground));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:text-zinc-50:hover {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]: hover {
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.after\:hover\:h-10:hover:after {
  content: var(--tw-content);
  height: 2.5rem;
}

.hover\:after\:bg-sidebar-border:hover:after {
  content: var(--tw-content);
  background-color: hsl(var(--sidebar-border));
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:bg-primary:focus {
  background-color: hsl(var(--primary));
}

.focus\:bg-zinc-700:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity));
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:text-primary-foreground:focus {
  color: hsl(var(--primary-foreground));
}

.focus\:text-white:focus {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.focus\:opacity-100:focus {
  opacity: 1;
}

.focus\:shadow-md:focus {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
    0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

.focus\:ring-1:focus,
.focus\:ring-2:focus {
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:border:focus-visible {
  border-width: 1px;
}

.focus-visible\:bg-background:focus-visible {
  background-color: hsl(var(--background));
}

.focus-visible\:bg-transparent:focus-visible {
  background-color: transparent;
}

.focus-visible\:bg-zinc-700:focus-visible {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity));
}

.focus-visible\:text-white:focus-visible {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-0:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-sidebar-ring:focus-visible {
  --tw-ring-color: hsl(var(--sidebar-ring));
}

.focus-visible\:ring-offset-0:focus-visible {
  --tw-ring-offset-width: 0px;
}

.focus-visible\:ring-offset-1:focus-visible {
  --tw-ring-offset-width: 1px;
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:ring-offset-background:focus-visible {
  --tw-ring-offset-color: hsl(var(--background));
}

.active\:bg-primary\/90:active {
  background-color: hsl(var(--primary) / 0.9);
}

.active\:bg-sidebar-accent:active {
  background-color: hsl(var(--sidebar-accent));
}

.active\:bg-zinc-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity));
}

.active\:text-primary-foreground:active {
  color: hsl(var(--primary-foreground));
}

.active\:text-sidebar-accent-foreground:active {
  color: hsl(var(--sidebar-accent-foreground));
}

.active\:text-white:active {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:rotate-0 {
  --tw-rotate: 0deg;
}

.group:hover .group-hover\:rotate-0,
.group:hover .group-hover\:rotate-45 {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-45 {
  --tw-rotate: 45deg;
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:text-foreground {
  color: hsl(var(--foreground));
}

.group:hover .group-hover\:underline {
  text-decoration-line: underline;
}

.group:hover .group-hover\:no-underline {
  text-decoration-line: none;
}

.group:hover .group-hover\:opacity-100,
.group\/menu-item:hover .group-hover\/menu-item\:opacity-100 {
  opacity: 1;
}

.group[data-collapsed="true"]
  .group-\[\[data-collapsed\=true\]\]\:justify-center {
  justify-content: center;
}

.group.destructive .group-\[\.destructive\]\:border-muted\/40 {
  border-color: hsl(var(--muted) / 0.4);
}

.group.toaster .group-\[\.toaster\]\:border-border {
  border-color: hsl(var(--border));
}

.group.toast .group-\[\.toast\]\:bg-muted {
  background-color: hsl(var(--muted));
}

.group.toast .group-\[\.toast\]\:bg-primary {
  background-color: hsl(var(--primary));
}

.group.toaster .group-\[\.toaster\]\:bg-background {
  background-color: hsl(var(--background));
}

.group[data-collapsed="true"] .group-\[\[data-collapsed\=true\]\]\:px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.group.destructive .group-\[\.destructive\]\:text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}

.group.toast .group-\[\.toast\]\:text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.group.toast .group-\[\.toast\]\:text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.group.toaster .group-\[\.toaster\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group.toaster .group-\[\.toaster\]\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
    0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group.destructive
  .group-\[\.destructive\]\:hover\:border-destructive\/30:hover {
  border-color: hsl(var(--destructive) / 0.3);
}

.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--destructive) / var(--tw-bg-opacity));
}

.group.destructive
  .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive-foreground) / var(--tw-text-opacity));
}

.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover {
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity));
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--destructive) / var(--tw-ring-opacity));
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity));
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus {
  --tw-ring-offset-color: #dc2626;
}

.peer\/menu-button:hover
  ~ .peer-hover\/menu-button\:text-sidebar-accent-foreground {
  color: hsl(var(--sidebar-accent-foreground));
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}

.has-\[\[data-variant\=inset\]\]\:bg-sidebar:has([data-variant="inset"]) {
  background-color: hsl(var(--sidebar-background));
}

.has-\[\:disabled\]\:opacity-50:has(:disabled) {
  opacity: 0.5;
}

.group\/sidebar-wrapper:has([data-collapsible="icon"])
  .group-has-\[\[data-collapsible\=icon\]\]\/sidebar-wrapper\:h-12 {
  height: 3rem;
}

.group\/menu-item:has([data-state="open"])
  .group-has-\[\[data-state\=open\]\]\/menu-item\:bg-sidebar-accent {
  background-color: hsl(var(--sidebar-accent));
}

.group\/menu-item:has([data-sidebar="menu-action"])
  .group-has-\[\[data-sidebar\=menu-action\]\]\/menu-item\:pr-8 {
  padding-right: 2rem;
}

.aria-checked\:border-\[--color-1\][aria-checked="true"] {
  border-color: var(--color-1);
}

.aria-disabled\:pointer-events-none[aria-disabled="true"] {
  pointer-events: none;
}

.aria-disabled\:opacity-50[aria-disabled="true"] {
  opacity: 0.5;
}

.aria-selected\:bg-accent[aria-selected="true"] {
  background-color: hsl(var(--accent));
}

.aria-selected\:bg-accent\/50[aria-selected="true"] {
  background-color: hsl(var(--accent) / 0.5);
}

.aria-selected\:text-accent-foreground[aria-selected="true"] {
  color: hsl(var(--accent-foreground));
}

.aria-selected\:text-muted-foreground[aria-selected="true"] {
  color: hsl(var(--muted-foreground));
}

.aria-selected\:opacity-100[aria-selected="true"] {
  opacity: 1;
}

.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"],
.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[dragging\=true\]\:z-10[data-dragging="true"] {
  z-index: 10;
}

.data-\[state\=active\]\:flex[data-state="active"] {
  display: flex;
}

.data-\[orientation\=vertical\]\:h-4[data-orientation="vertical"] {
  height: 1rem;
}

.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"] {
  height: 1px;
}

.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"] {
  width: 100%;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"],
.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
}

.data-\[side\=right\]\:translate-x-1[data-side="right"],
.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
}

.data-\[state\=checked\]\:translate-x-4[data-state="checked"] {
  --tw-translate-x: 1rem;
}

.data-\[state\=checked\]\:translate-x-4[data-state="checked"],
.data-\[state\=checked\]\:translate-x-5[data-state="checked"] {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=checked\]\:translate-x-5[data-state="checked"] {
  --tw-translate-x: 1.25rem;
}

.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"],
.data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
  --tw-translate-x: var(--radix-toast-swipe-end-x);
}

.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"],
.data-\[swipe\=move\]\:
  translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
  --tw-translate-x: var(--radix-toast-swipe-move-x);
}

.data-\[state\=open\]\:rotate-90[data-state="open"] {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes accordion-up {
  0% {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}

.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
  animation: accordion-up 0.2s ease-out;
}

@keyframes accordion-down {
  0% {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}

.data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
  animation: accordion-down 0.2s ease-out;
}

.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"] {
  flex-direction: column;
}

.data-\[active\=true\]\:border-sidebar-primary[data-active="true"] {
  border-color: hsl(var(--sidebar-primary));
}

.data-\[state\=active\]\:border-b-primary[data-state="active"] {
  border-bottom-color: hsl(var(--primary));
}

.data-\[state\=active\]\:border-b-zinc-50[data-state="active"] {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(250 250 250 / var(--tw-border-opacity));
}

.data-\[active\=true\]\:bg-muted[data-active="true"] {
  background-color: hsl(var(--muted));
}

.data-\[active\=true\]\:bg-muted\/50[data-active="true"] {
  background-color: hsl(var(--muted) / 0.5);
}

.data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
  background-color: hsl(var(--sidebar-accent));
}

.data-\[active\=true\]\:bg-sidebar-primary[data-active="true"] {
  background-color: hsl(var(--sidebar-primary));
}

.data-\[active\=true\]\:bg-transparent[data-active="true"] {
  background-color: transparent;
}

.data-\[active\=true\]\:bg-zinc-700[data-active="true"] {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity));
}

.data-\[selected\=\'true\'\]\:bg-accent[data-selected="true"],
.data-\[selected\=true\]\:bg-accent[data-selected="true"] {
  background-color: hsl(var(--accent));
}
.data-\[selected\=true\]\:bg-primary[data-selected="true"] {
  background-color: hsl(var(--primary));
}
.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}
.data-\[state\=active\]\:bg-transparent[data-state="active"] {
  background-color: transparent;
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary));
}
.data-\[state\=on\]\:bg-accent[data-state="on"],
.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
  background-color: hsl(var(--accent) / 0.5);
}
.data-\[state\=open\]\:bg-muted[data-state="open"] {
  background-color: hsl(var(--muted));
}
.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: hsl(var(--secondary));
}
.data-\[state\=open\]\:bg-sidebar-accent[data-state="open"] {
  background-color: hsl(var(--sidebar-accent));
}
.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: hsl(var(--muted));
}
.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
  background-color: hsl(var(--input));
}
.data-\[slot\=sidebar-menu-button\]\:\!p-1\.5[data-slot="sidebar-menu-button"] {
  padding: 0.375rem !important;
}
.data-\[collapsed\=true\]\:py-2[data-collapsed="true"] {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.data-\[active\=true\]\:font-medium[data-active="true"] {
  font-weight: 500;
}
.data-\[active\=true\]\:text-foreground[data-active="true"] {
  color: hsl(var(--foreground));
}
.data-\[active\=true\]\:text-primary[data-active="true"] {
  color: hsl(var(--primary));
}
.data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
  color: hsl(var(--sidebar-accent-foreground));
}
.data-\[active\=true\]\:text-white[data-active="true"] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
  color: hsl(var(--muted-foreground));
}
.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
  color: hsl(var(--accent-foreground));
}
.data-\[selected\=true\]\:text-primary-foreground[data-selected="true"] {
  color: hsl(var(--primary-foreground));
}
.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}
.data-\[state\=active\]\:text-zinc-50[data-state="active"] {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity));
}
.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
  color: hsl(var(--primary-foreground));
}
.data-\[state\=on\]\:text-accent-foreground[data-state="on"],
.data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
  color: hsl(var(--accent-foreground));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}
.data-\[state\=open\]\:text-sidebar-accent-foreground[data-state="open"] {
  color: hsl(var(--sidebar-accent-foreground));
}
.data-\[disabled\=true\]\:opacity-50[data-disabled="true"],
.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}
.data-\[dragging\=true\]\:opacity-80[data-dragging="true"] {
  opacity: 0.8;
}
.data-\[state\=open\]\:opacity-100[data-state="open"] {
  opacity: 1;
}
.data-\[state\=active\]\:shadow[data-state="active"] {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
    0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.data-\[state\=active\]\:shadow-none[data-state="active"] {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.data-\[state\=active\]\:shadow-sm[data-state="active"] {
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.data-\[swipe\=move\]\:transition-none[data-swipe="move"] {
  transition-property: none;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: 0.3s;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: 0.5s;
}
.data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"],
.data-\[state\=open\]\:animate-in[data-state="open"],
.data-\[state\=visible\]\:animate-in[data-state="visible"] {
  animation-name: enter;
  animation-duration: 0.15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"],
.data-\[state\=closed\]\:animate-out[data-state="closed"],
.data-\[state\=hidden\]\:animate-out[data-state="hidden"],
.data-\[swipe\=end\]\:animate-out[data-swipe="end"] {
  animation-name: exit;
  animation-duration: 0.15s;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
  --tw-enter-opacity: 0;
}
.data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"],
.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}
.data-\[state\=closed\]\:fade-out-80[data-state="closed"] {
  --tw-exit-opacity: 0.8;
}
.data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
  --tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"],
.data-\[state\=visible\]\:fade-in[data-state="visible"] {
  --tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: 0.95;
}
.data-\[state\=open\]\:zoom-in-90[data-state="open"] {
  --tw-enter-scale: 0.9;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: 0.95;
}
.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
  --tw-enter-translate-x: 13rem;
}
.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
  --tw-enter-translate-x: -13rem;
}
.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
  --tw-exit-translate-x: 13rem;
}
.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
  --tw-exit-translate-x: -13rem;
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}
.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}
.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}
.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"],
.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}
.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}
.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}
.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}
.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}
.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}
.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-top-full[data-state="open"] {
  --tw-enter-translate-y: -100%;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: 0.3s;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: 0.5s;
}
.\*\:data-\[slot\=card\]\:bg-gradient-to-t[data-slot="card"] > * {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.\*\:data-\[slot\=card\]\:from-primary\/5[data-slot="card"] > * {
  --tw-gradient-from: hsl(var(--primary) / 0.05)
    var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.\*\:data-\[slot\=card\]\:to-card[data-slot="card"] > * {
  --tw-gradient-to: hsl(var(--card)) var(--tw-gradient-to-position);
}
.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  left: 0;
}
.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  height: 0.25rem;
}
.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  width: 100%;
}
.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=open\]\:hover\:bg-accent:hover[data-state="open"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:hover\:bg-sidebar-accent:hover[data-state="open"] {
  background-color: hsl(var(--sidebar-accent));
}
.data-\[state\=open\]\:hover\:bg-zinc-700:hover[data-state="open"] {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity));
}
.data-\[state\=open\]\:hover\:text-sidebar-accent-foreground:hover[data-state="open"] {
  color: hsl(var(--sidebar-accent-foreground));
}
.data-\[state\=open\]\:hover\:text-white:hover[data-state="open"] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.data-\[state\=open\]\:focus\:bg-accent:focus[data-state="open"] {
  background-color: hsl(var(--accent));
}
.group[data-collapsible="offcanvas"]
  .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] {
  left: calc(var(--sidebar-width) * -1);
}
.group[data-collapsible="offcanvas"]
  .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] {
  right: calc(var(--sidebar-width) * -1);
}
.group[data-side="left"] .group-data-\[side\=left\]\:-right-4 {
  right: -1rem;
}
.group[data-side="right"] .group-data-\[side\=right\]\:left-0 {
  left: 0;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:-mt-8 {
  margin-top: -2rem;
}
.group\/calendar-item[data-active="true"]
  .group-data-\[active\=true\]\/calendar-item\:block {
  display: block;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:hidden,
.group\/block-view-wrapper[data-view="code"]
  .group-data-\[view\=code\]\/block-view-wrapper\:hidden,
.group\/block-view-wrapper[data-view="preview"]
  .group-data-\[view\=preview\]\/block-view-wrapper\:hidden,
.group\/collapsible[data-state="closed"]
  .group-data-\[state\=closed\]\/collapsible\:hidden,
.group\/collapsible[data-state="open"]
  .group-data-\[state\=open\]\/collapsible\:hidden {
  display: none;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!size-8 {
  width: 2rem !important;
  height: 2rem !important;
}
.group[data-collapsible="icon"]
  .group-data-\[collapsible\=icon\]\:w-\[--sidebar-width-icon\] {
  width: var(--sidebar-width-icon);
}
.group[data-collapsible="icon"]
  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\] {
  width: calc(var(--sidebar-width-icon) + 1rem);
}
.group[data-collapsible="icon"]
  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\] {
  width: calc(var(--sidebar-width-icon) + 1rem + 2px);
}
.group[data-collapsible="offcanvas"]
  .group-data-\[collapsible\=offcanvas\]\:w-0 {
  width: 0;
}
.group[data-collapsible="offcanvas"]
  .group-data-\[collapsible\=offcanvas\]\:translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-side="right"] .group-data-\[side\=right\]\:rotate-180,
.group\/collapsible[data-state="open"]
  .group-data-\[state\=open\]\/collapsible\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/collapsible[data-state="open"]
  .group-data-\[state\=open\]\/collapsible\:rotate-90 {
  --tw-rotate: 90deg;
}
.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180,
.group\/collapsible[data-state="open"]
  .group-data-\[state\=open\]\/collapsible\:rotate-90 {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180 {
  --tw-rotate: 180deg;
}
.group[data-collapsible="icon"]
  .group-data-\[collapsible\=icon\]\:overflow-hidden {
  overflow: hidden;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:rounded-lg {
  border-radius: var(--radius);
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border {
  border-width: 1px;
}
.group[data-side="left"] .group-data-\[side\=left\]\:border-r {
  border-right-width: 1px;
}
.group[data-side="right"] .group-data-\[side\=right\]\:border-l {
  border-left-width: 1px;
}
.group[data-variant="floating"]
  .group-data-\[variant\=floating\]\:border-sidebar-border {
  border-color: hsl(var(--sidebar-border));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-0 {
  padding: 0 !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-2 {
  padding: 0.5rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:opacity-0 {
  opacity: 0;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:shadow {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
    0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group[data-collapsible="offcanvas"]
  .group-data-\[collapsible\=offcanvas\]\:after\:left-full:after {
  content: var(--tw-content);
  left: 100%;
}
.group[data-collapsible="offcanvas"]
  .group-data-\[collapsible\=offcanvas\]\:hover\:bg-sidebar:hover {
  background-color: hsl(var(--sidebar-background));
}
.peer\/menu-button[data-size="default"]
  ~ .peer-data-\[size\=default\]\/menu-button\:top-1\.5 {
  top: 0.375rem;
}
.peer\/menu-button[data-size="lg"]
  ~ .peer-data-\[size\=lg\]\/menu-button\:top-2\.5 {
  top: 0.625rem;
}
.peer\/menu-button[data-size="sm"]
  ~ .peer-data-\[size\=sm\]\/menu-button\:top-1 {
  top: 0.25rem;
}
.peer[data-state="checked"] ~ .peer-data-\[state\=checked\]\:border-primary {
  border-color: hsl(var(--primary));
}
.peer\/menu-button[data-active="true"]
  ~ .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground {
  color: hsl(var(--sidebar-accent-foreground));
}
.peer\/menu-button[data-active="true"]
  ~ .peer-data-\[active\=true\]\/menu-button\:opacity-100 {
  opacity: 1;
}
@container main (min-width: 36rem) {
  .\@xl\/main\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@container main (min-width: 56rem) {
  .\@4xl\/main\:flex {
    display: flex;
  }
  .\@4xl\/main\:hidden {
    display: none;
  }
}
@container main (min-width: 64rem) {
  .\@5xl\/main\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
@container card (min-width: 250px) {
  .\@\[250px\]\/card\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}
@container card (min-width: 540px) {
  .\@\[540px\]\/card\:block {
    display: block;
  }
  .\@\[540px\]\/card\:hidden {
    display: none;
  }
}
@container card (min-width: 767px) {
  .\@\[767px\]\/card\:flex {
    display: flex;
  }
  .\@\[767px\]\/card\:hidden {
    display: none;
  }
}
@supports (
  (-webkit-backdrop-filter: var(--tw)) or (backdrop-filter: var(--tw))
) {
  .supports-\[backdrop-filter\]\:bg-background\/60 {
    background-color: hsl(var(--background) / 0.6);
  }
}
.dark\:block:is(.dark *) {
  display: block;
}
.dark\:hidden:is(.dark *) {
  display: none;
}
.dark\:-rotate-90:is(.dark *) {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:rotate-0:is(.dark *) {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:scale-0:is(.dark *) {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:scale-100:is(.dark *) {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:border-l:is(.dark *) {
  border-left-width: 1px;
}
.dark\:border-r:is(.dark *) {
  border-right-width: 1px;
}
.dark\:border-amber-950:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(69 26 3 / var(--tw-border-opacity));
}
.dark\:border-blue-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(30 58 138 / var(--tw-border-opacity));
}
.dark\:border-destructive:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: hsl(var(--destructive) / var(--tw-border-opacity));
}
.dark\:bg-amber-950\/50:is(.dark *) {
  background-color: rgba(69, 26, 3, 0.5);
}
.dark\:bg-blue-950:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(23 37 84 / var(--tw-bg-opacity));
}
.dark\:bg-muted:is(.dark *) {
  background-color: hsl(var(--muted));
}
.dark\:bg-white:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.dark\:bg-zinc-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity));
}
.dark\:bg-zinc-950:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity));
}
.dark\:text-black:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
.dark\:text-foreground:is(.dark *) {
  color: hsl(var(--foreground));
}
.dark\:text-green-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity));
}
.dark\:text-muted-foreground:is(.dark *) {
  color: hsl(var(--muted-foreground));
}
.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark\:text-zinc-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity));
}
.dark\:brightness-\[0\.2\]:is(.dark *) {
  --tw-brightness: brightness(0.2);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
    var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate)
    var(--tw-sepia) var(--tw-drop-shadow);
}
.dark\:grayscale:is(.dark *) {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
    var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate)
    var(--tw-sepia) var(--tw-drop-shadow);
}
.dark\:hover\:bg-muted:hover:is(.dark *) {
  background-color: hsl(var(--muted));
}
.dark\:hover\:text-white:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark\:\*\:data-\[slot\=card\]\:bg-card[data-slot="card"] > :is(.dark *) {
  background-color: hsl(var(--card));
}
@media (min-width: 640px) {
  .sm\:not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
  .sm\:static {
    position: static;
  }
  .sm\:bottom-0 {
    bottom: 0;
  }
  .sm\:right-0 {
    right: 0;
  }
  .sm\:top-auto {
    top: auto;
  }
  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }
  .sm\:ml-0 {
    margin-left: 0;
  }
  .sm\:ml-auto {
    margin-left: auto;
  }
  .sm\:mt-0 {
    margin-top: 0;
  }
  .sm\:block {
    display: block;
  }
  .sm\:flex {
    display: flex;
  }
  .sm\:table-cell {
    display: table-cell;
  }
  .sm\:hidden {
    display: none;
  }
  .sm\:aspect-\[2\/3\] {
    aspect-ratio: 2/3;
  }
  .sm\:h-auto {
    height: auto;
  }
  .sm\:max-h-\[90vh\] {
    max-height: 90vh;
  }
  .sm\:w-\[1280px\] {
    width: 1280px;
  }
  .sm\:w-\[300px\] {
    width: 300px;
  }
  .sm\:w-\[350px\] {
    width: 350px;
  }
  .sm\:w-\[540px\] {
    width: 540px;
  }
  .sm\:w-auto {
    width: auto;
  }
  .sm\:max-w-\[425px\] {
    max-width: 425px;
  }
  .sm\:max-w-\[475px\] {
    max-width: 475px;
  }
  .sm\:max-w-\[625px\] {
    max-width: 625px;
  }
  .sm\:max-w-md {
    max-width: 28rem;
  }
  .sm\:max-w-sm {
    max-width: 24rem;
  }
  .sm\:max-w-xs {
    max-width: 20rem;
  }
  .sm\:flex-initial {
    flex: 0 1 auto;
  }
  .sm\:grow-0 {
    flex-grow: 0;
  }
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .sm\:grid-cols-\[260px_1fr\] {
    grid-template-columns: 260px 1fr;
  }
  .sm\:flex-row {
    flex-direction: row;
  }
  .sm\:flex-col {
    flex-direction: column;
  }
  .sm\:items-center {
    align-items: center;
  }
  .sm\:justify-start {
    justify-content: flex-start;
  }
  .sm\:justify-end {
    justify-content: flex-end;
  }
  .sm\:justify-between {
    justify-content: space-between;
  }
  .sm\:gap-2 {
    gap: 0.5rem;
  }
  .sm\:gap-2\.5 {
    gap: 0.625rem;
  }
  .sm\:gap-4 {
    gap: 1rem;
  }
  .sm\:gap-6 {
    gap: 1.5rem;
  }
  .sm\:space-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
  .sm\:whitespace-nowrap {
    white-space: nowrap;
  }
  .sm\:rounded-lg {
    border-radius: var(--radius);
  }
  .sm\:border-0 {
    border-width: 0;
  }
  .sm\:border-l {
    border-left-width: 1px;
  }
  .sm\:border-t-0 {
    border-top-width: 0;
  }
  .sm\:bg-transparent {
    background-color: transparent;
  }
  .sm\:p-10 {
    padding: 2.5rem;
  }
  .sm\:p-6 {
    padding: 1.5rem;
  }
  .sm\:p-8 {
    padding: 2rem;
  }
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .sm\:py-0 {
    padding-top: 0;
    padding-bottom: 0;
  }
  .sm\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
  .sm\:pl-14 {
    padding-left: 3.5rem;
  }
  .sm\:pl-2 {
    padding-left: 0.5rem;
  }
  .sm\:pr-12 {
    padding-right: 3rem;
  }
  .sm\:pt-0 {
    padding-top: 0;
  }
  .sm\:pt-6 {
    padding-top: 1.5rem;
  }
  .sm\:text-left {
    text-align: left;
  }
  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"] {
    --tw-enter-translate-y: 100%;
  }
}
@media (min-width: 768px) {
  .md\:absolute {
    position: absolute;
  }
  .md\:sticky {
    position: sticky;
  }
  .md\:right-8 {
    right: 2rem;
  }
  .md\:top-8 {
    top: 2rem;
  }
  .md\:order-1 {
    order: 1;
  }
  .md\:order-2 {
    order: 2;
  }
  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }
  .md\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
  .md\:-ml-4 {
    margin-left: -1rem;
  }
  .md\:ml-4 {
    margin-left: 1rem;
  }
  .md\:ml-auto {
    margin-left: auto;
  }
  .md\:block {
    display: block;
  }
  .md\:inline-block {
    display: inline-block;
  }
  .md\:inline {
    display: inline;
  }
  .md\:\!flex {
    display: flex !important;
  }
  .md\:flex {
    display: flex;
  }
  .md\:table-cell {
    display: table-cell;
  }
  .md\:grid {
    display: grid;
  }
  .md\:hidden {
    display: none;
  }
  .md\:aspect-auto {
    aspect-ratio: auto;
  }
  .md\:h-16 {
    height: 4rem;
  }
  .md\:h-8 {
    height: 2rem;
  }
  .md\:h-\[--height\] {
    height: var(--height);
  }
  .md\:h-\[200px\] {
    height: 200px;
  }
  .md\:max-h-\[500px\] {
    max-height: 500px;
  }
  .md\:min-h-\[700px\] {
    min-height: 700px;
  }
  .md\:min-h-min {
    min-height: -moz-min-content;
    min-height: min-content;
  }
  .md\:w-2\/3 {
    width: 66.666667%;
  }
  .md\:w-40 {
    width: 10rem;
  }
  .md\:w-8 {
    width: 2rem;
  }
  .md\:w-\[100px\] {
    width: 100px;
  }
  .md\:w-\[200px\] {
    width: 200px;
  }
  .md\:w-\[400px\] {
    width: 400px;
  }
  .md\:w-\[450px\] {
    width: 450px;
  }
  .md\:w-\[500px\] {
    width: 500px;
  }
  .md\:w-\[700px\] {
    width: 700px;
  }
  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
    width: var(--radix-navigation-menu-viewport-width);
  }
  .md\:w-auto {
    width: auto;
  }
  .md\:min-w-\[450px\] {
    min-width: 450px;
  }
  .md\:max-w-3xl {
    max-width: 48rem;
  }
  .md\:max-w-\[200px\] {
    max-width: 200px;
  }
  .md\:max-w-\[420px\] {
    max-width: 420px;
  }
  .md\:max-w-\[700px\] {
    max-width: 700px;
  }
  .md\:max-w-none {
    max-width: none;
  }
  .md\:flex-1 {
    flex: 1 1 0%;
  }
  .md\:flex-none {
    flex: none;
  }
  .md\:grow-0 {
    flex-grow: 0;
  }
  .md\:basis-1\/2 {
    flex-basis: 50%;
  }
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .md\:grid-cols-\[180px_1fr\] {
    grid-template-columns: 180px 1fr;
  }
  .md\:grid-cols-\[1fr_200px\] {
    grid-template-columns: 1fr 200px;
  }
  .md\:grid-cols-\[1fr_250px\] {
    grid-template-columns: 1fr 250px;
  }
  .md\:grid-cols-\[220px_1fr\] {
    grid-template-columns: 220px 1fr;
  }
  .md\:grid-cols-\[220px_minmax\(0\2c 1fr\)\] {
    grid-template-columns: 220px minmax(0, 1fr);
  }
  .md\:flex-row {
    flex-direction: row;
  }
  .md\:flex-row-reverse {
    flex-direction: row-reverse;
  }
  .md\:items-start {
    align-items: flex-start;
  }
  .md\:items-center {
    align-items: center;
  }
  .md\:justify-start {
    justify-content: flex-start;
  }
  .md\:justify-end {
    justify-content: flex-end;
  }
  .md\:gap-2 {
    gap: 0.5rem;
  }
  .md\:gap-4 {
    gap: 1rem;
  }
  .md\:gap-5 {
    gap: 1.25rem;
  }
  .md\:gap-6 {
    gap: 1.5rem;
  }
  .md\:gap-8 {
    gap: 2rem;
  }
  .md\:rounded-lg {
    border-radius: var(--radius);
  }
  .md\:border-l {
    border-left-width: 1px;
  }
  .md\:p-0 {
    padding: 0;
  }
  .md\:p-10 {
    padding: 2.5rem;
  }
  .md\:p-4 {
    padding: 1rem;
  }
  .md\:p-8 {
    padding: 2rem;
  }
  .md\:px-0 {
    padding-left: 0;
    padding-right: 0;
  }
  .md\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .md\:py-0 {
    padding-top: 0;
    padding-bottom: 0;
  }
  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }
  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
  .md\:pl-4 {
    padding-left: 1rem;
  }
  .md\:pl-8 {
    padding-left: 2rem;
  }
  .md\:pr-\[14px\] {
    padding-right: 14px;
  }
  .md\:pt-0 {
    padding-top: 0;
  }
  .md\:text-left {
    text-align: left;
  }
  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  .md\:opacity-0 {
    opacity: 0;
  }
  .md\:shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),
      0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
      var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .after\:md\:hidden:after {
    content: var(--tw-content);
    display: none;
  }
  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:m-2 {
    margin: 0.5rem;
  }
  .peer[data-state="collapsed"][data-variant="inset"]
    ~ .md\:peer-data-\[state\=collapsed\]\:peer-data-\[variant\=inset\]\:ml-2 {
    margin-left: 0.5rem;
  }
  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:ml-0 {
    margin-left: 0;
  }
  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:rounded-xl {
    border-radius: calc(var(--radius) + 4px);
  }
  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:shadow {
    --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
      0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
      var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .md\:dark\:hidden:is(.dark *) {
    display: none;
  }
}
@media (min-width: 1024px) {
  .lg\:sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .lg\:sticky {
    position: sticky;
  }
  .lg\:bottom-auto {
    bottom: auto;
  }
  .lg\:top-20 {
    top: 5rem;
  }
  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }
  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }
  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }
  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }
  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }
  .lg\:ml-0 {
    margin-left: 0;
  }
  .lg\:mr-6 {
    margin-right: 1.5rem;
  }
  .lg\:block {
    display: block;
  }
  .lg\:inline-block {
    display: inline-block;
  }
  .lg\:inline {
    display: inline;
  }
  .lg\:flex {
    display: flex;
  }
  .lg\:inline-flex {
    display: inline-flex;
  }
  .lg\:hidden {
    display: none;
  }
  .lg\:h-\[60px\] {
    height: 60px;
  }
  .lg\:min-h-\[580px\] {
    min-height: 580px;
  }
  .lg\:min-h-\[700px\] {
    min-height: 700px;
  }
  .lg\:w-1\/3 {
    width: 33.333333%;
  }
  .lg\:w-1\/5 {
    width: 20%;
  }
  .lg\:w-56 {
    width: 14rem;
  }
  .lg\:w-\[250px\] {
    width: 250px;
  }
  .lg\:w-\[300px\] {
    width: 300px;
  }
  .lg\:w-\[320px\] {
    width: 320px;
  }
  .lg\:w-\[500px\] {
    width: 500px;
  }
  .lg\:w-\[600px\] {
    width: 600px;
  }
  .lg\:w-auto {
    width: auto;
  }
  .lg\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }
  .lg\:max-w-2xl {
    max-width: 42rem;
  }
  .lg\:max-w-\[20rem\] {
    max-width: 20rem;
  }
  .lg\:max-w-\[22rem\] {
    max-width: 22rem;
  }
  .lg\:max-w-\[300px\] {
    max-width: 300px;
  }
  .lg\:max-w-\[800px\] {
    max-width: 800px;
  }
  .lg\:max-w-md {
    max-width: 28rem;
  }
  .lg\:max-w-none {
    max-width: none;
  }
  .lg\:basis-1\/3 {
    flex-basis: 33.333333%;
  }
  .lg\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .lg\:grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .lg\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
  .lg\:grid-cols-\[\.75fr_1fr\] {
    grid-template-columns: 0.75fr 1fr;
  }
  .lg\:grid-cols-\[240px_minmax\(0\2c 1fr\)\] {
    grid-template-columns: 240px minmax(0, 1fr);
  }
  .lg\:grid-cols-\[250px_1fr\] {
    grid-template-columns: 250px 1fr;
  }
  .lg\:grid-cols-\[280px_1fr\] {
    grid-template-columns: 280px 1fr;
  }
  .lg\:grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }
  .lg\:flex-row {
    flex-direction: row;
  }
  .lg\:flex-col {
    flex-direction: column;
  }
  .lg\:justify-start {
    justify-content: flex-start;
  }
  .lg\:gap-1 {
    gap: 0.25rem;
  }
  .lg\:gap-10 {
    gap: 2.5rem;
  }
  .lg\:gap-2 {
    gap: 0.5rem;
  }
  .lg\:gap-4 {
    gap: 1rem;
  }
  .lg\:gap-6 {
    gap: 1.5rem;
  }
  .lg\:gap-8 {
    gap: 2rem;
  }
  .lg\:space-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }
  .lg\:space-x-12 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(3rem * var(--tw-space-x-reverse));
    margin-left: calc(3rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .lg\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .lg\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
  .lg\:space-y-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
  }
  .lg\:border-l {
    border-left-width: 1px;
  }
  .lg\:p-6 {
    padding: 1.5rem;
  }
  .lg\:p-8 {
    padding: 2rem;
  }
  .lg\:px-0 {
    padding-left: 0;
    padding-right: 0;
  }
  .lg\:px-2\.5 {
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }
  .lg\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .lg\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
  .lg\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }
  .lg\:leading-\[1\.1\] {
    line-height: 1.1;
  }
}
@media (min-width: 1280px) {
  .xl\:not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }
  .xl\:col-span-2 {
    grid-column: span 2 / span 2;
  }
  .xl\:col-span-5 {
    grid-column: span 5 / span 5;
  }
  .xl\:col-span-6 {
    grid-column: span 6 / span 6;
  }
  .xl\:block {
    display: block;
  }
  .xl\:flex {
    display: flex;
  }
  .xl\:table-column {
    display: table-column;
  }
  .xl\:grid {
    display: grid;
  }
  .xl\:hidden {
    display: none;
  }
  .xl\:min-h-\[700px\] {
    min-height: 700px;
  }
  .xl\:w-64 {
    width: 16rem;
  }
  .xl\:w-\[86px\] {
    width: 86px;
  }
  .xl\:max-w-\[25rem\] {
    max-width: 25rem;
  }
  .xl\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .xl\:grid-cols-11 {
    grid-template-columns: repeat(11, minmax(0, 1fr));
  }
  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .xl\:grid-cols-\[1fr_300px\] {
    grid-template-columns: 1fr 300px;
  }
  .xl\:gap-10 {
    gap: 2.5rem;
  }
  .xl\:gap-4 {
    gap: 1rem;
  }
  .xl\:gap-6 {
    gap: 1.5rem;
  }
  .xl\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }
  .xl\:whitespace-nowrap {
    white-space: nowrap;
  }
  .xl\:pl-3 {
    padding-left: 0.75rem;
  }
  .xl\:pl-4 {
    padding-left: 1rem;
  }
  .xl\:pt-3 {
    padding-top: 0.75rem;
  }
  .xl\:pt-4 {
    padding-top: 1rem;
  }
}
@media (min-width: 1536px) {
  .\32xl\:block {
    display: block;
  }
  .\32xl\:hidden {
    display: none;
  }
}
.\[\&\+div\]\:text-xs + div {
  font-size: 0.75rem;
  line-height: 1rem;
}
.\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has(> .day-range-end) {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has(> .day-range-start) {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
  border-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
  background-color: hsl(var(--accent));
}
.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has(
    [aria-selected]
  ):first-child {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has(
    [aria-selected]
  ):last-child {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has(
    [aria-selected].day-outside
  ) {
  background-color: hsl(var(--accent) / 0.5);
}
.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has(
    [aria-selected].day-range-end
  ) {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[data-state\=checked\]\)\>div\]\:border-primary:has(
    [data-state="checked"]
  )
  > div {
  border-color: hsl(var(--primary));
}
.\[\&\:has\(\[data-state\=checked\]\)\]\:border-primary:has(
    [data-state="checked"]
  ) {
  border-color: hsl(var(--primary));
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pl-3:has([role="checkbox"]) {
  padding-left: 0.75rem;
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
  padding-right: 0;
}
.\[\&\:not\(\:first-child\)\]\:mt-6:not(:first-child) {
  margin-top: 1.5rem;
}
.\[\&\>\*\]\:basis-1\/4 > * {
  flex-basis: 25%;
}
.\[\&\>\*\]\:justify-center > * {
  justify-content: center;
}
.\[\&\>\[data-sidebar\=sidebar\]\]\:flex-row > [data-sidebar="sidebar"] {
  flex-direction: row;
}
.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
  --tw-translate-y: 2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>a\:first-child\]\:text-primary > a:first-child {
  color: hsl(var(--primary));
}
.\[\&\>button\]\:hidden > button {
  display: none;
}
.\[\&\>button\]\:w-\[260px\] > button {
  width: 260px;
}
.\[\&\>div\.bg-muted\]\:shrink-0 > div.bg-muted {
  flex-shrink: 0;
}
.\[\&\>div\.min-h-\[350px\]\]\:p-6 > div.min-h-[\33 50px] {
  padding: 1.5rem;
}
.\[\&\>div\>div\:first-child\]\:hidden > div > div:first-child {
  display: none;
}
.\[\&\>div\]\:relative > div {
  position: relative;
}
.\[\&\>div\]\:flex > div {
  display: flex;
}
.\[\&\>div\]\:h-\[137px\] > div {
  height: 137px;
}
.\[\&\>div\]\:w-\[224px\] > div {
  width: 224px;
}
.\[\&\>div\]\:w-full > div {
  width: 100%;
}
.\[\&\>div\]\:max-w-full > div {
  max-width: 100%;
}
.\[\&\>div\]\:max-w-md > div {
  max-width: 28rem;
}
.\[\&\>div\]\:flex-1 > div {
  flex: 1 1 0%;
}
.\[\&\>div\]\:items-center > div {
  align-items: center;
}
.\[\&\>div\]\:justify-center > div {
  justify-content: center;
}
.\[\&\>div\]\:rounded-none > div {
  border-radius: 0;
}
.\[\&\>div\]\:border-0 > div {
  border-width: 0;
}
.\[\&\>div\]\:border-b > div {
  border-bottom-width: 1px;
}
.\[\&\>div\]\:border-none > div {
  border-style: none;
}
.\[\&\>div\]\:p-0 > div {
  padding: 0;
}
.\[\&\>div\]\:p-4 > div {
  padding: 1rem;
}
.\[\&\>div\]\:shadow-none > div {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.\[\&\>form\]\:flex > form {
  display: flex;
}
.\[\&\>h3\]\:step > h3 {
  counter-increment: step;
}
.\[\&\>h3\]\:step > h3:before {
  margin-right: 0.5rem;
  display: inline-flex;
  height: 2rem;
  width: 2rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-width: 4px;
  border-color: hsl(var(--background));
  background-color: hsl(var(--muted));
  text-align: center;
  text-indent: -1px;
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
}
@media (min-width: 768px) {
  .\[\&\>h3\]\:step > h3:before {
    position: absolute;
    height: 2.25rem;
    width: 2.25rem;
    margin-left: -50px;
    margin-top: -4px;
  }
}
.\[\&\>h3\]\:step > h3:before {
  content: counter(step);
}
.\[\&\>li\]\:mt-2 > li {
  margin-top: 0.5rem;
}
.\[\&\>span\:last-child\]\:truncate > span:last-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.\[\&\>span\]\:line-clamp-1 > span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.\[\&\>span\]\:flex > span {
  display: flex;
}
.\[\&\>span\]\:w-auto > span {
  width: auto;
}
.\[\&\>span\]\:w-full > span {
  width: 100%;
}
.\[\&\>span\]\:items-center > span {
  align-items: center;
}
.\[\&\>span\]\:gap-1 > span {
  gap: 0.25rem;
}
.\[\&\>span\]\:gap-2 > span {
  gap: 0.5rem;
}
.\[\&\>span\]\:truncate > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.\[\&\>svg\+div\]\:translate-y-\[-3px\] > svg + div {
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>svg\]\:absolute > svg {
  position: absolute;
}
.\[\&\>svg\]\:left-4 > svg {
  left: 1rem;
}
.\[\&\>svg\]\:right-4 > svg {
  right: 1rem;
}
.\[\&\>svg\]\:top-4 > svg {
  top: 1rem;
}
.\[\&\>svg\]\:hidden > svg {
  display: none;
}
.\[\&\>svg\]\:size-3 > svg {
  width: 0.75rem;
  height: 0.75rem;
}
.\[\&\>svg\]\:size-4 > svg {
  width: 1rem;
  height: 1rem;
}
.\[\&\>svg\]\:h-2\.5 > svg {
  height: 0.625rem;
}
.\[\&\>svg\]\:h-3 > svg {
  height: 0.75rem;
}
.\[\&\>svg\]\:h-3\.5 > svg {
  height: 0.875rem;
}
.\[\&\>svg\]\:h-\[0\.9rem\] > svg {
  height: 0.9rem;
}
.\[\&\>svg\]\:w-2\.5 > svg {
  width: 0.625rem;
}
.\[\&\>svg\]\:w-3 > svg {
  width: 0.75rem;
}
.\[\&\>svg\]\:w-3\.5 > svg {
  width: 0.875rem;
}
.\[\&\>svg\]\:w-\[0\.9rem\] > svg {
  width: 0.9rem;
}
.\[\&\>svg\]\:shrink-0 > svg {
  flex-shrink: 0;
}
.\[\&\>svg\]\:text-destructive > svg {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive) / var(--tw-text-opacity));
}
.\[\&\>svg\]\:text-foreground > svg {
  color: hsl(var(--foreground));
}
.\[\&\>svg\]\:text-muted-foreground > svg {
  color: hsl(var(--muted-foreground));
}
.\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
  color: hsl(var(--sidebar-accent-foreground));
}
.\[\&\>svg\]\:opacity-0 > svg {
  opacity: 0;
}
.\[\&\>svg\]\:transition-opacity > svg {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}
.\[\&\>svg\~\*\]\:pl-7 > svg ~ * {
  padding-left: 1.75rem;
}
.\[\&\>tr\]\:last\:border-b-0:last-child > tr {
  border-bottom-width: 0;
}
.\[\&\[align\=center\]\]\:text-center[align="center"] {
  text-align: center;
}
.\[\&\[align\=right\]\]\:text-right[align="right"] {
  text-align: right;
}
.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction="vertical"]
  > div,
.\[\&\[data-state\=open\]\>button\>svg\:first-child\]\:rotate-90[data-state="open"]
  > button
  > svg:first-child {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&_\.line\:before\]\:sticky .line:before {
  position: sticky;
}
.\[\&_\.line\:before\]\:left-2 .line:before {
  left: 0.5rem;
}
.\[\&_\.line\:before\]\:z-10 .line:before {
  z-index: 10;
}
.\[\&_\.line\:before\]\:translate-y-\[-1px\] .line:before {
  --tw-translate-y: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&_\.line\:before\]\:pr-1 .line:before {
  padding-right: 0.25rem;
}
.\[\&_\.max-w-xs\]\:max-w-\[70\%\] .max-w-xs {
  max-width: 70%;
}
@media (min-width: 640px) {
  .\[\&_\.preview\>\[data-orientation\=vertical\]\]\:sm\:max-w-\[70\%\]
    .preview
    > [data-orientation="vertical"] {
    max-width: 70%;
  }
}
.\[\&_\.preview\>div\]\:w-full .preview > div {
  width: 100%;
}
.\[\&_\.preview\>div\]\:max-w-\[450px\] .preview > div {
  max-width: 450px;
}
.\[\&_\.preview\>div\]\:border-none .preview > div {
  border-style: none;
}
.\[\&_\.preview\>div\]\:shadow-none .preview > div {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.\[\&_\.preview\]\:min-h-\[250px\] .preview {
  min-height: 250px;
}
.\[\&_\.preview\]\:items-start .preview {
  align-items: flex-start;
}
.\[\&_\.preview\]\:border-t .preview {
  border-top-width: 1px;
}
.\[\&_\.preview\]\:p-0 .preview {
  padding: 0;
}
.\[\&_\.preview\]\:p-2 .preview {
  padding: 0.5rem;
}
.\[\&_\.preview\]\:p-4 .preview {
  padding: 1rem;
}
.\[\&_\.preview\]\:py-0 .preview {
  padding-top: 0;
  padding-bottom: 0;
}
@media (min-width: 1024px) {
  .\[\&_\.preview\]\:lg\:min-h-\[404px\] .preview {
    min-height: 404px;
  }
}
.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground
  .recharts-cartesian-axis-tick
  text {
  fill: hsl(var(--muted-foreground));
}
.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50
  .recharts-cartesian-grid
  line[stroke="#ccc"] {
  stroke: hsl(var(--border) / 0.5);
}
.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border
  .recharts-curve.recharts-tooltip-cursor {
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent
  .recharts-dot[stroke="#fff"] {
  stroke: transparent;
}
.\[\&_\.recharts-layer\]\:outline-none .recharts-layer {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\.recharts-pie-label-text\]\:fill-foreground .recharts-pie-label-text {
  fill: hsl(var(--foreground));
}
.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border
  .recharts-polar-grid
  [stroke="#ccc"] {
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted
  .recharts-radial-bar-background-sector,
.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted
  .recharts-rectangle.recharts-tooltip-cursor {
  fill: hsl(var(--muted));
}
.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border
  .recharts-reference-line
  [stroke="#ccc"] {
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent
  .recharts-sector[stroke="#fff"] {
  stroke: transparent;
}
.\[\&_\.recharts-sector\]\:outline-none .recharts-sector,
.\[\&_\.recharts-surface\]\:outline-none .recharts-surface {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\.recharts-text\]\:fill-background .recharts-text {
  fill: hsl(var(--background));
}
.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
  font-size: 0.75rem;
  line-height: 1rem;
}
.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
  font-weight: 500;
}
.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
  color: hsl(var(--muted-foreground));
}
.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0
  [cmdk-group]:not([hidden])
  ~ [cmdk-group] {
  padding-top: 0;
}
.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
  height: 1.25rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
  width: 1.25rem;
}
.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
  height: 3rem;
}
.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
  height: 1.25rem;
}
.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
  width: 1.25rem;
}
@media (min-width: 768px) {
  .\[\&_\[cmdk-root\]\]\:md\:min-w-max [cmdk-root] {
    min-width: -moz-max-content;
    min-width: max-content;
  }
}
.\[\&_\[data-chart\]\]\:mx-auto [data-chart] {
  margin-left: auto;
  margin-right: auto;
}
.\[\&_\[data-chart\]\]\:max-h-\[35vh\] [data-chart] {
  max-height: 35vh;
}
@media (min-width: 1280px) {
  .\[\&_\[data-chart\]\]\:xl\:max-h-\[243px\] [data-chart] {
    max-height: 243px;
  }
}
.\[\&_\[data-description\]\]\:hidden [data-description] {
  display: none;
}
.\[\&_\[data-rehype-pretty-code-title\]\]\:pt-1
  [data-rehype-pretty-code-title] {
  padding-top: 0.25rem;
}
.\[\&_\[role\=gridcell\]\.bg-accent\]\:bg-sidebar-primary
  [role="gridcell"].bg-accent {
  background-color: hsl(var(--sidebar-primary));
}
.\[\&_\[role\=gridcell\]\.bg-accent\]\:text-sidebar-primary-foreground
  [role="gridcell"].bg-accent {
  color: hsl(var(--sidebar-primary-foreground));
}
.\[\&_\[role\=gridcell\]\]\:w-\[33px\] [role="gridcell"] {
  width: 33px;
}
.\[\&_\[role\=slider\]\]\:h-4 [role="slider"] {
  height: 1rem;
}
.\[\&_\[role\=slider\]\]\:w-4 [role="slider"] {
  width: 1rem;
}
.\[\&_\[role\=tablist\]\]\:hidden [role="tablist"] {
  display: none;
}
.\[\&_a\]\:underline a {
  text-decoration-line: underline;
}
.\[\&_a\]\:underline-offset-4 a {
  text-underline-offset: 4px;
}
.\[\&_a\]\:hover\:text-primary:hover a,
.hover\:\[\&_a\]\:text-primary a:hover {
  color: hsl(var(--primary));
}
.\[\&_button\]\:hidden button {
  display: none;
}
.\[\&_code\]\:bg-blue-100 code {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}
.dark\:\[\&_code\]\:bg-blue-900 code:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity));
}
.\[\&_div\.grid\]\:w-full div.grid {
  width: 100%;
}
.\[\&_h3\.font-heading\]\:text-base h3.font-heading {
  font-size: 1rem;
  line-height: 1.5rem;
}
.\[\&_h3\.font-heading\]\:font-semibold h3.font-heading {
  font-weight: 600;
}
.\[\&_input\]\:max-w-xs input {
  max-width: 20rem;
}
.\[\&_p\]\:leading-relaxed p {
  line-height: 1.625;
}
.\[\&_pre\]\:my-0 pre {
  margin-top: 0;
  margin-bottom: 0;
}
.\[\&_pre\]\:mb-0 pre {
  margin-bottom: 0;
}
.\[\&_pre\]\:h-\[--height\] pre {
  height: var(--height);
}
.\[\&_pre\]\:max-h-\[350px\] pre {
  max-height: 350px;
}
.\[\&_pre\]\:max-h-\[650px\] pre {
  max-height: 650px;
}
.\[\&_pre\]\:overflow-auto pre {
  overflow: auto;
}
.\[\&_pre\]\:overflow-hidden pre {
  overflow: hidden;
}
.\[\&_pre\]\:\!bg-black pre {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity)) !important;
}
.\[\&_pre\]\:\!bg-transparent pre {
  background-color: transparent !important;
}
.\[\&_pre\]\:py-6 pre {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.\[\&_pre\]\:pb-20 pre {
  padding-bottom: 5rem;
}
.\[\&_pre\]\:pb-\[100px\] pre {
  padding-bottom: 100px;
}
.\[\&_pre\]\:pt-4 pre {
  padding-top: 1rem;
}
.\[\&_pre\]\:font-mono pre {
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.\[\&_pre\]\:text-sm pre {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.\[\&_pre\]\:leading-relaxed pre {
  line-height: 1.625;
}
.\[\&_span\]\:flex span {
  display: flex;
}
.\[\&_span\]\:h-4 span {
  height: 1rem;
}
.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}
.\[\&_svg\]\:invisible svg {
  visibility: hidden;
}
.\[\&_svg\]\:hidden svg {
  display: none;
}
.\[\&_svg\]\:size-3 svg {
  width: 0.75rem;
  height: 0.75rem;
}
.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}
.\[\&_svg\]\:h-3 svg {
  height: 0.75rem;
}
.\[\&_svg\]\:h-3\.5 svg {
  height: 0.875rem;
}
.\[\&_svg\]\:h-4 svg {
  height: 1rem;
}
.\[\&_svg\]\:w-3 svg {
  width: 0.75rem;
}
.\[\&_svg\]\:w-3\.5 svg {
  width: 0.875rem;
}
.\[\&_svg\]\:w-4 svg {
  width: 1rem;
}
.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}
.\[\&_svg\]\:text-foreground svg {
  color: hsl(var(--foreground));
}
.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0;
}
.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}
.\[\&_ul\]\:mt-0 ul {
  margin-top: 0;
}
[data-side="left"][data-collapsible="offcanvas"]
  .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
  right: -0.5rem;
}
[data-side="left"][data-state="collapsed"]
  .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
  cursor: e-resize;
}
[data-side="left"] .\[\[data-side\=left\]_\&\]\:cursor-w-resize {
  cursor: w-resize;
}
[data-side="right"][data-collapsible="offcanvas"]
  .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
  left: -0.5rem;
}
[data-side="right"][data-state="collapsed"]
  .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
  cursor: w-resize;
}
[data-side="right"] .\[\[data-side\=right\]_\&\]\:cursor-e-resize {
  cursor: e-resize;
}
html.dark .\[html\.dark_\&\]\:block,
html.light .\[html\.light_\&\]\:block {
  display: block;
}
