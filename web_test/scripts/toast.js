// Toast notification utility using shadcn UI styling

let toastContainer = null;
let toastIdCounter = 0;

export function initToast() {
    toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        console.error('Toast container not found');
    }
}

export function showToast(message, type = 'info', duration = 3000) {
    if (!toastContainer) {
        console.error('Toast not initialized');
        return;
    }

    const toastId = `toast-${++toastIdCounter}`;
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = getToastClasses(type);
    
    const icon = getToastIcon(type);
    
    toast.innerHTML = `
        <div class="flex items-start gap-3">
            <div class="flex-shrink-0 mt-0.5">
                ${icon}
            </div>
            <div class="flex-1 text-sm">
                ${message}
            </div>
            <button type="button" class="flex-shrink-0 ml-2 text-muted-foreground hover:text-foreground transition-colors" onclick="removeToast('${toastId}')">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 6 6 18M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;

    // Add animation classes
    toast.style.transform = 'translateX(100%)';
    toast.style.opacity = '0';
    
    toastContainer.appendChild(toast);

    // Animate in
    requestAnimationFrame(() => {
        toast.style.transition = 'all 0.3s ease-out';
        toast.style.transform = 'translateX(0)';
        toast.style.opacity = '1';
    });

    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeToast(toastId);
        }, duration);
    }

    return toastId;
}

export function removeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (!toast) return;

    toast.style.transition = 'all 0.3s ease-out';
    toast.style.transform = 'translateX(100%)';
    toast.style.opacity = '0';

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
}

function getToastClasses(type) {
    const baseClasses = 'relative w-full rounded-lg border p-4 shadow-lg backdrop-blur-sm';
    
    switch (type) {
        case 'success':
            return `${baseClasses} bg-background border-green-200 text-green-800`;
        case 'error':
            return `${baseClasses} bg-background border-red-200 text-red-800`;
        case 'warning':
            return `${baseClasses} bg-background border-yellow-200 text-yellow-800`;
        default:
            return `${baseClasses} bg-background border-border text-foreground`;
    }
}

function getToastIcon(type) {
    switch (type) {
        case 'success':
            return `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                </svg>
            `;
        case 'error':
            return `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-600">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" x2="9" y1="9" y2="15"></line>
                    <line x1="9" x2="15" y1="9" y2="15"></line>
                </svg>
            `;
        case 'warning':
            return `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-yellow-600">
                    <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
                    <line x1="12" x2="12" y1="9" y2="13"></line>
                    <dot cx="12" cy="17" r="1"></dot>
                </svg>
            `;
        default:
            return `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="m9 12 2 2 4-4"></path>
                </svg>
            `;
    }
}

// Make removeToast available globally for onclick handlers
window.removeToast = removeToast;
