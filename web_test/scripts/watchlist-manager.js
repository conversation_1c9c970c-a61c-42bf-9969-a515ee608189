// Watchlist Manager Dialog Module
// Handles the comprehensive watchlist management interface

import { showToast } from './toast.js';

// DOM Elements
let watchlistManagerDialogEl = null;
let watchlistManagerDialogContentEl = null;
let watchlistManagerListEl = null;
let watchlistSymbolsListEl = null;
let selectedWatchlistNameEl = null;
let managerSymbolInputEl = null;
let managerSymbolAutocompleteEl = null;
let addNewWatchlistBtnEl = null;
let cancelWatchlistManagerBtnEl = null;
let saveWatchlistManagerBtnEl = null;

// State
let managerWatchlists = {}; // Local copy for editing
let selectedManagerWatchlistId = null;
let hasUnsavedChanges = false;
let editingWatchlistId = null; // Track which watchlist is being edited
let isCreatingNewWatchlist = false; // Track if we're creating a new watchlist

// Sample stock symbols for autocomplete
const AVAILABLE_SYMBOLS = ['HPG', 'VNM', 'TCB', 'VCB', 'VIC', 'FPT', 'MWG', 'VHM', 'MSN', 'VRE', 'GAS', 'CTG', 'BID', 'PLX', 'POW', 'SSI', 'HDB', 'TPB', 'MBB', 'STB'];

export function initWatchlistManager() {
    console.log('Initializing watchlist manager...');

    // Get DOM elements
    watchlistManagerDialogEl = document.getElementById('watchlistManagerDialog');
    watchlistManagerDialogContentEl = watchlistManagerDialogEl?.querySelector('[role="dialog"]');
    watchlistManagerListEl = document.getElementById('watchlistManagerList');
    watchlistSymbolsListEl = document.getElementById('watchlistSymbolsList');
    selectedWatchlistNameEl = document.getElementById('selectedWatchlistName');
    managerSymbolInputEl = document.getElementById('managerSymbolInput');
    managerSymbolAutocompleteEl = document.getElementById('managerSymbolAutocomplete');
    addNewWatchlistBtnEl = document.getElementById('addNewWatchlistBtn');
    cancelWatchlistManagerBtnEl = document.getElementById('cancelWatchlistManagerBtn');
    saveWatchlistManagerBtnEl = document.getElementById('saveWatchlistManagerBtn');

    console.log('Watchlist manager elements found:', {
        dialog: !!watchlistManagerDialogEl,
        list: !!watchlistManagerListEl,
        symbols: !!watchlistSymbolsListEl
    });

    // Add event listeners
    if (addNewWatchlistBtnEl) {
        addNewWatchlistBtnEl.addEventListener('click', handleAddNewWatchlist);
    }

    if (cancelWatchlistManagerBtnEl) {
        cancelWatchlistManagerBtnEl.addEventListener('click', closeWatchlistManager);
    }

    if (saveWatchlistManagerBtnEl) {
        saveWatchlistManagerBtnEl.addEventListener('click', handleSaveChanges);
    }

    // Initialize symbol input autocomplete
    if (managerSymbolInputEl && managerSymbolAutocompleteEl) {
        initManagerSymbolAutocomplete();
    }

    // Handle Enter key in symbol input
    if (managerSymbolInputEl) {
        managerSymbolInputEl.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleAddSymbol();
            }
        });
    }
}

export function openWatchlistManager(watchlists) {
    console.log('Opening watchlist manager with watchlists:', watchlists);

    // Load current watchlists into local state
    managerWatchlists = JSON.parse(JSON.stringify(watchlists)); // Deep copy
    selectedManagerWatchlistId = null;
    hasUnsavedChanges = false;

    // Update UI state
    updateSaveButtonState();

    // Render the interface
    renderWatchlistManagerList();
    renderWatchlistSymbols();

    // Show dialog
    if (watchlistManagerDialogEl) {
        console.log('Showing watchlist manager dialog');
        watchlistManagerDialogEl.classList.remove('hidden');
        watchlistManagerDialogEl.classList.add('flex');
    } else {
        console.error('Watchlist manager dialog element not found');
    }
    if (watchlistManagerDialogContentEl) {
        watchlistManagerDialogContentEl.setAttribute('data-state', 'open');
    }
}

function closeWatchlistManager() {
    if (hasUnsavedChanges) {
        showUnsavedChangesWarning();
        return;
    }

    performCloseWatchlistManager();
}

function showUnsavedChangesWarning() {
    // Create inline warning UI
    const warningHtml = `
        <div class="fixed inset-0 z-[60] bg-background/80 backdrop-blur-sm flex items-center justify-center">
            <div class="relative z-50 grid w-full max-w-md gap-4 border bg-background p-6 shadow-lg rounded-lg">
                <div class="flex flex-col space-y-2 text-center">
                    <h3 class="text-lg font-semibold">Thay đổi chưa được lưu</h3>
                    <p class="text-sm text-muted-foreground">Bạn có thay đổi chưa được lưu. Bạn có muốn lưu trước khi đóng không?</p>
                </div>
                <div class="flex gap-2 justify-end">
                    <button type="button" id="discardChangesBtn" class="whitespace-nowrap rounded-md text-sm font-medium transition-colors border border-input bg-background text-accent-foreground h-10 px-4 py-2 cursor-pointer">
                        Không lưu
                    </button>
                    <button type="button" id="saveAndCloseBtn" class="whitespace-nowrap rounded-md text-sm font-medium transition-colors border border-input bg-primary text-primary-foreground h-10 px-4 py-2 cursor-pointer">
                        Lưu và đóng
                    </button>
                    <button type="button" id="cancelCloseBtn" class="whitespace-nowrap rounded-md text-sm font-medium transition-colors border border-input bg-secondary text-secondary-foreground h-10 px-4 py-2 cursor-pointer">
                        Hủy
                    </button>
                </div>
            </div>
        </div>
    `;

    const warningEl = document.createElement('div');
    warningEl.id = 'unsavedChangesWarning';
    warningEl.innerHTML = warningHtml;
    document.body.appendChild(warningEl);

    // Add event listeners
    document.getElementById('discardChangesBtn').addEventListener('click', () => {
        document.body.removeChild(warningEl);
        hasUnsavedChanges = false;
        performCloseWatchlistManager();
    });

    document.getElementById('saveAndCloseBtn').addEventListener('click', () => {
        document.body.removeChild(warningEl);
        handleSaveChanges(); // This will auto-close after saving
    });

    document.getElementById('cancelCloseBtn').addEventListener('click', () => {
        document.body.removeChild(warningEl);
    });
}

function performCloseWatchlistManager() {
    if (watchlistManagerDialogContentEl) {
        watchlistManagerDialogContentEl.setAttribute('data-state', 'closed');
    }

    setTimeout(() => {
        if (watchlistManagerDialogEl) {
            watchlistManagerDialogEl.classList.add('hidden');
            watchlistManagerDialogEl.classList.remove('flex');
        }
    }, 200);

    // Reset state
    selectedManagerWatchlistId = null;
    hasUnsavedChanges = false;
    editingWatchlistId = null;
    isCreatingNewWatchlist = false;
    if (managerSymbolInputEl) managerSymbolInputEl.value = '';
}

function renderWatchlistManagerList() {
    if (!watchlistManagerListEl) return;

    const watchlistIds = Object.keys(managerWatchlists);
    let html = '';

    // Add new watchlist input row if creating
    if (isCreatingNewWatchlist) {
        html += `
            <div class="watchlist-manager-item flex items-center justify-between p-3 mb-2 rounded-md border border-primary bg-accent/50">
                <div class="flex-1 min-w-0">
                    <input type="text" id="newWatchlistInput"
                           class="w-full bg-transparent border-none outline-none font-medium text-sm"
                           placeholder="Nhập tên danh sách..."
                           maxlength="50">
                    <div id="newWatchlistError" class="text-xs text-destructive mt-1 hidden"></div>
                </div>
                <div class="flex gap-1 ml-2">
                    <button type="button" id="saveNewWatchlistBtn" class="p-1 rounded-md text-green-600 hover:bg-green-100 transition-colors" title="Lưu">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                    </button>
                    <button type="button" id="cancelNewWatchlistBtn" class="p-1 rounded-md text-muted-foreground hover:bg-muted transition-colors" title="Hủy">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;
    }

    if (watchlistIds.length === 0 && !isCreatingNewWatchlist) {
        html += `
            <div class="flex flex-col items-center justify-center h-full text-center p-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground mb-2">
                    <path d="M3 12h18m-9-9v18"></path>
                </svg>
                <p class="text-sm text-muted-foreground">Chưa có danh sách theo dõi nào</p>
                <p class="text-xs text-muted-foreground mt-1">Nhấn "Thêm mới" để tạo danh sách đầu tiên</p>
            </div>
        `;
    } else {
        watchlistIds.forEach(watchlistId => {
            const isSelected = watchlistId === selectedManagerWatchlistId;
            const isEditing = watchlistId === editingWatchlistId;
            const symbolCount = managerWatchlists[watchlistId].length;

            if (isEditing) {
                html += `
                    <div class="watchlist-manager-item flex items-center justify-between p-3 mb-2 rounded-md border border-primary bg-accent/50"
                         data-watchlist-id="${watchlistId}">
                        <div class="flex-1 min-w-0">
                            <input type="text" id="editWatchlistInput"
                                   class="w-full bg-transparent border-none outline-none font-medium text-sm"
                                   value="${watchlistId}"
                                   maxlength="50">
                            <div id="editWatchlistError" class="text-xs text-destructive mt-1 hidden"></div>
                        </div>
                        <div class="flex gap-1 ml-2">
                            <button type="button" id="saveEditWatchlistBtn" class="p-1 rounded-md text-green-600 hover:bg-green-100 transition-colors" title="Lưu">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20,6 9,17 4,12"></polyline>
                                </svg>
                            </button>
                            <button type="button" id="cancelEditWatchlistBtn" class="p-1 rounded-md text-muted-foreground hover:bg-muted transition-colors" title="Hủy">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 6 6 18M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                `;
            } else {
                html += `
                    <div class="watchlist-manager-item flex items-center justify-between p-3 mb-2 rounded-md border cursor-pointer transition-colors ${isSelected ? 'bg-accent border-primary' : 'hover:bg-muted'}"
                         data-watchlist-id="${watchlistId}">
                        <div class="flex-1 min-w-0">
                            <div class="font-medium text-sm truncate">${watchlistId}</div>
                            <div class="text-xs text-muted-foreground">${symbolCount} mã cổ phiếu</div>
                        </div>
                        <div class="flex gap-1 ml-2">
                            <button type="button" class="edit-watchlist-btn p-1 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
                                    data-watchlist-id="${watchlistId}" title="Sửa tên">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                                    <path d="m15 5 4 4"></path>
                                </svg>
                            </button>
                            <button type="button" class="delete-watchlist-btn p-1 rounded-md text-muted-foreground hover:text-destructive hover:bg-destructive/10 transition-colors"
                                    data-watchlist-id="${watchlistId}" title="Xóa danh sách">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6m3 0V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                    <line x1="10" x2="10" y1="11" y2="17"></line>
                                    <line x1="14" x2="14" y1="11" y2="17"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                `;
            }
        });
    }

    watchlistManagerListEl.innerHTML = html;
    addWatchlistEventListeners();

    // Auto-focus input if creating or editing
    if (isCreatingNewWatchlist) {
        const input = document.getElementById('newWatchlistInput');
        if (input) {
            input.focus();
        }
    } else if (editingWatchlistId) {
        const input = document.getElementById('editWatchlistInput');
        if (input) {
            input.focus();
            input.select();
        }
    }
}

function addWatchlistEventListeners() {
    // New watchlist creation events
    const newInput = document.getElementById('newWatchlistInput');
    const saveNewBtn = document.getElementById('saveNewWatchlistBtn');
    const cancelNewBtn = document.getElementById('cancelNewWatchlistBtn');

    if (newInput) {
        newInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveNewWatchlist();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelNewWatchlist();
            }
        });

        newInput.addEventListener('input', () => {
            validateNewWatchlistName();
        });
    }

    if (saveNewBtn) {
        saveNewBtn.addEventListener('click', saveNewWatchlist);
    }

    if (cancelNewBtn) {
        cancelNewBtn.addEventListener('click', cancelNewWatchlist);
    }

    // Edit watchlist events
    const editInput = document.getElementById('editWatchlistInput');
    const saveEditBtn = document.getElementById('saveEditWatchlistBtn');
    const cancelEditBtn = document.getElementById('cancelEditWatchlistBtn');

    if (editInput) {
        editInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEditWatchlist();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEditWatchlist();
            }
        });

        editInput.addEventListener('input', () => {
            validateEditWatchlistName();
        });
    }

    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', saveEditWatchlist);
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', cancelEditWatchlist);
    }

    // Regular watchlist item events
    watchlistManagerListEl.querySelectorAll('.watchlist-manager-item').forEach(item => {
        if (!item.querySelector('input')) { // Only for non-editing items
            item.addEventListener('click', (e) => {
                if (!e.target.closest('button')) {
                    const watchlistId = item.dataset.watchlistId;
                    selectManagerWatchlist(watchlistId);
                }
            });
        }
    });

    // Edit button events
    watchlistManagerListEl.querySelectorAll('.edit-watchlist-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const watchlistId = btn.dataset.watchlistId;
            startEditingWatchlist(watchlistId);
        });
    });

    // Delete button events
    watchlistManagerListEl.querySelectorAll('.delete-watchlist-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const watchlistId = btn.dataset.watchlistId;
            showDeleteConfirmation(watchlistId);
        });
    });
}

function selectManagerWatchlist(watchlistId) {
    selectedManagerWatchlistId = watchlistId;
    renderWatchlistManagerList(); // Re-render to update selection
    renderWatchlistSymbols();

    if (selectedWatchlistNameEl) {
        selectedWatchlistNameEl.textContent = watchlistId;
    }
}

function renderWatchlistSymbols() {
    if (!watchlistSymbolsListEl) return;

    if (!selectedManagerWatchlistId || !managerWatchlists[selectedManagerWatchlistId]) {
        watchlistSymbolsListEl.innerHTML = `
            <div class="flex flex-col items-center justify-center h-full text-center p-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground mb-2">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                </svg>
                <p class="text-sm text-muted-foreground">Chọn danh sách bên trái</p>
                <p class="text-xs text-muted-foreground mt-1">để xem và chỉnh sửa mã cổ phiếu</p>
            </div>
        `;
        return;
    }

    const symbols = managerWatchlists[selectedManagerWatchlistId];

    if (symbols.length === 0) {
        watchlistSymbolsListEl.innerHTML = `
            <div class="flex flex-col items-center justify-center h-full text-center p-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground mb-2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 6v6l4 2"></path>
                </svg>
                <p class="text-sm text-muted-foreground">Danh sách trống</p>
                <p class="text-xs text-muted-foreground mt-1">Thêm mã cổ phiếu bằng ô nhập bên trên</p>
            </div>
        `;
        return;
    }

    let html = '';
    symbols.forEach(symbol => {
        html += `
            <div class="symbol-manager-item flex items-center justify-between p-3 mb-2 rounded-md border hover:bg-muted transition-colors">
                <div class="flex-1">
                    <div class="font-medium text-sm">${symbol}</div>
                </div>
                <button type="button" class="remove-symbol-btn p-1 rounded-md text-muted-foreground hover:text-destructive hover:bg-destructive/10 transition-colors"
                        data-symbol="${symbol}" title="Xóa mã">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
    });

    watchlistSymbolsListEl.innerHTML = html;

    // Add event listeners for remove buttons
    watchlistSymbolsListEl.querySelectorAll('.remove-symbol-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const symbol = btn.dataset.symbol;
            handleRemoveSymbol(symbol);
        });
    });
}

function handleAddNewWatchlist() {
    // Cancel any existing editing
    cancelAllEditing();

    // Set state for creating new watchlist
    isCreatingNewWatchlist = true;

    // Re-render to show the new input row
    renderWatchlistManagerList();
}

function cancelAllEditing() {
    isCreatingNewWatchlist = false;
    editingWatchlistId = null;
}

function validateNewWatchlistName() {
    const input = document.getElementById('newWatchlistInput');
    const errorEl = document.getElementById('newWatchlistError');
    if (!input || !errorEl) return false;

    const name = input.value.trim();

    if (!name) {
        errorEl.textContent = 'Hãy nhập tên cho danh sách theo dõi';
        errorEl.classList.remove('hidden');
        return false;
    }

    if (managerWatchlists.hasOwnProperty(name)) {
        errorEl.textContent = 'Tên này đã được sử dụng';
        errorEl.classList.remove('hidden');
        return false;
    }

    errorEl.classList.add('hidden');
    return true;
}

function saveNewWatchlist() {
    if (!validateNewWatchlistName()) return;

    const input = document.getElementById('newWatchlistInput');
    const name = input.value.trim();

    // Add new watchlist
    managerWatchlists[name] = [];
    hasUnsavedChanges = true;
    updateSaveButtonState();

    // Reset state
    isCreatingNewWatchlist = false;

    // Re-render and select the new watchlist
    renderWatchlistManagerList();
    selectManagerWatchlist(name);

    showToast('Đã tạo danh sách theo dõi mới', 'success');
}

function cancelNewWatchlist() {
    isCreatingNewWatchlist = false;
    renderWatchlistManagerList();
}

function startEditingWatchlist(watchlistId) {
    // Cancel any existing editing
    cancelAllEditing();

    // Set editing state
    editingWatchlistId = watchlistId;

    // Re-render to show edit input
    renderWatchlistManagerList();
}

function validateEditWatchlistName() {
    const input = document.getElementById('editWatchlistInput');
    const errorEl = document.getElementById('editWatchlistError');
    if (!input || !errorEl) return false;

    const name = input.value.trim();

    if (!name) {
        errorEl.textContent = 'Hãy nhập tên cho danh sách theo dõi';
        errorEl.classList.remove('hidden');
        return false;
    }

    // If name hasn't changed, it's valid
    if (name === editingWatchlistId) {
        errorEl.classList.add('hidden');
        return true;
    }

    if (managerWatchlists.hasOwnProperty(name)) {
        errorEl.textContent = 'Tên này đã được sử dụng';
        errorEl.classList.remove('hidden');
        return false;
    }

    errorEl.classList.add('hidden');
    return true;
}

function saveEditWatchlist() {
    if (!validateEditWatchlistName()) return;

    const input = document.getElementById('editWatchlistInput');
    const newName = input.value.trim();
    const oldName = editingWatchlistId;

    if (newName !== oldName) {
        // Rename the watchlist
        managerWatchlists[newName] = managerWatchlists[oldName];
        delete managerWatchlists[oldName];

        // Update selected watchlist if it was the renamed one
        if (selectedManagerWatchlistId === oldName) {
            selectedManagerWatchlistId = newName;
        }

        hasUnsavedChanges = true;
        updateSaveButtonState();

        showToast('Đã đổi tên danh sách theo dõi', 'success');
    }

    // Reset editing state
    editingWatchlistId = null;

    // Re-render
    renderWatchlistManagerList();
    renderWatchlistSymbols();
}

function cancelEditWatchlist() {
    editingWatchlistId = null;
    renderWatchlistManagerList();
}

function showDeleteConfirmation(watchlistId) {
    // Create inline confirmation UI
    const item = document.querySelector(`[data-watchlist-id="${watchlistId}"]`);
    if (!item) return;

    const symbolCount = managerWatchlists[watchlistId].length;
    const confirmationHtml = `
        <div class="flex items-center justify-between p-3 mb-2 rounded-md border border-destructive bg-destructive/10">
            <div class="flex-1 min-w-0">
                <div class="font-medium text-sm text-destructive">Xóa "${watchlistId}"?</div>
                <div class="text-xs text-muted-foreground">Sẽ xóa ${symbolCount} mã cổ phiếu</div>
            </div>
            <div class="flex gap-1 ml-2">
                <button type="button" class="confirm-delete-btn p-1 rounded-md text-destructive hover:bg-destructive hover:text-destructive-foreground transition-colors"
                        data-watchlist-id="${watchlistId}" title="Xác nhận xóa">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"></polyline>
                    </svg>
                </button>
                <button type="button" class="cancel-delete-btn p-1 rounded-md text-muted-foreground hover:bg-muted transition-colors"
                        data-watchlist-id="${watchlistId}" title="Hủy">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    item.outerHTML = confirmationHtml;

    // Add event listeners for confirmation buttons
    const confirmBtn = document.querySelector(`.confirm-delete-btn[data-watchlist-id="${watchlistId}"]`);
    const cancelBtn = document.querySelector(`.cancel-delete-btn[data-watchlist-id="${watchlistId}"]`);

    if (confirmBtn) {
        confirmBtn.addEventListener('click', () => confirmDeleteWatchlist(watchlistId));
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => renderWatchlistManagerList());
    }
}

function confirmDeleteWatchlist(watchlistId) {
    delete managerWatchlists[watchlistId];
    hasUnsavedChanges = true;
    updateSaveButtonState();

    // If this was the selected watchlist, clear selection
    if (selectedManagerWatchlistId === watchlistId) {
        selectedManagerWatchlistId = null;
        if (selectedWatchlistNameEl) {
            selectedWatchlistNameEl.textContent = 'Chọn danh sách bên trái';
        }
    }

    renderWatchlistManagerList();
    renderWatchlistSymbols();

    showToast('Đã xóa danh sách theo dõi', 'success');
}

function handleAddSymbol() {
    if (!selectedManagerWatchlistId || !managerSymbolInputEl) return;

    const symbol = managerSymbolInputEl.value.trim().toUpperCase();
    if (!symbol) return;

    // Validate symbol (basic check)
    if (!/^[A-Z]{3,4}$/.test(symbol)) {
        showToast('Mã cổ phiếu không hợp lệ. Vui lòng nhập mã gồm 3-4 chữ cái.', 'error');
        return;
    }

    // Check if symbol already exists in the watchlist
    if (managerWatchlists[selectedManagerWatchlistId].includes(symbol)) {
        showToast('Mã cổ phiếu này đã có trong danh sách.', 'warning');
        return;
    }

    // Add symbol
    managerWatchlists[selectedManagerWatchlistId].push(symbol);
    managerWatchlists[selectedManagerWatchlistId].sort(); // Keep sorted
    hasUnsavedChanges = true;
    updateSaveButtonState();

    // Clear input and re-render
    managerSymbolInputEl.value = '';
    renderWatchlistManagerList(); // Update symbol count
    renderWatchlistSymbols();

    showToast(`Đã thêm ${symbol} vào danh sách`, 'success');
}

function handleRemoveSymbol(symbol) {
    if (!selectedManagerWatchlistId) return;

    const symbols = managerWatchlists[selectedManagerWatchlistId];
    const index = symbols.indexOf(symbol);
    if (index !== -1) {
        symbols.splice(index, 1);
        hasUnsavedChanges = true;
        updateSaveButtonState();

        renderWatchlistManagerList(); // Update symbol count
        renderWatchlistSymbols();

        showToast(`Đã xóa ${symbol} khỏi danh sách`, 'success');
    }
}

function handleSaveChanges() {
    // Show saving state
    if (saveWatchlistManagerBtnEl) {
        saveWatchlistManagerBtnEl.disabled = true;
        saveWatchlistManagerBtnEl.textContent = 'Đang lưu...';
    }

    // Import the save function from watchlist.js
    if (typeof window.saveWatchlistsFromManager === 'function') {
        window.saveWatchlistsFromManager(managerWatchlists);
        hasUnsavedChanges = false;

        // Show success state briefly
        if (saveWatchlistManagerBtnEl) {
            saveWatchlistManagerBtnEl.textContent = 'Đã lưu!';
        }

        // Show success toast
        showToast('Đã lưu thay đổi thành công!', 'success');

        // Auto-close dialog after a brief delay
        setTimeout(() => {
            performCloseWatchlistManager();
        }, 1000);
    } else {
        console.error('Save function not available');
        showToast('Lỗi: Không thể lưu thay đổi', 'error');

        // Reset button state
        if (saveWatchlistManagerBtnEl) {
            saveWatchlistManagerBtnEl.disabled = false;
            saveWatchlistManagerBtnEl.textContent = 'Lưu thay đổi';
        }
    }
}

function updateSaveButtonState() {
    if (saveWatchlistManagerBtnEl) {
        if (hasUnsavedChanges) {
            saveWatchlistManagerBtnEl.disabled = false;
            saveWatchlistManagerBtnEl.textContent = 'Lưu thay đổi *';
        } else {
            saveWatchlistManagerBtnEl.disabled = true;
            saveWatchlistManagerBtnEl.textContent = 'Lưu thay đổi';
        }
    }
}

function initManagerSymbolAutocomplete() {
    if (!managerSymbolInputEl || !managerSymbolAutocompleteEl) return;

    managerSymbolInputEl.addEventListener('input', function() {
        const query = this.value.trim().toUpperCase();

        if (query.length === 0) {
            managerSymbolAutocompleteEl.classList.add('hidden');
            return;
        }

        // Filter symbols based on input
        const filteredSymbols = AVAILABLE_SYMBOLS.filter(symbol =>
            symbol.includes(query)
        );

        // Clear previous results
        managerSymbolAutocompleteEl.innerHTML = '';

        if (filteredSymbols.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'py-6 text-center text-sm text-muted-foreground';
            noResults.textContent = 'Không tìm thấy kết quả';
            managerSymbolAutocompleteEl.appendChild(noResults);
        } else {
            filteredSymbols.forEach(symbol => {
                const item = document.createElement('div');
                item.className = 'relative flex cursor-default select-none items-center rounded-sm px-4 py-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground';
                item.textContent = symbol;

                item.addEventListener('click', () => {
                    managerSymbolInputEl.value = symbol;
                    managerSymbolAutocompleteEl.classList.add('hidden');
                    handleAddSymbol();
                });

                managerSymbolAutocompleteEl.appendChild(item);
            });
        }

        managerSymbolAutocompleteEl.classList.remove('hidden');
    });

    // Hide autocomplete when clicking outside
    document.addEventListener('click', function(event) {
        if (!managerSymbolInputEl.contains(event.target) &&
            !managerSymbolAutocompleteEl.contains(event.target)) {
            managerSymbolAutocompleteEl.classList.add('hidden');
        }
    });
}
