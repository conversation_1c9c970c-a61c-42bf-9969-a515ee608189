// Tab management functions
export function selectTab(tabList, tabItem) {
    // Remove active state from all tabs
    tabList.querySelectorAll('[role="tab"]').forEach(tab => {
        tab.removeAttribute('data-state');
    });

    // Set active state on the clicked tab
    tabItem.setAttribute('data-state', 'active');

    // Trigger its afterSelected callback
    if (tabItem._afterSelectedCallback) {
        tabItem._afterSelectedCallback(tabItem.dataset.value);
    }
}

export function closeTab(tabList, tabItem) {
    // Find tab nested directly in role tablist
    const tabWrapper = tabItem.closest('[role="tablist"] > *');

    // Check if this is the active tab
    const isActiveTab = tabItem.hasAttribute('data-state');

    // Remove the tab from the DOM
    tabWrapper.remove();

    // Select the last tab
    const allTabs = tabList.querySelectorAll('[role="tab"]');
    if (allTabs && allTabs.length > 0) {
        selectTab(tabList, allTabs[allTabs.length - 1]);
    }
}

// Function to add a new tab
export function addTab(tabList, tabValue, closable = true, options = {}) {
    const { afterSelected: newAfterSelected, afterClosed: newAfterClosed } = options;

    // Check if tab already exists
    let tabNode = Array.from(tabList.querySelectorAll('[role="tab"]'))
        .find(tab => tab.dataset.value === tabValue);

    if (tabNode) {
        // If tab exists, just select it
        // Update callbacks if new ones are provided.
        // Pass undefined to keep existing, null to clear.
        if (newAfterSelected !== undefined) {
            tabNode._afterSelectedCallback = newAfterSelected;
        }
        if (newAfterClosed !== undefined) {
            tabNode._afterClosedCallback = newAfterClosed;
        }

        // selectTab(tabList, tabNode);

        return tabNode;
    }

    // Create new tab element
    tabNode = document.createElement('div');
    tabNode.role = "tab";
    // Set tabIndex based on the current number of tabs.
    tabNode.tabIndex = tabList.querySelectorAll('[role="tab"]').length;
    tabNode.setAttribute('data-value', tabValue);
    tabNode.className = 'flex items-center h-10 px-4 pr-2 pt-2 gap-2 cursor-pointer border-r';
    tabNode.onclick = () => selectTab(tabList, tabNode);

    // Store initial callbacks provided in options
    if (newAfterSelected !== undefined) {
        tabNode._afterSelectedCallback = newAfterSelected;
    }
    if (newAfterClosed !== undefined) {
        tabNode._afterClosedCallback = newAfterClosed;
    }

    // Create tab title
    const title = document.createElement('span');
    title.innerHTML = tabValue; // Use tabValue for the title
    tabNode.appendChild(title);

    // Create close button
    if (closable) {
        const closeBtn = document.createElement('a');
        closeBtn.className = "size-6 rounded-full flex items-center justify-center cursor-pointer";
        closeBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg>
        `;
        closeBtn.onclick = (e) => {
            e.stopPropagation();
            const value = tabNode.dataset.value;
            const callback = tabNode._afterClosedCallback;
            closeTab(tabList, tabNode); // This might select another tab
            if (callback) {
                callback(value, tabNode); // tabNode is now detached from DOM
            }
        };
        tabNode.appendChild(closeBtn);
    }

    // Append tab to tablist
    tabList.appendChild(tabNode);

    // Select the new tab
    // selectTab(tabList, tabNode);

    return tabNode;
}
