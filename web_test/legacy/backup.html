<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StockPal</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style type="text/css">
      body {
        margin: 0;
        padding: 0;
        height: 100vh;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .main-container {
        display: flex;
        flex: 1;
        overflow: hidden;
      }

      .sidebar {
        flex: 0 0 80px;
        background-color: #1e293b;
        color: #e2e8f0;
        overflow-y: auto;
      }

      .content {
        flex: 1;
        overflow-y: auto;
        background-color: #f8fafc;
      }

      .chart-container {
        height: 440px;
        max-width: 800px;
        position: relative;
      }

      .icon-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 0;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .icon-container:hover {
        background-color: #2d3748;
      }

      .icon {
        width: 24px;
        height: 24px;
        margin-bottom: 4px;
        fill: #e2e8f0;
      }

      .chart-title-wrap {
        position: absolute;
        left: 0px;
        top: 0px;
        width: 100%;
        height: 0px;
        z-index: 2000;
        color: #76808f;
      }

      .chart-title-wrap-container {
        box-sizing: border-box;
        height: 0;
        position: absolute;
        top: 0px;
        left: 0px;
        max-width: 550.703px;
        padding: 3px 4px;
      }

      .chart-title-wrap .chart-title-row {
        align-items: flex-start;
        border: 1px solid transparent;
        display: flex;
        font-size: 12px;
        line-height: 14px;
        margin-left: 4px;
        padding: 4px;
        width: -moz-fit-content;
        width: fit-content;
        width: -moz-max-content;
        background: rgba(255, 255, 255, 0.5);
      }

      .chart-title-row .icon-box {
        cursor: pointer;
        display: inline-flex;
        height: 16px;
        line-height: 14px;
        margin-right: 2px;
        position: relative;
        width: 16px;
      }

      .chart-title-row.fold svg {
        transform: rotate(-90deg);
      }

      .icon-box svg {
        transition: transform 0.5s ease-out;
      }

      .chart-title-row .chart-title-indicator-container {
        display: inline-flex;
        flex-wrap: wrap;
        line-height: 16px;
      }

      .chart-title-row .default-label-box {
        font-size: 12px;
        font-weight: 400;
      }

      .material-symbols-outlined {
        font-variation-settings: "FILL" 0, "wght" 200, "GRAD" 0, "opsz" 24;
      }
    </style>
  </head>

  <body>
    <nav class="topbar bg-gray-800 text-white">
      <div class="relative flex px-6 h-16 items-center justify-between">
        <div
          class="flex items-center justify-center sm:items-stretch sm:justify-start"
        >
          <div class="flex shrink-0 items-center">
            <span style="font-size: 30px" class="material-symbols-outlined"
              >strategy</span
            >
          </div>
          <div class="ml-6">
            <div class="flex space-x-4">
              <!-- Current: "bg-gray-900 text-white", Default: "text-gray-300 hover:bg-gray-700 hover:text-white" -->
              <a
                href="#"
                class="rounded-md bg-gray-900 px-3 py-2 text-sm font-medium text-white"
                aria-current="page"
                >HPG</a
              >
              <a
                href="#"
                class="rounded-md px-3 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white"
                >TCB</a
              >
            </div>
          </div>
        </div>
      </div>
    </nav>

    <main class="main-container">
      <div class="sidebar">
        <div class="p-4">
          <div class="flex flex-col items-center">
            <div class="icon-container mb-4 w-full">
              <svg
                class="icon"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3 13H11V3H3V13ZM3 21H11V15H3V21ZM13 21H21V11H13V21ZM13 3V9H21V3H13Z"
                  fill="currentColor"
                />
              </svg>
              <p class="text-center text-xs">Dashboard</p>
            </div>
            <div class="icon-container mb-4 w-full">
              <svg
                class="icon"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3.5 18.49L9.5 12.5L13.5 16.5L22 6.92001L20.59 5.51001L13.5 13.5L9.5 9.50001L2 16.99L3.5 18.49Z"
                  fill="currentColor"
                />
              </svg>
              <p class="text-center text-xs">Charts</p>
            </div>
            <div class="icon-container mb-4 w-full">
              <svg
                class="icon"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM9 17H7V10H9V17ZM13 17H11V7H13V17ZM17 17H15V13H17V17Z"
                  fill="currentColor"
                />
              </svg>
              <p class="text-center text-xs">Analysis</p>
            </div>
            <div class="icon-container mb-4 w-full">
              <svg
                class="icon"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM11 7H13V13H11V7ZM11 15H13V17H11V15Z"
                  fill="currentColor"
                />
              </svg>
              <p class="text-center text-xs">Info</p>
            </div>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="p-6">
          <div class="flex items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800 mr-5">HPG</h2>
            <span class="text-3xl font-bold mr-2">42.65</span>
            <span class="text-green-500 flex items-center">
              <svg
                class="w-4 h-4 mr-1"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M7 14L12 9L17 14H7Z" fill="currentColor" />
              </svg>
              2.35 (5.83%)
            </span>
          </div>

          <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">Price Chart</h3>
            </div>
            <div class="price-chart-container">
              <!-- Time Selector -->
              <div class="flex items-center p-2 bg-gray-50 border-b">
                <div class="text-gray-500 text-sm mr-4">Time</div>
                <div class="flex space-x-1">
                  <button
                    class="px-2 py-1 text-xs bg-gray-200 font-medium rounded"
                  >
                    1D
                  </button>
                  <button
                    class="px-2 py-1 text-xs text-gray-500 hover:bg-gray-200 rounded"
                  >
                    1M
                  </button>
                  <button
                    class="px-2 py-1 text-xs text-gray-500 hover:bg-gray-200 rounded flex items-center"
                  >
                    <span>...</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-3 w-3 ml-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>
                </div>

                <div class="ml-auto flex items-center space-x-2">
                  <button class="p-1 hover:bg-gray-200 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 6h16M4 12h16M4 18h16"
                      />
                    </svg>
                  </button>
                  <button class="p-1 hover:bg-gray-200 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                  </button>
                  <button class="p-1 hover:bg-gray-200 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                      />
                    </svg>
                  </button>
                  <button class="p-1 hover:bg-gray-200 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </button>
                </div>

                <div class="ml-8 flex items-center space-x-4">
                  <button class="text-sm text-gray-500 hover:text-gray-800">
                    Original
                  </button>
                  <button class="text-sm text-gray-700 font-medium">
                    Trading View
                  </button>
                  <button class="text-sm text-gray-500 hover:text-gray-800">
                    Depth
                  </button>
                </div>
              </div>
              <!-- Chart Info -->
              <div class="p-2 bg-gray-50 flex items-center border-b">
                <div class="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 text-gray-500 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                  <span class="text-xs text-gray-700">2025/05/01</span>
                  <span class="ml-2 text-xs text-gray-700"
                    >Open: <span class="text-green-500">599.88</span></span
                  >
                  <span class="ml-2 text-xs text-gray-700"
                    >High: <span class="text-green-500">602.44</span></span
                  >
                  <span class="ml-2 text-xs text-gray-700"
                    >Low: <span class="text-green-500">599.61</span></span
                  >
                  <span class="ml-2 text-xs text-gray-700"
                    >Close: <span class="text-green-500">602.05</span></span
                  >
                  <span class="ml-2 text-xs text-gray-700"
                    >CHANGE: <span class="text-green-500">0.36%</span></span
                  >
                  <span class="ml-2 text-xs text-gray-700"
                    >AMPLITUDE: <span class="text-green-500">0.47%</span></span
                  >
                </div>
              </div>
              <div
                class="chart-title-row"
                style="background: rgba(255, 255, 255, 0.5)"
                isfold="false"
              >
                <div class="icon-box">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M7.92656 8.11751L10.5192 5.52486L11.6977 6.70337L7.92648 10.4746L6.74797 9.2961L6.74805 9.29602L4.15527 6.70325L5.33378 5.52474L7.92656 8.11751Z"
                      fill="#929AA5"
                    ></path>
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M7.92656 8.11751L10.5192 5.52486L11.6977 6.70337L7.92648 10.4746L6.74797 9.2961L6.74805 9.29602L4.15527 6.70325L5.33378 5.52474L7.92656 8.11751Z"
                      fill="#929AA5"
                    ></path>
                  </svg>
                </div>
                <div class="chart-title-indicator-container">
                  <span
                    class="default-label-box"
                    key="t"
                    style="padding-right: 8px"
                    >2025/05/01</span
                  ><span
                    class="default-label-box"
                    key="O"
                    style="padding-right: 8px"
                    >Open:</span
                  ><span
                    class="default-label-box"
                    key="o"
                    style="padding-right: 8px; color: rgb(46, 189, 133)"
                    >599.88</span
                  ><span
                    class="default-label-box"
                    key="H"
                    style="padding-right: 8px"
                    >High:</span
                  ><span
                    class="default-label-box"
                    key="h"
                    style="padding-right: 8px; color: rgb(46, 189, 133)"
                    >602.44</span
                  ><span
                    class="default-label-box"
                    key="L"
                    style="padding-right: 8px"
                    >Low:</span
                  ><span
                    class="default-label-box"
                    key="l"
                    style="padding-right: 8px; color: rgb(46, 189, 133)"
                    >599.61</span
                  ><span
                    class="default-label-box"
                    key="C"
                    style="padding-right: 8px"
                    >Close:</span
                  ><span
                    class="default-label-box"
                    key="c"
                    style="padding-right: 8px; color: rgb(46, 189, 133)"
                    >601.95</span
                  ><span
                    class="default-label-box title_change_label"
                    key="CHANGE"
                    style="padding-right: 8px"
                    >CHANGE:</span
                  ><span
                    class="default-label-box title_change_value"
                    key="change"
                    style="padding-right: 8px; color: rgb(46, 189, 133)"
                    >0.35%</span
                  ><span
                    class="default-label-box title_amplitude_label"
                    key="AMP"
                    style="padding-right: 8px"
                    >AMPLITUDE:</span
                  ><span
                    class="default-label-box title_amplitude_value"
                    key="amp"
                    style="padding-right: 8px; color: rgb(46, 189, 133)"
                    >0.47%</span
                  >
                </div>
              </div>
            </div>
            <div class="chart-title-row" isfold="false">
              <div class="icon-box">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M7.92656 8.11751L10.5192 5.52486L11.6977 6.70337L7.92648 10.4746L6.74797 9.2961L6.74805 9.29602L4.15527 6.70325L5.33378 5.52474L7.92656 8.11751Z"
                    fill="#929AA5"
                  ></path>
                </svg>
              </div>
              <div class="chart-title-indicator-container" key="@MA">
                <span
                  class="default-label-box"
                  key="MA[0]"
                  style="padding-right: 8px; display: block"
                  >MA(7):</span
                ><span
                  class="default-label-box"
                  key="MA[0]Series"
                  style="
                    padding-right: 8px;
                    color: rgb(201, 148, 0);
                    display: block;
                  "
                  >-7.02%</span
                ><span
                  class="default-label-box"
                  key="MA[1]"
                  style="padding-right: 8px; display: block"
                  >MA(25):</span
                ><span
                  class="default-label-box"
                  key="MA[1]Series"
                  style="
                    padding-right: 8px;
                    color: rgb(235, 64, 181);
                    display: block;
                  "
                  >-8.72%</span
                ><span
                  class="default-label-box"
                  key="MA[2]"
                  style="padding-right: 8px; display: block"
                  >MA(99):</span
                ><span
                  class="default-label-box"
                  key="MA[2]Series"
                  style="
                    padding-right: 8px;
                    color: rgb(113, 38, 156);
                    display: block;
                  "
                  >-5.24%</span
                >
                <div class="indicator-title-toolbox">
                  <div class="icon-box" id="@MA-visible" tooltip="Hide">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M12 14.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5z"
                        fill="currentColor"
                      ></path>
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M6.555 6.31L1 12l5.555 5.69a7.572 7.572 0 0010.89 0L23 12l-5.555-5.69a7.572 7.572 0 00-10.89 0zM17 12a5 5 0 11-10 0 5 5 0 0110 0z"
                        fill="currentColor"
                      ></path>
                    </svg>
                  </div>
                  <div class="icon-box" tooltip="Settings">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M13.8 3h-3.6v2.027c-.66.17-1.285.431-1.858.77L6.91 4.363 4.363 6.91l1.434 1.433a7.157 7.157 0 00-.77 1.858H3v3.6h2.027c.17.66.431 1.285.77 1.858L4.363 17.09l2.546 2.546 1.433-1.434c.573.339 1.197.6 1.858.77V21h3.6v-2.027a7.157 7.157 0 001.858-.77l1.433 1.434 2.546-2.546-1.434-1.434a7.16 7.16 0 00.77-1.857H21v-3.6h-2.027a7.158 7.158 0 00-.77-1.858l1.434-1.433-2.546-2.546-1.434 1.434a7.156 7.156 0 00-1.857-.77V3zm-4.5 9a2.7 2.7 0 115.4 0 2.7 2.7 0 01-5.4 0z"
                        fill="currentColor"
                      ></path>
                    </svg>
                    <div class="tooltip-wrap up" style="display: none">
                      <div class="tooltip-text">Settings</div>
                      <div class="tooltip-arrow"></div>
                    </div>
                  </div>
                  <div class="icon-box" tooltip="Close">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M6.697 4.575L4.575 6.697 9.88 12l-5.304 5.303 2.122 2.122L12 14.12l5.303 5.304 2.122-2.122L14.12 12l5.304-5.303-2.122-2.122L12 9.88 6.697 4.575z"
                        fill="currentColor"
                      ></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div id="chart-price-history" class="chart-container"></div>
          </div>
        </div>
      </div>
    </main>
  </body>

  <script type="text/javascript">
    // Generated chart data for HPG
    const symbol = "HPG";

    const ChartData = {
      priceHistory: {
        closePrices: [
          {
            time: "2024-10-17",
            open: 27150,
            high: 27250,
            low: 26750,
            close: 27250,
            volume: 20575549,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-18",
            open: 27250,
            high: 27300,
            low: 26950,
            close: 26950,
            volume: 16254444,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-21",
            open: 26950,
            high: 27050,
            low: 26700,
            close: 26750,
            volume: 13909844,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-22",
            open: 26650,
            high: 26850,
            low: 26500,
            close: 26650,
            volume: 18330635,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-23",
            open: 26650,
            high: 26700,
            low: 26350,
            close: 26450,
            volume: 13617216,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-24",
            open: 26500,
            high: 26700,
            low: 26350,
            close: 26450,
            volume: 15908431,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-25",
            open: 26600,
            high: 26650,
            low: 26450,
            close: 26450,
            volume: 9453996,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-28",
            open: 26500,
            high: 26950,
            low: 26500,
            close: 26750,
            volume: 11941974,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-29",
            open: 26800,
            high: 27150,
            low: 26700,
            close: 27050,
            volume: 15745226,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-30",
            open: 27050,
            high: 27100,
            low: 26800,
            close: 27000,
            volume: 11074326,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-10-31",
            open: 27050,
            high: 27050,
            low: 26800,
            close: 26900,
            volume: 13332393,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-01",
            open: 26800,
            high: 26850,
            low: 26450,
            close: 26550,
            volume: 13212705,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-04",
            open: 26550,
            high: 26550,
            low: 26150,
            close: 26300,
            volume: 18321430,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-05",
            open: 26300,
            high: 26550,
            low: 26200,
            close: 26500,
            volume: 8102496,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-06",
            open: 26650,
            high: 27150,
            low: 26550,
            close: 26950,
            volume: 21004956,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-07",
            open: 27100,
            high: 27300,
            low: 26950,
            close: 26950,
            volume: 15996565,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-08",
            open: 27200,
            high: 27500,
            low: 27000,
            close: 27000,
            volume: 24778767,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-11",
            open: 27050,
            high: 27700,
            low: 26950,
            close: 27700,
            volume: 40900657,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-12",
            open: 27750,
            high: 27900,
            low: 27300,
            close: 27500,
            volume: 23977950,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-13",
            open: 27200,
            high: 27400,
            low: 26850,
            close: 27050,
            volume: 22299796,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-14",
            open: 27050,
            high: 27100,
            low: 26300,
            close: 26300,
            volume: 26584281,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-15",
            open: 26300,
            high: 26350,
            low: 25900,
            close: 25900,
            volume: 27622921,
          },
          {
            time: "2024-11-18",
            open: 26000,
            high: 26050,
            low: 25500,
            close: 25800,
            volume: 20617517,
          },
          {
            time: "2024-11-19",
            open: 25850,
            high: 26000,
            low: 25450,
            close: 25450,
            volume: 15855294,
          },
          {
            time: "2024-11-20",
            open: 25400,
            high: 26000,
            low: 25250,
            close: 25600,
            volume: 22767490,
          },
          {
            time: "2024-11-21",
            open: 25650,
            high: 25950,
            low: 25550,
            close: 25850,
            volume: 12784099,
          },
          {
            time: "2024-11-22",
            open: 25900,
            high: 26250,
            low: 25850,
            close: 26100,
            volume: 17276669,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-25",
            open: 26100,
            high: 26300,
            low: 26050,
            close: 26300,
            volume: 12839515,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-26",
            open: 26300,
            high: 26500,
            low: 26200,
            close: 26350,
            volume: 13190635,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-27",
            open: 26350,
            high: 26450,
            low: 26100,
            close: 26300,
            volume: 7974789,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-28",
            open: 26450,
            high: 26750,
            low: 26300,
            close: 26400,
            volume: 14139732,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-11-29",
            open: 26450,
            high: 26750,
            low: 26300,
            close: 26750,
            volume: 13886852,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-02",
            open: 26950,
            high: 27150,
            low: 26750,
            close: 26900,
            volume: 18590525,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-03",
            open: 26850,
            high: 27000,
            low: 26600,
            close: 27000,
            volume: 15823157,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-04",
            open: 26850,
            high: 26850,
            low: 26650,
            close: 26700,
            volume: 12517041,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-05",
            open: 26700,
            high: 27850,
            low: 26400,
            close: 27850,
            volume: 44772623,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-06",
            open: 27850,
            high: 27900,
            low: 27500,
            close: 27600,
            volume: 23768664,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-09",
            open: 27650,
            high: 27900,
            low: 27500,
            close: 27650,
            volume: 15967778,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-10",
            open: 27700,
            high: 28200,
            low: 27600,
            close: 27900,
            volume: 32715496,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-11",
            open: 27900,
            high: 28150,
            low: 27600,
            close: 27750,
            volume: 17045103,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-12",
            open: 27750,
            high: 27900,
            low: 27350,
            close: 27500,
            volume: 20976380,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-13",
            open: 27300,
            high: 27400,
            low: 27100,
            close: 27200,
            volume: 17278081,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-16",
            open: 27200,
            high: 27350,
            low: 26850,
            close: 27000,
            volume: 16666364,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-17",
            open: 27000,
            high: 27100,
            low: 26850,
            close: 27000,
            volume: 10816933,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-18",
            open: 27000,
            high: 27350,
            low: 26950,
            close: 27350,
            volume: 12801871,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-19",
            open: 27000,
            high: 27150,
            low: 26800,
            close: 26850,
            volume: 30343905,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-20",
            open: 26850,
            high: 26950,
            low: 26700,
            close: 26700,
            volume: 13753770,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-23",
            open: 26750,
            high: 27050,
            low: 26750,
            close: 27000,
            volume: 12907390,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-24",
            open: 26900,
            high: 26900,
            low: 26600,
            close: 26700,
            volume: 13471873,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-25",
            open: 26750,
            high: 27150,
            low: 26750,
            close: 27150,
            volume: 17153771,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-26",
            open: 27150,
            high: 27200,
            low: 26850,
            close: 27000,
            volume: 9982189,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-27",
            open: 27000,
            high: 27000,
            low: 26800,
            close: 26850,
            volume: 11397592,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-30",
            open: 26800,
            high: 26850,
            low: 26650,
            close: 26750,
            volume: 12176491,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2024-12-31",
            open: 26750,
            high: 26750,
            low: 26600,
            close: 26650,
            volume: 10258083,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-02",
            open: 26700,
            high: 27050,
            low: 26700,
            close: 27000,
            volume: 14970526,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-03",
            open: 26900,
            high: 27000,
            low: 26600,
            close: 26600,
            volume: 13208512,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-06",
            open: 26600,
            high: 26700,
            low: 26050,
            close: 26050,
            volume: 15267581,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-07",
            open: 26150,
            high: 26400,
            low: 26050,
            close: 26050,
            volume: 14414801,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-08",
            open: 26050,
            high: 26250,
            low: 25950,
            close: 26250,
            volume: 12963197,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-09",
            open: 26300,
            high: 26300,
            low: 25900,
            close: 26000,
            volume: 9252679,
          },
          {
            time: "2025-01-10",
            open: 26000,
            high: 26100,
            low: 25550,
            close: 25550,
            volume: 14290074,
          },
          {
            time: "2025-01-13",
            open: 25550,
            high: 26050,
            low: 25450,
            close: 25900,
            volume: 13586454,
          },
          {
            time: "2025-01-14",
            open: 25950,
            high: 26000,
            low: 25800,
            close: 25850,
            volume: 7670367,
          },
          {
            time: "2025-01-15",
            open: 26000,
            high: 26250,
            low: 25900,
            close: 26200,
            volume: 11228889,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-16",
            open: 26300,
            high: 26450,
            low: 26050,
            close: 26300,
            volume: 11215124,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-17",
            open: 26300,
            high: 26500,
            low: 26100,
            close: 26500,
            volume: 13956621,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-20",
            open: 26500,
            high: 26750,
            low: 26450,
            close: 26450,
            volume: 9665010,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-21",
            open: 26600,
            high: 26600,
            low: 26200,
            close: 26300,
            volume: 10429273,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-22",
            open: 26400,
            high: 26400,
            low: 26150,
            close: 26150,
            volume: 10285189,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-23",
            open: 26200,
            high: 26750,
            low: 26150,
            close: 26650,
            volume: 15800444,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-01-24",
            open: 26650,
            high: 26650,
            low: 26450,
            close: 26550,
            volume: 10409008,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-03",
            open: 26500,
            high: 26600,
            low: 26250,
            close: 26400,
            volume: 16987354,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-04",
            open: 26500,
            high: 27000,
            low: 26450,
            close: 26850,
            volume: 17370750,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-05",
            open: 26850,
            high: 27000,
            low: 26750,
            close: 26800,
            volume: 14477772,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-06",
            open: 26800,
            high: 26950,
            low: 26750,
            close: 26800,
            volume: 13545529,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-07",
            open: 26800,
            high: 26800,
            low: 26600,
            close: 26650,
            volume: 12556995,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-10",
            open: 26500,
            high: 26500,
            low: 25400,
            close: 25400,
            volume: 61143342,
          },
          {
            time: "2025-02-11",
            open: 25450,
            high: 26100,
            low: 25450,
            close: 26100,
            volume: 28914141,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-12",
            open: 26200,
            high: 26200,
            low: 25850,
            close: 25850,
            volume: 13674965,
          },
          {
            time: "2025-02-13",
            open: 25950,
            high: 26000,
            low: 25850,
            close: 25900,
            volume: 9507535,
          },
          {
            time: "2025-02-14",
            open: 26100,
            high: 26300,
            low: 26000,
            close: 26100,
            volume: 18592795,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-17",
            open: 26150,
            high: 26250,
            low: 26050,
            close: 26150,
            volume: 14380031,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-18",
            open: 26350,
            high: 26600,
            low: 26300,
            close: 26550,
            volume: 24951942,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-19",
            open: 26600,
            high: 26600,
            low: 26400,
            close: 26500,
            volume: 13664314,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-20",
            open: 26550,
            high: 26600,
            low: 26500,
            close: 26550,
            volume: 11920169,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-21",
            open: 26550,
            high: 26600,
            low: 26400,
            close: 26450,
            volume: 11076712,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-24",
            open: 28300,
            high: 28300,
            low: 27550,
            close: 27700,
            volume: 73928603,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-25",
            open: 27600,
            high: 27700,
            low: 27200,
            close: 27500,
            volume: 31597535,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-26",
            open: 27600,
            high: 27950,
            low: 27550,
            close: 27750,
            volume: 31840327,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-27",
            open: 27800,
            high: 28450,
            low: 27600,
            close: 28450,
            volume: 51151649,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-02-28",
            open: 28400,
            high: 28400,
            low: 27950,
            close: 28000,
            volume: 24230428,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-03",
            open: 28050,
            high: 28250,
            low: 28000,
            close: 28050,
            volume: 20678579,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-04",
            open: 28050,
            high: 28050,
            low: 27650,
            close: 27850,
            volume: 27441800,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-05",
            open: 27850,
            high: 27900,
            low: 27550,
            close: 27550,
            volume: 30401582,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-06",
            open: 27600,
            high: 27950,
            low: 27600,
            close: 27950,
            volume: 36989136,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-07",
            open: 28000,
            high: 28100,
            low: 27750,
            close: 28050,
            volume: 33148420,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-10",
            open: 28100,
            high: 28200,
            low: 27950,
            close: 27950,
            volume: 23601359,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-11",
            open: 27600,
            high: 28150,
            low: 27600,
            close: 28150,
            volume: 26105585,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-12",
            open: 28200,
            high: 28200,
            low: 27800,
            close: 27800,
            volume: 20664501,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-13",
            open: 27850,
            high: 28050,
            low: 27700,
            close: 27700,
            volume: 24826490,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-14",
            open: 27700,
            high: 27850,
            low: 27550,
            close: 27550,
            volume: 18298151,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-17",
            open: 27900,
            high: 28150,
            low: 27750,
            close: 27850,
            volume: 19738337,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-18",
            open: 27900,
            high: 28050,
            low: 27600,
            close: 27600,
            volume: 18761045,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-19",
            open: 27600,
            high: 27600,
            low: 27100,
            close: 27250,
            volume: 37953539,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-20",
            open: 27300,
            high: 27450,
            low: 27050,
            close: 27150,
            volume: 25038597,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-21",
            open: 27100,
            high: 27250,
            low: 27050,
            close: 27150,
            volume: 18921643,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-24",
            open: 27150,
            high: 27250,
            low: 26900,
            close: 27150,
            volume: 16247689,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-25",
            open: 27250,
            high: 27400,
            low: 27100,
            close: 27200,
            volume: 20115102,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-26",
            open: 27200,
            high: 27950,
            low: 27150,
            close: 27550,
            volume: 43997569,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-27",
            open: 27700,
            high: 27700,
            low: 27450,
            close: 27500,
            volume: 12023359,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-28",
            open: 27450,
            high: 27500,
            low: 27100,
            close: 27150,
            volume: 17090800,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-03-31",
            open: 27050,
            high: 27100,
            low: 26750,
            close: 26750,
            volume: 26565641,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-04-01",
            open: 26800,
            high: 27050,
            low: 26750,
            close: 27000,
            volume: 14037535,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-04-02",
            open: 27150,
            high: 27400,
            low: 27100,
            close: 27250,
            volume: 16797845,
            color: "orange",
            wickColor: "orange",
          },
          {
            time: "2025-04-03",
            open: 25800,
            high: 26450,
            low: 25350,
            close: 25350,
            volume: 80792245,
          },
          {
            time: "2025-04-04",
            open: 23600,
            high: 24850,
            low: 23600,
            close: 24600,
            volume: 73222029,
          },
          {
            time: "2025-04-08",
            open: 23500,
            high: 24000,
            low: 22900,
            close: 22900,
            volume: 35517471,
          },
          {
            time: "2025-04-09",
            open: 21300,
            high: 22300,
            low: 21300,
            close: 21300,
            volume: 77212228,
          },
          {
            time: "2025-04-10",
            open: 22750,
            high: 22750,
            low: 22750,
            close: 22750,
            volume: 3066909,
          },
          {
            time: "2025-04-11",
            open: 24200,
            high: 24300,
            low: 23500,
            close: 24300,
            volume: 76305239,
          },
          {
            time: "2025-04-14",
            open: 24800,
            high: 25750,
            low: 24750,
            close: 25350,
            volume: 51254525,
          },
          {
            time: "2025-04-15",
            open: 25150,
            high: 26150,
            low: 25050,
            close: 25850,
            volume: 50611194,
          },
          {
            time: "2025-04-16",
            open: 25900,
            high: 26000,
            low: 25050,
            close: 25500,
            volume: 25738379,
          },
          {
            time: "2025-04-17",
            open: 25200,
            high: 25600,
            low: 24800,
            close: 25500,
            volume: 24465980,
          },
          {
            time: "2025-04-18",
            open: 25650,
            high: 25750,
            low: 25450,
            close: 25450,
            volume: 20785143,
          },
          {
            time: "2025-04-21",
            open: 25450,
            high: 25450,
            low: 24950,
            close: 24950,
            volume: 17327776,
          },
          {
            time: "2025-04-22",
            open: 24800,
            high: 25100,
            low: 23400,
            close: 25050,
            volume: 47493704,
          },
          {
            time: "2025-04-23",
            open: 25500,
            high: 25700,
            low: 25350,
            close: 25550,
            volume: 22246788,
          },
          {
            time: "2025-04-24",
            open: 25700,
            high: 25700,
            low: 25200,
            close: 25600,
            volume: 17227712,
          },
          {
            time: "2025-04-25",
            open: 25450,
            high: 25700,
            low: 25250,
            close: 25700,
            volume: 24864961,
          },
          {
            time: "2025-04-28",
            open: 25700,
            high: 25700,
            low: 25350,
            close: 25650,
            volume: 9768183,
          },
          {
            time: "2025-04-29",
            open: 25650,
            high: 25700,
            low: 25400,
            close: 25500,
            volume: 27916600,
          },
        ],
        volumes: [
          {
            time: "2024-10-17",
            value: 20575549,
          },
          {
            time: "2024-10-18",
            value: 16254444,
          },
          {
            time: "2024-10-21",
            value: 13909844,
          },
          {
            time: "2024-10-22",
            value: 18330635,
          },
          {
            time: "2024-10-23",
            value: 13617216,
          },
          {
            time: "2024-10-24",
            value: 15908431,
          },
          {
            time: "2024-10-25",
            value: 9453996,
          },
          {
            time: "2024-10-28",
            value: 11941974,
          },
          {
            time: "2024-10-29",
            value: 15745226,
          },
          {
            time: "2024-10-30",
            value: 11074326,
          },
          {
            time: "2024-10-31",
            value: 13332393,
          },
          {
            time: "2024-11-01",
            value: 13212705,
          },
          {
            time: "2024-11-04",
            value: 18321430,
          },
          {
            time: "2024-11-05",
            value: 8102496,
          },
          {
            time: "2024-11-06",
            value: 21004956,
          },
          {
            time: "2024-11-07",
            value: 15996565,
          },
          {
            time: "2024-11-08",
            value: 24778767,
          },
          {
            time: "2024-11-11",
            value: 40900657,
          },
          {
            time: "2024-11-12",
            value: 23977950,
          },
          {
            time: "2024-11-13",
            value: 22299796,
          },
          {
            time: "2024-11-14",
            value: 26584281,
          },
          {
            time: "2024-11-15",
            value: 27622921,
          },
          {
            time: "2024-11-18",
            value: 20617517,
          },
          {
            time: "2024-11-19",
            value: 15855294,
          },
          {
            time: "2024-11-20",
            value: 22767490,
          },
          {
            time: "2024-11-21",
            value: 12784099,
          },
          {
            time: "2024-11-22",
            value: 17276669,
          },
          {
            time: "2024-11-25",
            value: 12839515,
          },
          {
            time: "2024-11-26",
            value: 13190635,
          },
          {
            time: "2024-11-27",
            value: 7974789,
          },
          {
            time: "2024-11-28",
            value: 14139732,
          },
          {
            time: "2024-11-29",
            value: 13886852,
          },
          {
            time: "2024-12-02",
            value: 18590525,
          },
          {
            time: "2024-12-03",
            value: 15823157,
          },
          {
            time: "2024-12-04",
            value: 12517041,
          },
          {
            time: "2024-12-05",
            value: 44772623,
          },
          {
            time: "2024-12-06",
            value: 23768664,
          },
          {
            time: "2024-12-09",
            value: 15967778,
          },
          {
            time: "2024-12-10",
            value: 32715496,
          },
          {
            time: "2024-12-11",
            value: 17045103,
          },
          {
            time: "2024-12-12",
            value: 20976380,
          },
          {
            time: "2024-12-13",
            value: 17278081,
          },
          {
            time: "2024-12-16",
            value: 16666364,
          },
          {
            time: "2024-12-17",
            value: 10816933,
          },
          {
            time: "2024-12-18",
            value: 12801871,
          },
          {
            time: "2024-12-19",
            value: 30343905,
          },
          {
            time: "2024-12-20",
            value: 13753770,
          },
          {
            time: "2024-12-23",
            value: 12907390,
          },
          {
            time: "2024-12-24",
            value: 13471873,
          },
          {
            time: "2024-12-25",
            value: 17153771,
          },
          {
            time: "2024-12-26",
            value: 9982189,
          },
          {
            time: "2024-12-27",
            value: 11397592,
          },
          {
            time: "2024-12-30",
            value: 12176491,
          },
          {
            time: "2024-12-31",
            value: 10258083,
          },
          {
            time: "2025-01-02",
            value: 14970526,
          },
          {
            time: "2025-01-03",
            value: 13208512,
          },
          {
            time: "2025-01-06",
            value: 15267581,
          },
          {
            time: "2025-01-07",
            value: 14414801,
          },
          {
            time: "2025-01-08",
            value: 12963197,
          },
          {
            time: "2025-01-09",
            value: 9252679,
          },
          {
            time: "2025-01-10",
            value: 14290074,
          },
          {
            time: "2025-01-13",
            value: 13586454,
          },
          {
            time: "2025-01-14",
            value: 7670367,
          },
          {
            time: "2025-01-15",
            value: 11228889,
          },
          {
            time: "2025-01-16",
            value: 11215124,
          },
          {
            time: "2025-01-17",
            value: 13956621,
          },
          {
            time: "2025-01-20",
            value: 9665010,
          },
          {
            time: "2025-01-21",
            value: 10429273,
          },
          {
            time: "2025-01-22",
            value: 10285189,
          },
          {
            time: "2025-01-23",
            value: 15800444,
          },
          {
            time: "2025-01-24",
            value: 10409008,
          },
          {
            time: "2025-02-03",
            value: 16987354,
          },
          {
            time: "2025-02-04",
            value: 17370750,
          },
          {
            time: "2025-02-05",
            value: 14477772,
          },
          {
            time: "2025-02-06",
            value: 13545529,
          },
          {
            time: "2025-02-07",
            value: 12556995,
          },
          {
            time: "2025-02-10",
            value: 61143342,
          },
          {
            time: "2025-02-11",
            value: 28914141,
          },
          {
            time: "2025-02-12",
            value: 13674965,
          },
          {
            time: "2025-02-13",
            value: 9507535,
          },
          {
            time: "2025-02-14",
            value: 18592795,
          },
          {
            time: "2025-02-17",
            value: 14380031,
          },
          {
            time: "2025-02-18",
            value: 24951942,
          },
          {
            time: "2025-02-19",
            value: 13664314,
          },
          {
            time: "2025-02-20",
            value: 11920169,
          },
          {
            time: "2025-02-21",
            value: 11076712,
          },
          {
            time: "2025-02-24",
            value: 73928603,
          },
          {
            time: "2025-02-25",
            value: 31597535,
          },
          {
            time: "2025-02-26",
            value: 31840327,
          },
          {
            time: "2025-02-27",
            value: 51151649,
          },
          {
            time: "2025-02-28",
            value: 24230428,
          },
          {
            time: "2025-03-03",
            value: 20678579,
          },
          {
            time: "2025-03-04",
            value: 27441800,
          },
          {
            time: "2025-03-05",
            value: 30401582,
          },
          {
            time: "2025-03-06",
            value: 36989136,
          },
          {
            time: "2025-03-07",
            value: 33148420,
          },
          {
            time: "2025-03-10",
            value: 23601359,
          },
          {
            time: "2025-03-11",
            value: 26105585,
          },
          {
            time: "2025-03-12",
            value: 20664501,
          },
          {
            time: "2025-03-13",
            value: 24826490,
          },
          {
            time: "2025-03-14",
            value: 18298151,
          },
          {
            time: "2025-03-17",
            value: 19738337,
          },
          {
            time: "2025-03-18",
            value: 18761045,
          },
          {
            time: "2025-03-19",
            value: 37953539,
          },
          {
            time: "2025-03-20",
            value: 25038597,
          },
          {
            time: "2025-03-21",
            value: 18921643,
          },
          {
            time: "2025-03-24",
            value: 16247689,
          },
          {
            time: "2025-03-25",
            value: 20115102,
          },
          {
            time: "2025-03-26",
            value: 43997569,
          },
          {
            time: "2025-03-27",
            value: 12023359,
          },
          {
            time: "2025-03-28",
            value: 17090800,
          },
          {
            time: "2025-03-31",
            value: 26565641,
          },
          {
            time: "2025-04-01",
            value: 14037535,
          },
          {
            time: "2025-04-02",
            value: 16797845,
          },
          {
            time: "2025-04-03",
            value: 80792245,
          },
          {
            time: "2025-04-04",
            value: 73222029,
          },
          {
            time: "2025-04-08",
            value: 35517471,
          },
          {
            time: "2025-04-09",
            value: 77212228,
          },
          {
            time: "2025-04-10",
            value: 3066909,
          },
          {
            time: "2025-04-11",
            value: 76305239,
          },
          {
            time: "2025-04-14",
            value: 51254525,
          },
          {
            time: "2025-04-15",
            value: 50611194,
          },
          {
            time: "2025-04-16",
            value: 25738379,
          },
          {
            time: "2025-04-17",
            value: 24465980,
          },
          {
            time: "2025-04-18",
            value: 20785143,
          },
          {
            time: "2025-04-21",
            value: 17327776,
          },
          {
            time: "2025-04-22",
            value: 47493704,
          },
          {
            time: "2025-04-23",
            value: 22246788,
          },
          {
            time: "2025-04-24",
            value: 17227712,
          },
          {
            time: "2025-04-25",
            value: 24864961,
          },
          {
            time: "2025-04-28",
            value: 9768183,
          },
          {
            time: "2025-04-29",
            value: 27916600,
          },
        ],
      },
    };
  </script>
  <script>
    let priceChartId = "chart-price-history";
    let volumeChartId = "chart-vol-history";

    let priceChart;

    // Initialize chart when DOM is loaded
    document.addEventListener("DOMContentLoaded", function () {
      const priceChartContainer = document.getElementById(priceChartId);

      priceChart = LightweightCharts.createChart(priceChartContainer, {
        autoSize: false,
        autoScale: false,
        width: priceChartContainer.clientWidth,
        height: priceChartContainer.clientHeight,
        layout: {
          backgroundColor: "#ffffff",
          textColor: "#333",
          attributionLogo: false,
          panes: {
            separatorColor: "#F3F4F6",
            separatorHoverColor: "#F3F4F6",
            enableResize: true,
          },
        },
        grid: {
          vertLines: { color: "#FBFBFC" },
          horzLines: { color: "#FBFBFC" },
        },
        crosshair: {
          mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
          borderColor: "#F3F4F6",
          scaleMargins: {
            top: 0.3, // leave some space for the legend
            bottom: 0.25,
          },
        },
        timeScale: {
          borderColor: "#F3F4F6",
          timeVisible: true,
          secondsVisible: false,
        },
      });

      const volumeSeries = priceChart.addSeries(
        LightweightCharts.HistogramSeries,
        {
          priceFormat: {
            type: "volume",
          },
        },
        1
      );
      volumeSeries.setData(ChartData.priceHistory.volumes);
      volumeSeries.priceScale().applyOptions({
        autoScale: true,
        scaleMargins: {
          top: 0.1, // highest point of the series will be 70% away from the top
          bottom: 0,
        },
      });
      const secondPane = priceChart.panes()[1];
      secondPane.setHeight(110);
      // secondPane.moveTo(0);

      const maData = calculateMovingAverageSeriesData(
        ChartData.priceHistory.closePrices,
        20
      );

      const maSeries = priceChart.addSeries(LightweightCharts.LineSeries, {
        color: "#2962FF",
        lineWidth: 1,
      });
      maSeries.setData(maData);

      const maData5 = calculateMovingAverageSeriesData(
        ChartData.priceHistory.closePrices,
        5
      );

      const maSeries5 = priceChart.addSeries(LightweightCharts.LineSeries, {
        color: "#orange",
        lineWidth: 1,
      });
      maSeries5.setData(maData5);

      const priceSeries = priceChart.addSeries(
        LightweightCharts.CandlestickSeries,
        {
          upColor: "#26a69a",
          downColor: "#ef5350",
          borderVisible: false,
          wickUpColor: "#26a69a",
          wickDownColor: "#ef5350",
        }
      );
      priceSeries.setData(ChartData.priceHistory.closePrices);

      const maxPriceLine = {
        price: 28510,
        color: "#26a69a",
        lineWidth: 2,
        lineStyle: 2, // LineStyle.Dashed
        axisLabelVisible: true,
        title: "bid",
      };

      priceSeries.createPriceLine(maxPriceLine);

      const markers = [
        {
          time: ChartData.priceHistory.closePrices[
            ChartData.priceHistory.closePrices.length - 7
          ].time,
          position: "aboveBar",
          color: "#f68410",
          shape: "circle",
          text: "HPG: Chia cổ tức",
        },
        {
          time: ChartData.priceHistory.closePrices[
            ChartData.priceHistory.closePrices.length - 5
          ].time,
          position: "aboveBar",
          color: "#e91e63",
          shape: "arrowDown",
          text:
            "Sell @ " +
            ChartData.priceHistory.closePrices[
              ChartData.priceHistory.closePrices.length - 5
            ].close,
        },
        {
          time: ChartData.priceHistory.closePrices[
            ChartData.priceHistory.closePrices.length - 10
          ].time,
          position: "belowBar",
          color: "#2196F3",
          shape: "arrowUp",
          text:
            "Buy @ " +
            ChartData.priceHistory.closePrices[
              ChartData.priceHistory.closePrices.length - 10
            ].close,
        },
      ];
      LightweightCharts.createSeriesMarkers(priceSeries, markers);

      const toolTipWidth = 80;
      const toolTipHeight = 80;
      const toolTipMargin = 15;
      // Create and style the tooltip html element
      const toolTip = document.createElement("div");
      toolTip.style = `width: 96px; height: 80px; position: absolute; display: none; padding: 8px; box-sizing: border-box; font-size: 12px; text-align: left; z-index: 1000; top: 12px; left: 12px; pointer-events: none; border: 1px solid; border-radius: 2px;font-family: -apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;`;
      toolTip.style.background = "white";
      toolTip.style.color = "black";
      toolTip.style.borderColor = "rgba( 38, 166, 154, 1)";
      priceChartContainer.appendChild(toolTip);

      // Add legend
      const symbolName = "ETC USD 7D VWAP";
      const legend = document.createElement("div");
      legend.style = `position: absolute; left: 12px; top: 12px; z-index: 1; font-size: 14px; font-family: sans-serif; line-height: 18px; font-weight: 300;`;
      priceChartContainer.appendChild(legend);

      const firstRow = document.createElement("div");
      firstRow.innerHTML = symbolName;
      firstRow.style.color = "black";
      legend.appendChild(firstRow);

      // update tooltip
      priceChart.subscribeCrosshairMove((param) => {
        if (
          param.point === undefined ||
          !param.time ||
          param.point.x < 0 ||
          param.point.x > priceChartContainer.clientWidth ||
          param.point.y < 0 ||
          param.point.y > priceChartContainer.clientHeight
        ) {
          toolTip.style.display = "none";
        } else {
          // time will be in the same format that we supplied to setData.
          // thus it will be YYYY-MM-DD
          const dateStr = param.time;
          toolTip.style.display = "block";
          const data = param.seriesData.get(priceSeries);
          const price = data.value !== undefined ? data.value : data.close;
          toolTip.innerHTML = `<div style="color: ${"rgba( 38, 166, 154, 1)"}">ABC Inc.</div><div style="font-size: 24px; margin: 4px 0px; color: ${"black"}">
            ${Math.round(100 * price) / 100}
            </div><div style="color: ${"black"}">
            ${dateStr}
            </div>`;

          const coordinate = priceSeries.priceToCoordinate(price);
          let shiftedCoordinate = param.point.x - 50;
          if (coordinate != null) {
            shiftedCoordinate = Math.max(
              0,
              Math.min(
                priceChartContainer.clientWidth - toolTipWidth,
                shiftedCoordinate
              )
            );
            const coordinateY =
              coordinate - toolTipHeight - toolTipMargin > 0
                ? coordinate - toolTipHeight - toolTipMargin
                : Math.max(
                    0,
                    Math.min(
                      priceChartContainer.clientHeight -
                        toolTipHeight -
                        toolTipMargin,
                      coordinate + toolTipMargin
                    )
                  );
            toolTip.style.left = shiftedCoordinate + "px";
            toolTip.style.top = coordinateY + "px";
          }
        }

        // Handle for legend
        let priceFormatted = "";
        if (param.time) {
          const data = param.seriesData.get(priceSeries);
          const price = data.value !== undefined ? data.value : data.close;
          priceFormatted = price.toFixed(2);
        }
        firstRow.innerHTML = `${symbolName} <strong>${priceFormatted}</strong>`;

        const getLastBar = (series) => {
          const lastIndex = series.dataByIndex(Number.MAX_SAFE_INTEGER, -1);
          return series.dataByIndex(lastIndex);
        };
        const formatPrice = (price) =>
          (Math.round(price * 100) / 100).toFixed(2);
        const setTooltipHtml = (name, date, price) => {
          legend.innerHTML = `<div style="font-size: 24px; margin: 4px 0px;">${name}</div><div style="font-size: 22px; margin: 4px 0px;">${price}</div><div>${date}</div>`;
        };
        const validCrosshairPoint = !(
          param === undefined ||
          param.time === undefined ||
          param.point.x < 0 ||
          param.point.y < 0
        );
        const bar = validCrosshairPoint
          ? param.seriesData.get(priceSeries)
          : getLastBar(priceSeries);
        // time is in the same format that you supplied to the setData method,
        // which in this case is YYYY-MM-DD
        const time = bar.time;
        const price = bar.value !== undefined ? bar.value : bar.close;
        const formattedPrice = formatPrice(price);
        setTooltipHtml(symbolName, time, formattedPrice);
      });

      priceSeries.priceScale().applyOptions({
        autoScale: false,
        scaleMargins: {
          top: 0.3, // leave some space for the legend
          bottom: 0.25,
        },
      });

      // priceChart.timeScale().fitContent();

      // Handle window resize
      // window.addEventListener('resize', function () {
      //   priceChart.applyOptions({
      //     width: priceChartContainer.clientWidth,
      //   });
      // });
    });

    function calculateMovingAverageSeriesData(candleData, maLength) {
      const maData = [];

      for (let i = 0; i < candleData.length; i++) {
        if (i < maLength) {
          // Provide whitespace data points until the MA can be calculated
          maData.push({ time: candleData[i].time });
        } else {
          // Calculate the moving average, slow but simple way
          let sum = 0;
          for (let j = 0; j < maLength; j++) {
            sum += candleData[i - j].close;
          }
          const maValue = sum / maLength;
          maData.push({ time: candleData[i].time, value: maValue });
        }
      }

      return maData;
    }

    // Calculate SMA data
    function calculateSMA(data, period) {
      const smaData = [];
      for (let i = period - 1; i < data.length; i++) {
        const sum = data
          .slice(i - period + 1, i + 1)
          .reduce((total, price) => total + price.close, 0);
        smaData.push({
          time: data[i].time,
          value: sum / period,
        });
      }
      return smaData;
    }

    function initChartPriceVolHistory() {
      const options = {
        animation: false,
        spanGaps: true,
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      };
      const ctx = document.getElementById("chart-price-vol-history");
      const mixedChart = new Chart(ctx, {
        data: {
          datasets: [
            {
              type: "line",
              label: "Price",
              data: lineData,
              order: 1,
            },
            {
              type: "bar",
              label: "Volume",
              data: volumeData,
              order: 2,
            },
          ],
          labels: ["January", "February", "March", "April"],
        },
        options: options,
      });
    }
  </script>
</html>