"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  getTrendIcon,
  getTrendClass,
  getStrengthIcon,
  getConfidenceIcon,
} from "@/lib/stock-helpers";

interface StockTableProps {
  stocks: any[];
  onRowClick: (symbol: string) => void;
}

export function StockTable({ stocks, onRowClick }: StockTableProps) {
  // Calculate indicator summary
  const getIndicatorSummary = (stock: any) => {
    const { ma_signals, indicator_signals } = stock;

    const result = {
      ma_count: ma_signals && Array.isArray(ma_signals) ? ma_signals.length : 0,
      indicator_count:
        indicator_signals && Array.isArray(indicator_signals)
          ? indicator_signals.length
          : 0,
      ma_buy: 0,
      ma_sell: 0,
      ma_neutral: 0,
      tech_buy: 0,
      tech_sell: 0,
      tech_neutral: 0,
    };

    // Count MA signals by type
    if (ma_signals && Array.isArray(ma_signals)) {
      result.ma_buy = ma_signals.filter((s: any) => s.signal === "Mua").length;
      result.ma_sell = ma_signals.filter((s: any) => s.signal === "Bán").length;
      result.ma_neutral = ma_signals.filter(
        (s: any) => s.signal === "Trung tính"
      ).length;
    }

    // Count technical indicators by type
    if (indicator_signals && Array.isArray(indicator_signals)) {
      result.tech_buy = indicator_signals.filter(
        (s: any) => s.signal === "Mua"
      ).length;
      result.tech_sell = indicator_signals.filter(
        (s: any) => s.signal === "Bán"
      ).length;
      result.tech_neutral = indicator_signals.filter(
        (s: any) => s.signal === "Trung tính"
      ).length;
    }

    return result;
  };

  // Format text with line breaks
  const formatText = (text: string) => {
    return text.split("\n").map((line, i) => <p key={i}>{line}</p>);
  };

  return (
    <div className="overflow-hidden rounded-lg border">
      <Table>
        <TableHeader className="sticky top-0 z-10 bg-muted">
          <TableRow>
            <TableHead className="text-center">Mã CK</TableHead>
            <TableHead className="text-right">Giá</TableHead>
            <TableHead>Xu hướng</TableHead>
            <TableHead>Độ mạnh</TableHead>
            <TableHead>Độ tin cậy</TableHead>
            <TableHead>MA</TableHead>
            <TableHead>Kỹ thuật</TableHead>
            <TableHead className="min-w-[200px]">Khuyến nghị</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {stocks.map((stock, idx) => {
            const summary = getIndicatorSummary(stock);
            return (
              <TableRow
                key={stock.symbol}
                className="border-b transition-colors hover:bg-muted/50"
                onClick={() => onRowClick(stock.symbol)}
              >
                <TableCell>
                  <div className="flex items-center gap-2">
                    <img
                      src={`/assets/logos/${stock.symbol}.svg`}
                      alt={stock.symbol}
                      className="w-6 h-6 mx-auto rounded-full"
                    />
                    <span className="text-sm font-medium">{stock.symbol}</span>
                  </div>
                </TableCell>
                <TableCell className="text-right font-semibold">
                  {stock.current_price}
                </TableCell>
                <TableCell className={getTrendClass(stock.trend_direction)}>
                  <div className="flex items-center gap-1">
                    {getTrendIcon(stock.trend_direction)}
                    <span>{stock.trend_direction}</span>
                  </div>
                </TableCell>
                <TableCell className="text-blue-500">
                  <div className="flex items-center gap-1">
                    {getStrengthIcon(stock.trend_strength)}
                    <span>{stock.trend_strength}</span>
                  </div>
                </TableCell>
                <TableCell className="text-yellow-500">
                  <div className="flex items-center justify-center gap-1">
                    {getConfidenceIcon(stock.trend_confidence)}
                    <span>{stock.trend_confidence}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {stock.ma_signals && stock.ma_signals.length > 0 ? (
                    <div className="flex items-center gap-1 justify-center">
                      <span className="w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 text-blue-800 font-medium">
                        {summary.ma_count}
                      </span>
                      <div className="flex">
                        <span className="px-1 rounded-l bg-green-100 text-green-800">
                          {summary.ma_buy}
                        </span>
                        <span className="px-1 bg-red-100 text-red-800">
                          {summary.ma_sell}
                        </span>
                        <span className="px-1 rounded-r bg-gray-100 text-gray-800">
                          {summary.ma_neutral}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <span className="text-gray-500">Không có dữ liệu</span>
                  )}
                </TableCell>
                <TableCell>
                  {stock.indicator_signals &&
                  stock.indicator_signals.length > 0 ? (
                    <div className="flex items-center gap-1 justify-center">
                      <span className="w-6 h-6 flex items-center justify-center rounded-full bg-purple-100 text-purple-800 font-medium">
                        {summary.indicator_count}
                      </span>
                      <div className="flex">
                        <span className="px-1 rounded-l bg-green-100 text-green-800">
                          {summary.tech_buy}
                        </span>
                        <span className="px-1 bg-red-100 text-red-800">
                          {summary.tech_sell}
                        </span>
                        <span className="px-1 rounded-r bg-gray-100 text-gray-800">
                          {summary.tech_neutral}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <span className=" text-gray-500">Không có dữ liệu</span>
                  )}
                </TableCell>
                <TableCell className="max-w-[250px] recommendation-text">
                  {formatText(stock.recommendation)}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
