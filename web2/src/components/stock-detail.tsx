"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  getTrendIcon,
  getTrendClass,
  getStrengthIcon,
  getConfidenceIcon,
} from "@/lib/stock-helpers";
import { loadStockAnalysis } from "@/lib/analysis-loader";

interface StockDetailProps {
  symbol: string;
  stock?: any;
}

export function StockDetail({ symbol, stock: initialStock }: StockDetailProps) {
  const [stock, setStock] = useState<any>(initialStock);
  const [loading, setLoading] = useState(!initialStock);

  // Load stock data if not provided in props
  useEffect(() => {
    if (!initialStock) {
      const fetchStockData = async () => {
        setLoading(true);
        try {
          const data = await loadStockAnalysis(symbol);
          if (data) {
            setStock(data);
          }
        } catch (error) {
          console.error(`Error loading data for ${symbol}:`, error);
        } finally {
          setLoading(false);
        }
      };

      fetchStockData();
    }
  }, [symbol, initialStock]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[300px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">
            Đang tải dữ liệu cho mã {symbol}...
          </p>
        </div>
      </div>
    );
  }

  if (!stock) {
    return (
      <div className="flex items-center justify-center h-[300px]">
        <p className="text-muted-foreground">
          Không tìm thấy dữ liệu cho mã {symbol}
        </p>
      </div>
    );
  }

  // Calculate indicator summary
  const getIndicatorSummary = () => {
    const { ma_signals, indicator_signals } = stock;

    const result = {
      ma_count: ma_signals && Array.isArray(ma_signals) ? ma_signals.length : 0,
      indicator_count:
        indicator_signals && Array.isArray(indicator_signals)
          ? indicator_signals.length
          : 0,
      total_count: 0,
      ma_buy: 0,
      ma_sell: 0,
      ma_neutral: 0,
      tech_buy: 0,
      tech_sell: 0,
      tech_neutral: 0,
      total_buy: 0,
      total_sell: 0,
      total_neutral: 0,
    };

    result.total_count = result.ma_count + result.indicator_count;

    // Count MA signals by type
    if (ma_signals && Array.isArray(ma_signals)) {
      result.ma_buy = ma_signals.filter((s) => s.signal === "Mua").length;
      result.ma_sell = ma_signals.filter((s) => s.signal === "Bán").length;
      result.ma_neutral = ma_signals.filter(
        (s) => s.signal === "Trung tính"
      ).length;
    }

    // Count technical indicators by type
    if (indicator_signals && Array.isArray(indicator_signals)) {
      result.tech_buy = indicator_signals.filter(
        (s) => s.signal === "Mua"
      ).length;
      result.tech_sell = indicator_signals.filter(
        (s) => s.signal === "Bán"
      ).length;
      result.tech_neutral = indicator_signals.filter(
        (s) => s.signal === "Trung tính"
      ).length;
    }

    // Calculate totals
    result.total_buy = result.ma_buy + result.tech_buy;
    result.total_sell = result.ma_sell + result.tech_sell;
    result.total_neutral = result.ma_neutral + result.tech_neutral;

    return result;
  };

  // Group MA signals by period
  const groupMAByPeriod = () => {
    const { ma_signals } = stock;

    if (!ma_signals || !Array.isArray(ma_signals)) return [];

    const periods = [5, 10, 20, 50, 100, 200];
    const result = [];

    // Create an entry for each period
    for (const period of periods) {
      const smaSignal = ma_signals.find(
        (s) => s.period === period && s.type === "SMA"
      );
      const emaSignal = ma_signals.find(
        (s) => s.period === period && s.type === "EMA"
      );

      result.push({
        period,
        sma: {
          value: smaSignal ? smaSignal.value : null,
          signal: smaSignal ? smaSignal.signal : null,
        },
        ema: {
          value: emaSignal ? emaSignal.value : null,
          signal: emaSignal ? emaSignal.signal : null,
        },
      });
    }

    return result;
  };

  // Format text with line breaks
  const formatText = (text: string) => {
    return text.split("\n").map((line, i) => (
      <p key={i} className="mb-2">
        {line}
      </p>
    ));
  };

  // Format number to 2 decimal places
  const format2Decimals = (value: any) => {
    if (!value) return value;

    // If it's already a number, just format it
    if (typeof value === "number") {
      return value.toFixed(2);
    }

    // If it's a string, try to parse it correctly
    if (typeof value === "string") {
      // For Vietnamese formatted strings like "10.000" (ten thousand)
      // First remove all dots used as thousand separators
      const cleanValue = value.replace(/\./g, "");

      // Then parse as float
      const num = parseFloat(cleanValue);

      if (!isNaN(num)) {
        return num.toFixed(2);
      }
    }

    // If parsing fails, return the original value
    return value;
  };

  const summary = getIndicatorSummary();
  const maByPeriod = groupMAByPeriod();

  // Helper: Group indicators by type for mobile-style UI
  const groupIndicators = () => {
    // Định nghĩa nhóm chỉ báo (có thể điều chỉnh theo thực tế dữ liệu)
    const momentumNames = [
      "RSI",
      "RSI(14)",
      "Rsi(14)",
      "CCI",
      "CCI(20)",
      "%K",
      "%K(13,5)",
      "%D",
      "%D(13,5,5)",
      "Stochastic",
      "Williams %R",
      "Momentum",
      "ROC",
    ];
    const moneyFlowNames = [
      "MFI",
      "MFI(20)",
      "OBV",
      "CMF",
      "ADL",
      "Money Flow",
      "OBV(20)",
      "CMF(20)",
    ];
    const pivotsNames = [
      "Pivot",
      "Traditional",
      "Fibonacci",
      "Camarilla",
      "R1",
      "R2",
      "R3",
      "R4",
      "S1",
      "S2",
      "S3",
      "S4",
      "P",
    ];
    const momentum: any[] = [];
    const moneyFlow: any[] = [];
    const pivots: any[] = [];
    const others: any[] = [];
    if (Array.isArray(stock.indicator_signals)) {
      for (const ind of stock.indicator_signals) {
        const n = ind.name.trim();
        if (
          momentumNames.some((m) => n.toLowerCase().includes(m.toLowerCase()))
        )
          momentum.push(ind);
        else if (
          moneyFlowNames.some((m) => n.toLowerCase().includes(m.toLowerCase()))
        )
          moneyFlow.push(ind);
        else if (
          pivotsNames.some((m) => n.toLowerCase().includes(m.toLowerCase()))
        )
          pivots.push(ind);
        else others.push(ind);
      }
    }
    return { momentum, moneyFlow, pivots, others };
  };

  // Helper: Màu nền theo tín hiệu
  const getSignalBg = (signal: string) => {
    if (signal === "Mua") return "bg-green-600 text-white";
    if (signal === "Bán") return "bg-red-600 text-white";
    return "bg-orange-400 text-white";
  };

  return (
    <div className="space-y-4">
      {/* Thông tin cơ bản */}
      <Card>
        <CardContent className="p-3">
          <div className="flex flex-col md:flex-row mb-4 gap-3">
            <div className="flex items-center gap-3">
              <img
                src={`/assets/logos/${stock.symbol}.svg`}
                alt={stock.symbol}
                className="w-10 h-10 md:w-12 md:h-12 flex-shrink-0"
              />
              <div className="flex flex-col min-w-0">
                <div className="font-bold mb-1 truncate">{stock.symbol}</div>
                <div className="flex items-end gap-1">
                  <span className="text-lg font-extrabold">
                    {stock.current_price}
                  </span>
                  {stock.trend_change !== undefined &&
                    stock.trend_change !== null && (
                      <span
                        className={`inline-flex items-center gap-1 px-1.5 py-0.5 rounded-full font-semibold ${
                          parseFloat(stock.trend_change) > 0
                            ? "bg-green-50 text-green-600"
                            : parseFloat(stock.trend_change) < 0
                            ? "bg-red-50 text-red-600"
                            : "bg-gray-50 text-gray-600"
                        }`}
                      >
                        {parseFloat(stock.trend_change) > 0
                          ? getTrendIcon("Tăng")
                          : parseFloat(stock.trend_change) < 0
                          ? getTrendIcon("Giảm")
                          : getTrendIcon("Đi ngang")}
                        <span>
                          {parseFloat(stock.trend_change) > 0 ? "+" : ""}
                          {stock.trend_change}
                        </span>
                        {stock.trend_change_percent !== undefined &&
                          stock.trend_change_percent !== null && (
                            <span>
                              (
                              {parseFloat(stock.trend_change_percent) > 0
                                ? "+"
                                : ""}
                              {stock.trend_change_percent}%)
                            </span>
                          )}
                      </span>
                    )}
                </div>
              </div>
            </div>
            <div className="flex flex-row gap-2 md:gap-3 flex-shrink-0">
              <div className="flex flex-col items-center bg-gray-50 rounded-lg px-2 py-1 min-w-[60px]">
                <span
                  className={`mb-1 ${getTrendClass(stock.trend_direction)}`}
                >
                  {getTrendIcon(stock.trend_direction)}
                </span>
                <span className="text-gray-500">Xu hướng</span>
                <span
                  className={`font-semibold ${getTrendClass(
                    stock.trend_direction
                  )}`}
                >
                  {stock.trend_direction}
                </span>
              </div>
              <div className="flex flex-col items-center bg-gray-50 rounded-lg px-2 py-1 min-w-[60px]">
                <span className="mb-1  text-blue-500">
                  {getStrengthIcon(stock.trend_strength)}
                </span>
                <span className=" text-gray-500">Độ mạnh</span>
                <span className="font-semibold  text-blue-500">
                  {stock.trend_strength}
                </span>
              </div>
              <div className="flex flex-col items-center bg-gray-50 rounded-lg px-2 py-1 min-w-[60px]">
                <span className="mb-1  text-yellow-500">
                  {getConfidenceIcon(stock.trend_confidence)}
                </span>
                <span className=" text-gray-500">Tin cậy</span>
                <span className="font-semibold  text-yellow-500">
                  {stock.trend_confidence}
                </span>
              </div>
            </div>
          </div>

          {/* Khuyến nghị */}
          {stock.recommendation && (
            <div className="mb-4">
              <h3 className="font-semibold mb-2 text-blue-700 ">Khuyến nghị</h3>
              <div className="p-3 rounded-lg recommendation-text ">
                {formatText(stock.recommendation)}
              </div>
            </div>
          )}

          {/* Tổng hợp chỉ báo kỹ thuật */}
          <div className="mb-4">
            <h3 className="font-semibold mb-2 text-blue-700 ">
              Tổng hợp chỉ báo kỹ thuật
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-2">
              {/* MA Signals */}
              <div className="bg-white rounded-lg p-2 shadow-sm border ">
                <h4 className="font-medium text-blue-700 mb-1 text-center ">
                  Đường MA
                </h4>
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 mx-auto mb-1">
                  <span className=" font-bold text-blue-700">
                    {summary.ma_count}
                  </span>
                </div>
                <div className="flex gap-1 justify-center mb-1">
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">Mua</span>
                    <span className=" font-bold text-green-700">
                      {summary.ma_buy}
                    </span>
                  </div>
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">Bán</span>
                    <span className=" font-bold text-red-700">
                      {summary.ma_sell}
                    </span>
                  </div>
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">TT</span>
                    <span className=" font-bold text-gray-700">
                      {summary.ma_neutral}
                    </span>
                  </div>
                </div>
                <div className="text-center  text-gray-500">
                  SMA/EMA các chu kỳ 5, 10, 20, 50, 100, 200
                </div>
              </div>
              {/* Technical Indicator Signals */}
              <div className="bg-white rounded-lg p-2 shadow-sm border ">
                <h4 className="font-medium text-blue-700 mb-1 text-center ">
                  Chỉ báo kỹ thuật
                </h4>
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 mx-auto mb-1">
                  <span className=" font-bold text-purple-700">
                    {summary.indicator_count}
                  </span>
                </div>
                <div className="flex gap-1 justify-center mb-1">
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">Mua</span>
                    <span className=" font-bold text-green-700">
                      {summary.tech_buy}
                    </span>
                  </div>
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">Bán</span>
                    <span className=" font-bold text-red-700">
                      {summary.tech_sell}
                    </span>
                  </div>
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">TT</span>
                    <span className=" font-bold text-gray-700">
                      {summary.tech_neutral}
                    </span>
                  </div>
                </div>
                <div className="text-center  text-gray-500">
                  RSI, MACD, Stochastic, CCI, Williams %R, v.v.
                </div>
              </div>
              {/* Combined Signal Analysis */}
              <div className="bg-white rounded-lg p-2 shadow-sm border ">
                <h4 className="font-medium text-blue-700 mb-1 text-center ">
                  Tổng hợp
                </h4>
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-100 mx-auto mb-1">
                  <span className=" font-bold text-green-700">
                    {summary.total_count}
                  </span>
                </div>
                <div className="flex gap-1 justify-center mb-1">
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">Mua</span>
                    <span className=" font-bold text-green-700">
                      {summary.total_buy}
                    </span>
                  </div>
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">Bán</span>
                    <span className=" font-bold text-red-700">
                      {summary.total_sell}
                    </span>
                  </div>
                  <div className="flex flex-col items-center bg-gray-50 rounded-lg p-1 flex-1">
                    <span className=" text-gray-500">TT</span>
                    <span className=" font-bold text-gray-700">
                      {summary.total_neutral}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* MA Signals Table */}
      <div className="mb-4">
        <h3 className="font-semibold mb-2 text-blue-700 ">
          Phân tích đường MA
        </h3>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="">Chu kỳ</TableHead>
              <TableHead className="text-right ">SMA</TableHead>
              <TableHead className="text-right ">EMA</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {maByPeriod.map((item) => (
              <TableRow key={item.period} className="">
                <TableCell className="font-medium ">{item.period}</TableCell>
                <TableCell className="text-right ">
                  {item.sma.value ? (
                    <div className="flex items-center justify-end gap-1">
                      <span>{format2Decimals(item.sma.value)}</span>
                      {item.sma.signal && (
                        <span
                          className={`px-1 py-0.5 rounded-full  font-medium ${
                            item.sma.signal === "Mua"
                              ? "bg-green-100 text-green-800"
                              : item.sma.signal === "Bán"
                              ? "bg-red-100 text-red-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {item.sma.signal}
                        </span>
                      )}
                    </div>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-right ">
                  {item.ema.value ? (
                    <div className="flex items-center justify-end gap-1">
                      <span>{format2Decimals(item.ema.value)}</span>
                      {item.ema.signal && (
                        <span
                          className={`px-1 py-0.5 rounded-full  font-medium ${
                            item.ema.signal === "Mua"
                              ? "bg-green-100 text-green-800"
                              : item.ema.signal === "Bán"
                              ? "bg-red-100 text-red-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {item.ema.signal}
                        </span>
                      )}
                    </div>
                  ) : (
                    "-"
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Technical Indicators Table */}
      {Array.isArray(stock.indicator_signals) &&
        stock.indicator_signals.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2 text-blue-700 ">
              Bảng chỉ báo kỹ thuật
            </h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="">Chỉ báo</TableHead>
                  <TableHead className="text-right ">Giá trị</TableHead>
                  <TableHead className="">Lực mua/bán</TableHead>
                  {stock.indicator_signals.some((i: any) => i.action) && (
                    <TableHead className="">Khuyến nghị</TableHead>
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {stock.indicator_signals.map(
                  (indicator: any, index: number) => (
                    <TableRow key={index} className="">
                      <TableCell className="font-medium ">
                        {indicator.name}
                        {indicator.info && (
                          <div className="relative inline-block group ml-1">
                            <span className="text-gray-400 cursor-help  align-middle">
                              ℹ️
                            </span>
                            <div className="absolute z-10 hidden group-hover:block bg-white border border-gray-200 shadow-lg rounded-lg p-2 w-48  text-left left-0 top-full">
                              {indicator.info}
                            </div>
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-right font-mono ">
                        {indicator.value !== undefined &&
                        indicator.value !== null
                          ? format2Decimals(indicator.value)
                          : "-"}
                      </TableCell>
                      <TableCell className="">
                        <span
                          className={`px-1 py-0.5 rounded  font-medium ${
                            indicator.signal === "Mua"
                              ? "bg-green-100 text-green-800"
                              : indicator.signal === "Bán"
                              ? "bg-red-100 text-red-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {indicator.signal}
                        </span>
                      </TableCell>
                      {indicator.action && (
                        <TableCell className="">{indicator.action}</TableCell>
                      )}
                    </TableRow>
                  )
                )}
              </TableBody>
            </Table>
          </div>
        )}

      {/* --- Technical Indicator Cards (Mobile style) --- */}
      {(() => {
        const { momentum, moneyFlow, pivots, others } = groupIndicators();
        // Chú thích màu tín hiệu
        const legend = (
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center gap-1">
              <span className="inline-block w-4 h-4 rounded bg-green-600"></span>{" "}
              Mua
            </div>
            <div className="flex items-center gap-1">
              <span className="inline-block w-4 h-4 rounded bg-red-600"></span>{" "}
              Bán
            </div>
            <div className="flex items-center gap-1">
              <span className="inline-block w-4 h-4 rounded bg-orange-400"></span>{" "}
              Trung tính
            </div>
          </div>
        );
        return (
          <div className="mb-4">
            {legend}
            {/* Momentum */}
            {momentum.length > 0 && (
              <div className="mb-3">
                <div className="font-semibold mb-2 text-blue-700">
                  Chỉ báo động lượng
                </div>
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                  {momentum.map((ind, idx) => (
                    <div
                      key={idx}
                      className={`rounded-lg flex flex-col items-center py-2 px-1 ${getSignalBg(
                        ind.signal
                      )} shadow-sm`}
                    >
                      <div className="font-semibold mb-1">{ind.name}</div>
                      <div className="font-bold">
                        {ind.value !== undefined && ind.value !== null
                          ? format2Decimals(ind.value)
                          : "--"}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            {/* Money Flow */}
            {moneyFlow.length > 0 && (
              <div className="mb-3">
                <div className="font-semibold mb-2 text-blue-700">
                  Chỉ báo dòng tiền
                </div>
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                  {moneyFlow.map((ind, idx) => (
                    <div
                      key={idx}
                      className={`rounded-lg flex flex-col items-center py-2 px-1 ${getSignalBg(
                        ind.signal
                      )} shadow-sm`}
                    >
                      <div className="font-semibold mb-1">{ind.name}</div>
                      <div className="font-bold">
                        {ind.value !== undefined && ind.value !== null
                          ? format2Decimals(ind.value)
                          : "--"}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            {/* Pivots */}
            {pivots.length > 0 && (
              <div className="mb-3">
                <div className="font-semibold mb-2 text-blue-700">PIVOTS</div>
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                  {pivots.map((ind, idx) => (
                    <div
                      key={idx}
                      className={`rounded-lg flex flex-col items-center py-2 px-1 ${getSignalBg(
                        ind.signal
                      )} shadow-sm`}
                    >
                      <div className="font-semibold mb-1">{ind.name}</div>
                      <div className="font-bold">
                        {ind.value !== undefined && ind.value !== null
                          ? format2Decimals(ind.value)
                          : "--"}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            {/* Others */}
            {others.length > 0 && (
              <div className="mb-3">
                <div className="font-semibold mb-2 text-blue-700">Khác</div>
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                  {others.map((ind, idx) => (
                    <div
                      key={idx}
                      className={`rounded-lg flex flex-col items-center py-2 px-1 ${getSignalBg(
                        ind.signal
                      )} shadow-sm`}
                    >
                      <div className="font-semibold mb-1">{ind.name}</div>
                      <div className="font-bold">
                        {ind.value !== undefined && ind.value !== null
                          ? format2Decimals(ind.value)
                          : "--"}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );
      })()}

      {/* Phân tích kỹ thuật */}
      {stock.technical_summary && (
        <div className="mb-4">
          <h3 className="font-semibold mb-2 text-blue-700 ">
            Phân tích kỹ thuật
          </h3>
          <div className="p-3 bg-gray-50 rounded-lg technical-summary ">
            {formatText(stock.technical_summary)}
          </div>
        </div>
      )}

      {/* Vùng giá mua */}
      {Array.isArray(stock.buy_zones) && stock.buy_zones.length > 0 && (
        <div className="mb-4">
          <h3 className="font-semibold mb-2 text-blue-700 ">Vùng giá mua</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {stock.buy_zones.map((zone: any, index: number) => (
              <div key={index} className="bg-green-50 rounded-lg p-3">
                <div className=" font-bold text-green-700 mb-1">
                  {zone.price}
                </div>
                <div className=" text-green-600 mb-1">
                  Độ tin cậy:{" "}
                  <span className="font-medium">{zone.confidence}</span>
                </div>
                <div className=" text-gray-600">{zone.reason}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Vùng giá chốt lời */}
      {Array.isArray(stock.take_profit_zones) &&
        stock.take_profit_zones.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2 text-blue-700 ">
              Vùng giá chốt lời
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {stock.take_profit_zones.map((zone: any, index: number) => (
                <div key={index} className="bg-blue-50 rounded-lg p-3">
                  <div className=" font-bold text-blue-700 mb-1">
                    {zone.price}
                  </div>
                  <div className=" text-blue-600 mb-1">
                    Độ tin cậy:{" "}
                    <span className="font-medium">{zone.confidence}</span>
                  </div>
                  <div className=" text-gray-600">{zone.reason}</div>
                </div>
              ))}
            </div>
          </div>
        )}

      {/* Tỷ lệ lợi nhuận/rủi ro */}
      {Array.isArray(stock.risk_reward_ratios) &&
        stock.risk_reward_ratios.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2 text-blue-700 ">
              Tỷ lệ lợi nhuận/rủi ro
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {stock.risk_reward_ratios.map((ratio: any, index: number) => (
                <div key={index} className="bg-yellow-50 rounded-lg p-3">
                  <div className=" font-bold text-yellow-700 mb-1">
                    {ratio.ratio}
                    {ratio.ratio && !String(ratio.ratio).includes(":")
                      ? ":1"
                      : ""}
                  </div>
                  {ratio.buy_price && (
                    <div className=" mb-1">
                      <span className="text-gray-600">Giá mua: </span>
                      <span className="font-medium text-green-600">
                        {ratio.buy_price}
                      </span>
                    </div>
                  )}
                  {ratio.stop_loss_price && (
                    <div className=" mb-1">
                      <span className="text-gray-600">Cắt lỗ: </span>
                      <span className="font-medium text-red-600">
                        {ratio.stop_loss_price}
                      </span>
                    </div>
                  )}
                  {ratio.take_profit_price && (
                    <div className=" mb-1">
                      <span className="text-gray-600">Chốt lời: </span>
                      <span className="font-medium text-blue-600">
                        {ratio.take_profit_price}
                      </span>
                    </div>
                  )}
                  {ratio.stop_loss && (
                    <div className=" mb-1">
                      <span className="text-gray-600">Cắt lỗ: </span>
                      <span className="font-medium text-red-600">
                        {ratio.stop_loss}
                      </span>
                    </div>
                  )}
                  {ratio.take_profit && (
                    <div className=" mb-1">
                      <span className="text-gray-600">Chốt lời: </span>
                      <span className="font-medium text-blue-600">
                        {ratio.take_profit}
                      </span>
                    </div>
                  )}
                  {ratio.quality && (
                    <div className=" text-yellow-600">
                      Chất lượng: {ratio.quality}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
    </div>
  );
}
