'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export function WatchList() {
  const [stocks, setStocks] = useState<string[]>([])
  const [newStock, setNewStock] = useState('')

  const addStock = () => {
    if (newStock && !stocks.includes(newStock)) {
      setStocks([...stocks, newStock])
      setNewStock('')
    }
  }

  const removeStock = (stock: string) => {
    setStocks(stocks.filter(s => s !== stock))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Thêm cổ phiếu vào danh sách theo dõi</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2 mb-4">
          <Input
            placeholder="Nhập mã cổ phiếu"
            value={newStock}
            onChange={(e) => setNewStock(e.target.value.toUpperCase())}
            className="max-w-[200px]"
          />
          <Button onClick={addStock}>Thêm</Button>
        </div>
        <div className="grid gap-2">
          {stocks.map((stock) => (
            <div key={stock} className="flex items-center justify-between p-2 border rounded">
              <span>{stock}</span>
              <Button variant="destructive" size="sm" onClick={() => removeStock(stock)}>
                Xóa
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}