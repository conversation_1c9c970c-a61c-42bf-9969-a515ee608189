"use client";

import { useState, useEffect } from "react";
import { StockCard } from "@/components/stock-card";
import { StockTable } from "@/components/stock-table";
import { StockDetail } from "@/components/stock-detail";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Search, Grid3X3, Table2, XIcon } from "lucide-react";

import { StockPrediction } from "@/lib/prediction-data";
import { loadMultipleStockAnalyses } from "@/lib/analysis-loader";
import { useIsMobile } from "@/hooks/use-mobile";

// List of available stock symbols
const AVAILABLE_STOCKS = ["HPG", "TCB", "VNM"];

export function StockAnalysis() {
  const isMobile = useIsMobile();
  const [viewMode, setViewMode] = useState<"grid" | "table">("table");
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("stockList");
  const [openTabs, setOpenTabs] = useState<string[]>([]);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [stocks, setStocks] = useState<StockPrediction[]>([]);
  const [loading, setLoading] = useState(true);

  // Initialize from localStorage and load stock data
  useEffect(() => {
    const savedTabs = JSON.parse(localStorage.getItem("stockTabs") || "[]");
    if (savedTabs.length > 0) {
      setOpenTabs(savedTabs);
    }

    // Load stock analysis data from JSON files
    const loadStockData = async () => {
      setLoading(true);
      try {
        const stockData = await loadMultipleStockAnalyses(AVAILABLE_STOCKS);

        // Convert the object to array format
        const stocksArray = Object.entries(stockData).map(([symbol, data]) => ({
          symbol,
          ...data,
          // Mock/derive fields for new StockCard UI
          expected_profit: data.expected_profit || "+62.97%",
          win_rate: data.win_rate || 50,
          buy_range:
            data.buy_zones && data.buy_zones.length > 0
              ? `${data.buy_zones[0].price}${
                  data.buy_zones[1] ? " - " + data.buy_zones[1].price : ""
                }`
              : "39.00 - 44.85",
          target_price:
            data.take_profit_zones && data.take_profit_zones.length > 0
              ? data.take_profit_zones[0].price
              : "67.96",
          hold_days: data.hold_days || "22 phiên",
          buy_action: data.buy_action || "Mua",
          logo_url: `/assets/logos/${symbol}.svg`,
        }));

        setStocks(stocksArray as StockPrediction[]);

        // Set last updated date from the first stock (assuming all have same updated date)
        if (stocksArray.length > 0 && stocksArray[0].updated_at) {
          setLastUpdated(stocksArray[0].updated_at);
        } else {
          // Fallback to current date if no updated_at is available
          setLastUpdated(new Date().toLocaleDateString("vi-VN"));
        }
      } catch (error) {
        console.error("Error loading stock analysis data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadStockData();
  }, []);

  // Save tabs to localStorage when they change
  useEffect(() => {
    localStorage.setItem("stockTabs", JSON.stringify(openTabs));
  }, [openTabs]);

  // Filter stocks based on search term
  const filteredStocks =
    stocks && Array.isArray(stocks)
      ? stocks.filter((stock) =>
          stock.symbol.toLowerCase().includes(searchTerm.toLowerCase())
        )
      : [];

  // Handle opening a stock tab
  const handleOpenStockTab = (symbol: string) => {
    if (!openTabs.includes(symbol)) {
      setOpenTabs([...openTabs, symbol]);
    }
    setActiveTab(`stock-${symbol}`);
  };

  // Handle closing a stock tab
  const handleCloseTab = (symbol: string) => {
    setOpenTabs(openTabs.filter((tab) => tab !== symbol));
    if (activeTab === `stock-${symbol}`) {
      setActiveTab("stockList");
    }
  };

  // Auto-switch to card view on mobile
  useEffect(() => {
    if (isMobile) {
      setViewMode("grid");
    } else {
      setViewMode("table");
    }
  }, [isMobile]);

  return (
    <div className="px-4 lg:px-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex items-center justify-between mb-4">
          <TabsList className="relative flex flex-wrap gap-2 gap-y-2 w-auto items-start">
            <TabsTrigger
              className="flex-shrink-0 h-8 min-w-[64px] px-3"
              value="stockList"
            >
              Danh sách
            </TabsTrigger>
            {openTabs.map((symbol) => (
              <div key={symbol} className="flex items-center">
                <TabsTrigger
                  className="flex-shrink-0 h-8 min-w-[64px] px-3"
                  value={`stock-${symbol}`}
                >
                  {symbol}
                </TabsTrigger>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCloseTab(symbol);
                  }}
                  className="ml-1 rounded-full hover:bg-muted p-1"
                  aria-label="Đóng"
                  tabIndex={-1}
                >
                  <XIcon className="h-4 w-4" />
                </button>
              </div>
            ))}
          </TabsList>
          <div className="relative">
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Tìm mã cổ phiếu..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-[200px]"
            />
          </div>
        </div>

        <TabsContent value="stockList" className="mt-0">
          {loading ? (
            <div className="flex justify-center items-center h-[300px]">
              <div className="text-center">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Đang tải dữ liệu...</p>
              </div>
            </div>
          ) : filteredStocks.length > 0 ? (
            viewMode === "grid" ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                {filteredStocks.map((stock) => (
                  <StockCard
                    key={stock.symbol}
                    stock={stock}
                    onClick={() => handleOpenStockTab(stock.symbol)}
                  />
                ))}
              </div>
            ) : (
              <StockTable
                stocks={filteredStocks}
                onRowClick={(symbol) => handleOpenStockTab(symbol)}
              />
            )
          ) : (
            <div className="flex justify-center items-center h-[200px]">
              <p className="text-muted-foreground">
                Không tìm thấy cổ phiếu phù hợp
              </p>
            </div>
          )}
        </TabsContent>

        {openTabs.map((symbol) => (
          <TabsContent key={symbol} value={`stock-${symbol}`} className="mt-0">
            <StockDetail
              symbol={symbol}
              stock={stocks.find((s) => s.symbol === symbol)}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
