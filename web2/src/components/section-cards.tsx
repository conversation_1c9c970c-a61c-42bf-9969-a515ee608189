import { TrendingDownIcon, TrendingUpIcon } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { formatPrice, formatSigned, getPriceChangeClass, getTrendIcon } from "@/lib/stock-helpers"

export function SectionCards() {
  return (
    <div className="*:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4 grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card lg:px-6">
      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription>Tổng doanh thu</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {formatPrice(1250000)}
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className={`flex gap-1 rounded-lg text-xs ${getPriceChangeClass(12.5)}`}>
              {getTrendIcon('tăng')}
              {formatSigned(12.5)}%
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Xu hướng tăng trong tháng này {getTrendIcon('tăng')}
          </div>
          <div className="text-muted-foreground">
            Lượt truy cập trong 6 tháng qua
          </div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription>Khách hàng mới</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            1.234
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className={`flex gap-1 rounded-lg text-xs ${getPriceChangeClass(-20)}`}>
              {getTrendIcon('giảm')}
              {formatSigned(-20)}%
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Giảm 20% trong kỳ này {getTrendIcon('giảm')}
          </div>
          <div className="text-muted-foreground">
            Cần chú ý đến việc thu hút khách hàng
          </div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription>Tài khoản hoạt động</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            45.678
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className={`flex gap-1 rounded-lg text-xs ${getPriceChangeClass(12.5)}`}>
              {getTrendIcon('tăng')}
              {formatSigned(12.5)}%
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Giữ chân người dùng tốt {getTrendIcon('tăng')}
          </div>
          <div className="text-muted-foreground">
            Mức độ tương tác vượt chỉ tiêu</div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription>Tỷ lệ tăng trưởng</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            4,5%
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className={`flex gap-1 rounded-lg text-xs ${getPriceChangeClass(4.5)}`}>
              {getTrendIcon('tăng')}
              {formatSigned(4.5)}%
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Hiệu suất ổn định {getTrendIcon('tăng')}
          </div>
          <div className="text-muted-foreground">
            Đạt dự báo tăng trưởng</div>
        </CardFooter>
      </Card>
    </div>
  )
}
