"use client";

import { Card, CardContent } from "@/components/ui/card";
import {
  getTrendIcon,
  getTrendClass,
  getStrengthIcon,
  getConfidenceIcon,
} from "@/lib/stock-helpers";

interface StockCardProps {
  stock: any;
  onClick: () => void;
}

export function StockCard({ stock, onClick }: StockCardProps) {
  const {
    symbol,
    current_price,
    trend_direction,
    trend_strength,
    trend_confidence,
    recommendation,
    ma_signals,
    indicator_signals,
  } = stock;

  // Calculate indicator summary
  const getIndicatorSummary = () => {
    const result = {
      ma_count: ma_signals && Array.isArray(ma_signals) ? ma_signals.length : 0,
      indicator_count:
        indicator_signals && Array.isArray(indicator_signals)
          ? indicator_signals.length
          : 0,
      total_count: 0,
      ma_buy: 0,
      ma_sell: 0,
      ma_neutral: 0,
      tech_buy: 0,
      tech_sell: 0,
      tech_neutral: 0,
      total_buy: 0,
      total_buyer_percent: 0,
      total_sell: 0,
      total_seller_percent: 0,
      total_neutral: 0,
      total_neutral_percent: 0,
    };

    result.total_count = result.ma_count + result.indicator_count;

    // Count MA signals by type
    if (ma_signals && Array.isArray(ma_signals)) {
      result.ma_buy = ma_signals.filter((s) => s.signal === "Mua").length;
      result.ma_sell = ma_signals.filter((s) => s.signal === "Bán").length;
      result.ma_neutral = ma_signals.filter(
        (s) => s.signal === "Trung tính"
      ).length;
    }

    // Count technical indicators by type
    if (indicator_signals && Array.isArray(indicator_signals)) {
      result.tech_buy = indicator_signals.filter(
        (s) => s.signal === "Mua"
      ).length;
      result.tech_sell = indicator_signals.filter(
        (s) => s.signal === "Bán"
      ).length;
      result.tech_neutral = indicator_signals.filter(
        (s) => s.signal === "Trung tính"
      ).length;
    }

    // Calculate totals
    result.total_buy = result.ma_buy + result.tech_buy;
    result.total_buyer_percent = (result.total_buy / result.total_count) * 100;
    result.total_sell = result.ma_sell + result.tech_sell;
    result.total_seller_percent =
      (result.total_sell / result.total_count) * 100;
    result.total_neutral = result.ma_neutral + result.tech_neutral;
    result.total_neutral_percent =
      100 - result.total_buyer_percent - result.total_seller_percent;

    return result;
  };

  // Format text with line breaks
  const formatText = (text: string) => {
    return text.split("\n").map((line, i) => <p key={i}>{line}</p>);
  };

  const summary = getIndicatorSummary();

  return (
    <Card
      className="hover:shadow-md transition-all cursor-pointer"
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <img
              src={`/assets/logos/${symbol}.svg`}
              alt={symbol}
              className="w-10 h-10 mr-3"
            />
            <div className="text-xl font-semibold">{symbol}</div>
          </div>
          <div className="text-xl font-bold">{current_price}</div>
        </div>

        <div className="flex justify-between items-center mb-3 px-2">
          <div
            className="flex items-center gap-1"
            title={`Xu hướng: ${trend_direction}`}
          >
            <span className={`${getTrendClass(trend_direction)}`}>
              {getTrendIcon(trend_direction)}
            </span>
            <span className={` ${getTrendClass(trend_direction)}`}>
              {trend_direction}
            </span>
          </div>
          <div
            className="flex items-center gap-1"
            title={`Độ mạnh: ${trend_strength}`}
          >
            <span className="text-blue-500">
              {getStrengthIcon(trend_strength)}
            </span>
            <span className=" text-blue-500">{trend_strength}</span>
          </div>
          <div
            className="flex items-center gap-1"
            title={`Độ tin cậy: ${trend_confidence}`}
          >
            <span className="text-yellow-500">
              {getConfidenceIcon(trend_confidence)}
            </span>
            <span className=" text-yellow-500">{trend_confidence}</span>
          </div>
        </div>

        {/* Horizon bar: Mua/Bán/Trung tính */}
        {summary.total_count > 0 && (
          <div className="w-full mt-2 mb-2">
            <div className="mb-3">
              <div className="text-sm text-muted-foreground mb-1">Tín hiệu:</div>
            </div>
            <div className="flex h-4 w-full rounded overflow-hidden">
              <div
                className="bg-buy"
                style={{
                  width: `${summary.total_buyer_percent}%`,
                  transition: "width 0.3s",
                }}
                title={`Mua: ${summary.total_buy}`}
              />
              <div
                className="bg-neutral"
                style={{
                  width: `${summary.total_neutral_percent}%`,
                  transition: "width 0.3s",
                }}
                title={`Trung tính: ${summary.total_neutral}`}
              />
              <div
                className="bg-sell"
                style={{
                  width: `${summary.total_seller_percent}%`,
                  transition: "width 0.3s",
                }}
                title={`Bán: ${summary.total_sell}`}
              />
            </div>
            <div className="flex justify-between text-xs mt-1 px-1">
              <span className="text-buy">{summary.total_buy} Mua</span>
              <span className="text-neutral">{summary.total_neutral} TT</span>
              <span className="text-sell">{summary.total_sell} Bán</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
