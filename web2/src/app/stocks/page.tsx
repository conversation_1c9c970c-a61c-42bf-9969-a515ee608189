'use client';
import { useState } from "react";
import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { StockAnalysis } from "@/components/stock-analysis"
import { Skeleton } from "@/components/ui/skeleton"

export default function StocksPage() {
  return (
    <SidebarProvider>
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <StockAnalysis />
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
