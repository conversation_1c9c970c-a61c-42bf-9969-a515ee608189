"use client";
import { AppSidebar } from "@/components/app-sidebar";
import { SiteHeader } from "@/components/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { WatchList } from "@/components/watch-list";

export default function WatchlistPage() {
  return (
    <SidebarProvider>
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <h1 className="font-bold"><PERSON>h sách theo dõi</h1>
              <WatchList />
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
