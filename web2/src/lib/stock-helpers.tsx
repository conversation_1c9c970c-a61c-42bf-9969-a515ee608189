import { TrendingUp, TrendingDown, Minus } from "lucide-react"

// Helper function to get trend icon based on direction
export function getTrendIcon(direction: string) {
  if (!direction) return <Minus className="h-4 w-4" />
  
  if (direction.toLowerCase().includes('tăng')) {
    return <TrendingUp className="h-4 w-4" />
  }
  
  if (direction.toLowerCase().includes('giảm')) {
    return <TrendingDown className="h-4 w-4" />
  }
  
  return <Minus className="h-4 w-4" />
}

// Helper function to get trend class based on direction
export function getTrendClass(direction: string) {
  if (!direction) return ""
  
  if (direction.toLowerCase().includes('tăng')) {
    return "text-emerald-500"
  }
  
  if (direction.toLowerCase().includes('giảm')) {
    return "text-red-500"
  }
  
  return ""
}

// Helper function to get strength icon based on strength
export function getStrengthIcon(strength: string) {
  if (!strength) return "📊"
  
  if (strength.toLowerCase() === 'mạnh') {
    return "📈"
  }
  
  if (strength.toLowerCase() === 'trung bình') {
    return "📊"
  }
  
  return "📉"
}

// Helper function to get confidence icon based on confidence
export function getConfidenceIcon(confidence: string) {
  if (!confidence) return "❓"
  
  const value = parseFloat(confidence.replace('%', ''))
  
  if (isNaN(value)) return "❓"
  
  if (value > 70) return "✅"
  
  if (value > 50) return "✓"
  
  return "❓"
}

// Helper function to format price
export function formatPrice(price: number | string) {
  if (!price) return '0 ₫'
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0
  }).format(typeof price === 'string' ? parseFloat(price) : price)
}

// Helper function to format number with sign
export function formatSigned(value: number | string) {
  if (typeof value === 'string') value = parseFloat(value)
  if (isNaN(value as number)) return value
  return (value as number) > 0 ? `+${value}` : value
}

// Helper function to get price change class
export function getPriceChangeClass(change: number | string) {
  if (typeof change === 'string') change = parseFloat(change)
  if (isNaN(change as number)) return 'text-gray-700'
  if ((change as number) > 0) return 'text-green-600'
  if ((change as number) < 0) return 'text-red-600'
  return 'text-gray-700'
}

// Helper function to get price change background
export function getPriceChangeBg(change: number | string) {
  if (typeof change === 'string') change = parseFloat(change)
  if (isNaN(change as number)) return 'bg-gray-100'
  if ((change as number) > 0) return 'bg-green-50'
  if ((change as number) < 0) return 'bg-red-50'
  return 'bg-gray-100'
}
