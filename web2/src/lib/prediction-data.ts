// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho dự đoán cổ phiếu
export interface StockPrediction {
  symbol: string
  current_price: string
  trend_change: number
  trend_change_percent: number
  trend_direction: string
  trend_strength: string
  trend_confidence: string
  market_condition: string
  buy_zones: Array<{
    price: string
    confidence: string
    reason: string
  }>
  stop_loss_zones: Array<{
    price: string
    confidence: string
    reason: string
  }>
  take_profit_zones: Array<{
    price: string
    confidence: string
    reason: string
  }>
  risk_reward_ratios: Array<{
    buy_price: string
    stop_loss_price: string
    take_profit_price: string
    ratio: string
    quality: string
  }>
  recommendation: string
  analysis_date: string
  last_trading_date: string
  technical_summary: string
  ma_signals: Array<{
    period: number
    type: 'SMA' | 'EMA'
    value: string
    signal: 'Mua' | 'Bán' | 'Trung tính'
  }>
  indicator_signals: Array<{
    name: string
    value: number
    signal: 'Mua' | 'Bán' | 'Trung tính'
    action: string
    description: string
    info: string
  }>
}

// Ngày dự đoán
export const PREDICTION_DATE = '2025-05-13'
