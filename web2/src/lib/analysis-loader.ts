/**
 * Utility functions for loading stock analysis data from JSON files
 */

/**
 * Load analysis data for a specific stock symbol
 * @param symbol - Stock symbol to load data for
 * @returns Promise containing the analysis data
 */
export async function loadStockAnalysis(symbol: string): Promise<any> {
  try {
    // Construct the URL to the JSON file in the public folder
    const response = await fetch(`/analysis/${symbol}.json`, {
      // Add cache control to prevent stale data
      cache: 'no-store'
    });
    
    if (!response.ok) {
      throw new Error(`Failed to load analysis data for ${symbol}: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error loading analysis for ${symbol}:`, error);
    return null;
  }
}

/**
 * Load analysis data for multiple stock symbols
 * @param symbols - Array of stock symbols to load data for
 * @returns Promise containing an object with stock symbols as keys and analysis data as values
 */
export async function loadMultipleStockAnalyses(symbols: string[]): Promise<Record<string, any>> {
  const analysisMap: Record<string, any> = {};
  
  await Promise.all(
    symbols.map(async (symbol) => {
      const data = await loadStockAnalysis(symbol);
      if (data) {
        analysisMap[symbol] = data;
      }
    })
  );
  
  return analysisMap;
} 