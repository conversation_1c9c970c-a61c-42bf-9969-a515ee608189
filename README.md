# StockPal - Intelligent Stock Trading Analysis System

## Overview
StockPal is an intelligent stock trading analysis system that combines multiple technical indicators with adaptive weighting to provide accurate market trend predictions and trading signals. The system adapts to different market conditions and provides comprehensive analysis for informed trading decisions, leveraging advanced technical analysis techniques and machine learning capabilities to deliver reliable trading insights.

## Project Goals
- Provide traders with an intelligent and adaptive analysis tool for reliable market trend predictions and trading signals
- Achieve minimum 65% accuracy in trend prediction with support/resistance levels having less than 2% margin of error
- Generate favorable risk-reward ratios (target average ratio >1.5) that enable profitable trading strategies
- Support multi-timeframe analysis for comprehensive market insights
- Successfully detect market conditions (trending vs. ranging) with high accuracy (>80%)
- Create a system that dynamically adjusts indicator weights based on current market conditions

## Implementation Status

### Current Features
- ✅ Core technical indicator integration
- ✅ Adaptive weighting system
- ✅ Basic market condition detection
- ✅ Trend analysis with multi-timeframe support
- ✅ Trading signal generation with risk management
- ✅ Python-based implementation with key dependencies

### In Development
- 🔄 Advanced price analysis features
- 🔄 Enhanced risk management system
- 🔄 Performance validation tools
- 🔄 Machine learning integration
- 🔄 Market context analysis

## Project Structure
StockPal is organized into five main epics:

1. **Core Infrastructure Setup**: Foundational data management and technical analysis framework
2. **Technical Indicator Implementation**: Standard technical indicators with signal generation capabilities
3. **Adaptive Analysis System**: Market condition detection and dynamic indicator weighting
4. **Signal Synthesis and Recommendation**: Trading signal generation with entry, stop-loss, and take-profit zones
5. **Validation and Backtesting**: System performance validation and parameter optimization

## System Architecture

### Core Components

1. **Technical Indicators Integration**
   - ADX (Average Directional Index) for trend strength measurement
   - Moving Average systems (9, 20, 50 periods) for trend confirmation
   - MACD (Moving Average Convergence Divergence) for momentum analysis
   - OBV (On-Balance Volume) for volume trend confirmation
   - RSI (Relative Strength Index) for overbought/oversold conditions
   - Bollinger Bands for volatility-based targets
   - CCI (Commodity Channel Index) for trend identification
   - Stochastic Oscillator for momentum confirmation

2. **Adaptive Weighting System**
   - Dynamic adjustment of indicator weights based on market conditions
   - Trending Market Weights:
     * ADX and MA Crossovers: 2.0x weight
     * MACD and OBV: 1.5x weight
     * Oscillators (RSI, Stochastic, CCI): 0.8x weight
   - Ranging Market Weights:
     * RSI and Stochastic: 2.0x weight
     * CCI: 1.5x weight
     * Trend indicators: 0.8x weight

3. **Market Condition Detection**
   - Uses ADX to classify market as trending (ADX ≥ 25) or ranging
   - Automatically adjusts analysis strategy based on market condition
   - Provides real-time market state monitoring

### Analysis Features

1. **Trend Analysis**
   - Direction determination (bullish/bearish/neutral)
   - Trend strength qualification
   - Confidence score calculation
   - Multi-timeframe trend confirmation
   - Fibonacci retracement levels (30-period lookback)
     * Standard ratios: 0.236, 0.382, 0.5, 0.618
     * Support/resistance level identification
     * Trend reversal zone detection

2. **Price Target Generation**
   - Support and resistance level identification
   - Volatility-based target calculation using Bollinger Bands
   - Multiple moving average confluence points
   - Weighted confidence levels for each target

3. **Trading Signal Generation**
   - Combined indicator confidence scoring
   - Entry price recommendations
   - Target price levels
   - Stop-loss price calculation
   - Risk-reward ratio analysis

4. **Divergence Detection**
   - Price-RSI divergence analysis
   - Price-MACD divergence analysis
   - Early trend reversal identification
   - Confirmation of potential trend changes

## Usage

### New Clean Architecture (Recommended)

1. **Command Line Usage**
   ```bash
   cd server
   python refactored_main.py
   ```

2. **Python API Usage**
   ```python
   from server.refactored_main import StockPalApplication

   # Initialize application
   app = StockPalApplication()

   # Analyze single stock
   analysis = app.analyze_stock("VIC", days_back=90)
   print(f"Recommendation: {analysis['recommendation']}")

   # Batch analysis
   results = app.run_batch_analysis(symbols=["VIC", "HPG", "TCB"])
   print(f"Analyzed: {results['successful_analyses']} stocks")

   # ML prediction
   prediction = app.get_ml_prediction("VIC", horizon_days=5)
   print(f"Predicted price: {prediction['predicted_price']:.2f}")

   # Backtesting
   backtest = app.run_backtest("VIC", {"stop_loss": 0.05, "take_profit": 0.12})
   print(f"Return: {backtest['total_return_percent']:.2f}%")
   ```

3. **Batch Analysis with Export**
   ```python
   # Run analysis and export to web directory
   results = app.run_batch_analysis(test_mode=True, export_results=True)
   print(f'Success rate: {results["statistics"]["success_rate"]:.1f}%')
   ```

### Legacy Compatibility (Deprecated)

For backward compatibility, legacy functions are available but deprecated:

```python
from server.legacy_compatibility import analyze_stock, main

# Single stock analysis (deprecated)
result = analyze_stock("VIC")

# Batch analysis (deprecated)
main(analysis_symbols=["VIC", "HPG"])
```

**Note**: Legacy functions will show deprecation warnings and will be removed in future versions.

## Roadmap and Future Enhancements

### Phase 1: Advanced Analysis (Q1 2024)
1. **Advanced Price Analysis**
   - Volume profile analysis for key price levels
   - Supply and demand zone identification
   - Market structure break detection

   Priority: High
   Status: Planning

### Phase 2: Risk and Performance (Q2 2024)
2. **Enhanced Risk Management**
   - Volatility-based position sizing
   - Dynamic risk adjustment
   - Correlation-based portfolio management
   - Maximum drawdown controls

   Priority: Critical
   Status: Design Phase

3. **Performance Validation Suite**
   - Backtesting engine with historical data
   - Performance metrics dashboard
   - Strategy optimization tools
   - Risk-adjusted return analysis

   Priority: High
   Status: Research

### Phase 3: AI Integration (Q3-Q4 2024)
4. **Machine Learning Integration**
   - Pattern recognition with deep learning
   - Real-time anomaly detection
   - Predictive analytics engine
   - Sentiment analysis integration

   Priority: Medium
   Status: Research

5. **Market Context Analysis**
   - Advanced market regime detection
   - Cross-asset correlation analysis
   - Real-time sector rotation tracking
   - Comprehensive market breadth indicators

   Priority: Medium
   Status: Planning

## Technical Requirements

1. **Data Requirements**
   - Historical price data with OHLCV (Open, High, Low, Close, Volume)
   - Minimum data points: 50 periods for basic analysis
   - Recommended data points: 200+ periods for full feature utilization

2. **Performance Considerations**
   - Response times under 2 seconds for standard analysis operations
   - Efficient memory management for large datasets
   - Optimized indicator calculations
   - Thread-safe implementations for parallel data fetching

3. **Integration Requirements**
   - Support for multiple data providers (SSI, Vietstock, Cafef)
   - Web data visualization compatibility
   - Excel export functionality for analysis results

## Dependencies
- Python 3.7+
- NumPy for numerical computations
- Pandas for data manipulation
- SQLite for local storage of historical data
- TA-Lib for technical analysis (optional)

## Documentation
- `docs/prd.md` - Product Requirements Document
- `docs/epic1.md` - Core Infrastructure Setup
- `docs/epic2.md` - Technical Indicator Implementation
- `docs/epic3.md` - Adaptive Analysis System
- `docs/epic4.md` - Signal Synthesis and Recommendation
- `docs/epic5.md` - Validation and Backtesting

## Notes for AI Agents
- The system uses adaptive weights stored in `_indicator_weights` dictionary
- Market condition detection is primarily based on ADX values
- Divergence detection requires minimum 14 periods of data
- All indicators are initialized in the TrendPredictor constructor
- Risk percentage is configurable through the constructor

